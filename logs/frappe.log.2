2025-04-22 15:53:17,649 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:17,851 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:17,853 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,061 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,065 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,268 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,271 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,468 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,470 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,703 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 15:53:18,714 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1371, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1212, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1177, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1207, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1148, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'crm'
2025-04-22 16:08:48,478 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'CRM Lead', 'name': 'CRM-LEAD-2025-00002', 'fieldname': 'email', 'value': '<EMAIL>', 'cmd': 'frappe.client.set_value'}
2025-04-22 16:09:47,054 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'recipients': '<EMAIL>', 'attachments': [], 'cc': '', 'bcc': '', 'subject': 'Dr Juma (#CRM-LEAD-2025-00002)', 'content': '<p>welcome to our app</p>', 'doctype': 'CRM Lead', 'name': 'CRM-LEAD-2025-00002', 'send_email': 1, 'sender': '<EMAIL>', 'sender_full_name': 'Emanuel Fidelis', 'cmd': 'frappe.core.doctype.communication.email.make'}
2025-04-22 16:11:31,771 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"docstatus":0,"doctype":"Email Account","name":"new-email-account-lsopzbzycv","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":"","use_starttls":"","email_sync_option":"UNSEEN","initial_sync_count":"250","imap_folder":[{"docstatus":0,"doctype":"IMAP Folder","name":"new-imap-folder-ukjfrgaxdg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-email-account-lsopzbzycv","parentfield":"imap_folder","parenttype":"Email Account","idx":1,"folder_name":"INBOX"}],"append_emails_to_sent_folder":0,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":"","no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"attachment_limit":"","email_id":"<EMAIL>","email_account_name":"Developer","domain":null,"password":null,"login_id":null,"email_server":"imap.gmail.com","incoming_port":993,"smtp_server":"smtp.gmail.com","smtp_port":587,"sent_folder_name":""}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:11:46,087 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"docstatus":0,"doctype":"Email Account","name":"new-email-account-lsopzbzycv","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":"","use_starttls":"","email_sync_option":"UNSEEN","initial_sync_count":"250","imap_folder":[{"docstatus":0,"doctype":"IMAP Folder","name":"new-imap-folder-ukjfrgaxdg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-email-account-lsopzbzycv","parentfield":"imap_folder","parenttype":"Email Account","idx":1,"folder_name":"INBOX"}],"append_emails_to_sent_folder":0,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":"","no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"attachment_limit":"","email_id":"<EMAIL>","email_account_name":"Developer","domain":null,"password":"aakvatech","login_id":null,"email_server":"imap.gmail.com","incoming_port":993,"smtp_server":"smtp.gmail.com","smtp_port":587,"sent_folder_name":""}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:12:12,574 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"docstatus":0,"doctype":"Email Account","name":"new-email-account-lsopzbzycv","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":"","use_starttls":"","email_sync_option":"UNSEEN","initial_sync_count":"250","imap_folder":[{"docstatus":0,"doctype":"IMAP Folder","name":"new-imap-folder-ukjfrgaxdg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-email-account-lsopzbzycv","parentfield":"imap_folder","parenttype":"Email Account","idx":1,"folder_name":"INBOX"}],"append_emails_to_sent_folder":0,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":"","no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"attachment_limit":"","email_id":"<EMAIL>","email_account_name":"Developer","domain":null,"password":"Fidelis@1228","login_id":null,"email_server":"imap.gmail.com","incoming_port":993,"smtp_server":"smtp.gmail.com","smtp_port":587,"sent_folder_name":""}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:17:20,718 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"docstatus":0,"doctype":"Email Account","name":"new-email-account-lsopzbzycv","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":"","use_starttls":"","email_sync_option":"UNSEEN","initial_sync_count":"250","imap_folder":[{"docstatus":0,"doctype":"IMAP Folder","name":"new-imap-folder-ukjfrgaxdg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-email-account-lsopzbzycv","parentfield":"imap_folder","parenttype":"Email Account","idx":1,"folder_name":"INBOX"}],"append_emails_to_sent_folder":0,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":"","no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"attachment_limit":"","email_id":"<EMAIL>","email_account_name":"Developer","domain":null,"password":"Fidelis@1228","login_id":null,"email_server":"imap.gmail.com","incoming_port":993,"smtp_server":"smtp.gmail.com","smtp_port":587,"sent_folder_name":""}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:17:41,195 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"docstatus":0,"doctype":"Email Account","name":"new-email-account-lsopzbzycv","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":"","use_starttls":"","email_sync_option":"UNSEEN","initial_sync_count":"250","imap_folder":[{"docstatus":0,"doctype":"IMAP Folder","name":"new-imap-folder-ukjfrgaxdg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-email-account-lsopzbzycv","parentfield":"imap_folder","parenttype":"Email Account","idx":1,"folder_name":"INBOX"}],"append_emails_to_sent_folder":0,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":"","no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"attachment_limit":"","email_id":"<EMAIL>","email_account_name":"Developer","domain":null,"password":"Fidelis@2812","login_id":null,"email_server":"imap.gmail.com","incoming_port":993,"smtp_server":"smtp.gmail.com","smtp_port":587,"sent_folder_name":""}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:18:59,227 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"name":"Developer","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:18:03.577489","modified_by":"<EMAIL>","docstatus":0,"idx":0,"email_id":"<EMAIL>","email_account_name":"Developer","enable_incoming":0,"enable_outgoing":1,"service":"GMail","domain":null,"frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"password":"************","awaiting_password":0,"ascii_encode_password":0,"api_key":null,"api_secret":null,"connected_app":null,"connected_user":null,"login_id_is_different":0,"login_id":null,"default_incoming":0,"attachment_limit":0,"last_synced_at":null,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":0,"use_starttls":0,"email_server":"imap.gmail.com","incoming_port":993,"email_sync_option":"UNSEEN","initial_sync_count":"250","append_emails_to_sent_folder":0,"sent_folder_name":"","append_to":null,"create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"send_notification_to":null,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":0,"smtp_server":"smtp.gmail.com","smtp_port":587,"no_smtp_authentication":0,"always_bcc":null,"add_signature":0,"signature":null,"enable_auto_reply":0,"auto_reply_message":null,"footer":null,"brand_logo":null,"uidvalidity":null,"uidnext":0,"no_failed":0,"doctype":"Email Account","imap_folder":[{"name":"pjjnbf83el","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:18:03.577489","modified_by":"<EMAIL>","docstatus":0,"idx":1,"folder_name":"INBOX","append_to":null,"uidvalidity":null,"uidnext":null,"parent":"Developer","parentfield":"imap_folder","parenttype":"Email Account","doctype":"IMAP Folder","__unsaved":1}],"__last_sync_on":"2025-04-22T13:18:04.277Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:23:42,126 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"name":"Developer","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:22:55.719022","modified_by":"<EMAIL>","docstatus":0,"idx":0,"email_id":"<EMAIL>","email_account_name":"Developer","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"attachment_limit":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":0,"use_starttls":0,"email_server":"imap.gmail.com","incoming_port":993,"email_sync_option":"UNSEEN","initial_sync_count":"250","append_emails_to_sent_folder":0,"sent_folder_name":"","create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":0,"smtp_server":"smtp.gmail.com","smtp_port":587,"no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"uidnext":0,"no_failed":0,"doctype":"Email Account","imap_folder":[{"name":"pjjnbf83el","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:22:55.719022","modified_by":"<EMAIL>","docstatus":0,"idx":1,"folder_name":"INBOX","parent":"Developer","parentfield":"imap_folder","parenttype":"Email Account","doctype":"IMAP Folder","__unsaved":1}],"__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:23:52,576 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"name":"Developer","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:22:55.719022","modified_by":"<EMAIL>","docstatus":0,"idx":0,"email_id":"<EMAIL>","email_account_name":"Developer","enable_incoming":1,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":1,"login_id_is_different":0,"default_incoming":0,"attachment_limit":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":0,"use_starttls":0,"email_server":"imap.gmail.com","incoming_port":993,"email_sync_option":"UNSEEN","initial_sync_count":"250","append_emails_to_sent_folder":0,"sent_folder_name":"","create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":0,"smtp_server":"smtp.gmail.com","smtp_port":587,"no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"uidnext":0,"no_failed":0,"doctype":"Email Account","imap_folder":[{"name":"pjjnbf83el","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:22:55.719022","modified_by":"<EMAIL>","docstatus":0,"idx":1,"folder_name":"INBOX","parent":"Developer","parentfield":"imap_folder","parenttype":"Email Account","doctype":"IMAP Folder","__unsaved":1}],"__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:29:46,338 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"name":"Developer","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:24:17.058559","modified_by":"<EMAIL>","docstatus":0,"idx":0,"email_id":"<EMAIL>","email_account_name":"Developer","enable_incoming":0,"enable_outgoing":1,"service":"GMail","frappe_mail_site":"https://frappemail.com","auth_method":"Basic","backend_app_flow":0,"awaiting_password":0,"ascii_encode_password":0,"login_id_is_different":0,"default_incoming":0,"attachment_limit":0,"use_imap":1,"use_ssl":1,"validate_ssl_certificate":0,"use_starttls":0,"email_server":"imap.gmail.com","incoming_port":993,"email_sync_option":"UNSEEN","initial_sync_count":"250","append_emails_to_sent_folder":0,"sent_folder_name":"","create_contact":1,"enable_automatic_linking":0,"notify_if_unreplied":0,"unreplied_for_mins":30,"default_outgoing":0,"always_use_account_email_id_as_sender":1,"always_use_account_name_as_sender_name":0,"send_unsubscribe_message":1,"track_email_status":1,"use_tls":1,"use_ssl_for_outgoing":0,"smtp_server":"smtp.gmail.com","smtp_port":587,"no_smtp_authentication":0,"add_signature":0,"enable_auto_reply":0,"uidnext":0,"no_failed":0,"doctype":"Email Account","imap_folder":[{"name":"pjjnbf83el","owner":"<EMAIL>","creation":"2025-04-22 16:18:03.577489","modified":"2025-04-22 16:24:17.058559","modified_by":"<EMAIL>","docstatus":0,"idx":1,"folder_name":"INBOX","parent":"Developer","parentfield":"imap_folder","parenttype":"Email Account","doctype":"IMAP Folder","__unsaved":1}],"password":"************","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 16:39:32,226 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'cmd': 'crm.api.get_posthog_settings'}
2025-04-22 17:04:06,792 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'medium': 'Twilio', 'cmd': 'crm.integrations.api.set_default_calling_medium'}
2025-04-22 17:04:10,868 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'CRM Twilio Settings', 'name': 'CRM Twilio Settings', 'fieldname': {'owner': 'Administrator', 'creation': None, 'modified': '2025-04-22 15:50:49.434609', 'modified_by': 'Administrator', 'docstatus': 0, 'idx': '0', 'enabled': True, 'record_calls': True, 'account_sid': '**********************************', 'auth_token': '58ab5d13ce871a22e2c63fd5838b095e', 'api_key': None, 'twiml_sid': None, 'api_secret': None}, 'cmd': 'frappe.client.set_value'}
2025-04-22 17:12:04,470 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'medium': 'Twilio', 'cmd': 'crm.integrations.api.set_default_calling_medium'}
2025-04-22 17:12:08,156 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'CRM Twilio Settings', 'name': 'CRM Twilio Settings', 'fieldname': {'owner': 'Administrator', 'creation': None, 'modified': '2025-04-22 15:50:49.434609', 'modified_by': 'Administrator', 'docstatus': 0, 'idx': '0', 'enabled': True, 'record_calls': True, 'account_sid': '**********************************', 'auth_token': '91165257eb8f8ec3f40e6bdb294ed254', 'api_key': None, 'twiml_sid': None, 'api_secret': None}, 'cmd': 'frappe.client.set_value'}
2025-04-22 17:12:14,781 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'medium': 'Twilio', 'cmd': 'crm.integrations.api.set_default_calling_medium'}
2025-04-22 17:12:18,073 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'CRM Twilio Settings', 'name': 'CRM Twilio Settings', 'fieldname': {'owner': 'Administrator', 'creation': None, 'modified': '2025-04-22 15:50:49.434609', 'modified_by': 'Administrator', 'docstatus': 0, 'idx': '0', 'enabled': True, 'record_calls': True, 'account_sid': '**********************************', 'auth_token': '91165257eb8f8ec3f40e6bdb294ed254', 'api_key': None, 'twiml_sid': None, 'api_secret': None}, 'cmd': 'frappe.client.set_value'}
2025-04-25 12:33:31,317 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/error.py", line 87, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/error.py", line 62, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/model/utils/__init__.py", line 217, in wrapper
    return func(*args, **kw)
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/model/document.py", line 105, in get_doc
    return get_doc_from_dict(kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/model/document.py", line 139, in get_doc_from_dict
    controller = get_controller(data["doctype"])
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/model/base_document.py", line 83, in get_controller
    site_controllers[doctype] = import_controller(doctype)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/model/base_document.py", line 103, in import_controller
    module = load_doctype_module(doctype, module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-04-25 12:33:54,070 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:54,076 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:57,275 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:57,283 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:58,452 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
