2025-04-22 15:34:33,193 WARNING database DDL Query made to DB:
create table `tabPayment Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`payment_type` varchar(140),
`payment_order_status` varchar(140),
`posting_date` date,
`company` varchar(140),
`mode_of_payment` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`party_name` varchar(140),
`book_advance_payments_in_separate_party_account` tinyint NOT NULL DEFAULT 0,
`reconcile_on_advance_payment_date` tinyint NOT NULL DEFAULT 0,
`advance_reconciliation_takes_effect_on` varchar(140) DEFAULT 'Oldest Of Invoice Or Advance',
`bank_account` varchar(140),
`party_bank_account` varchar(140),
`contact_person` varchar(140),
`contact_email` varchar(140),
`paid_from` varchar(140),
`paid_from_account_type` varchar(140),
`paid_from_account_currency` varchar(140),
`paid_to` varchar(140),
`paid_to_account_type` varchar(140),
`paid_to_account_currency` varchar(140),
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`source_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_paid_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`received_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`received_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`target_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_received_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_received_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`unallocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`purchase_taxes_and_charges_template` varchar(140),
`sales_taxes_and_charges_template` varchar(140),
`apply_tax_withholding_amount` tinyint NOT NULL DEFAULT 0,
`tax_withholding_category` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`reference_no` varchar(140),
`reference_date` date,
`clearance_date` date,
`project` varchar(140),
`cost_center` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`custom_remarks` tinyint NOT NULL DEFAULT 0,
`remarks` text,
`base_in_words` text,
`is_opening` varchar(140) DEFAULT 'No',
`letter_head` varchar(140),
`print_heading` varchar(140),
`bank` varchar(140),
`bank_account_no` varchar(140),
`payment_order` varchar(140),
`in_words` text,
`auto_repeat` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `party_type`(`party_type`),
index `reference_date`(`reference_date`),
index `is_opening`(`is_opening`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,318 WARNING database DDL Query made to DB:
create table `tabAccount` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disabled` tinyint NOT NULL DEFAULT 0,
`account_name` varchar(140),
`account_number` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`root_type` varchar(140),
`report_type` varchar(140),
`account_currency` varchar(140),
`parent_account` varchar(140),
`account_type` varchar(140),
`tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`freeze_account` varchar(140),
`balance_must_be` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`include_in_gross` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent_account`(`parent_account`),
index `account_type`(`account_type`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:33,456 WARNING database DDL Query made to DB:
create table `tabPayment Entry Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`reference_name` varchar(140),
`due_date` date,
`bill_no` varchar(140),
`payment_term` varchar(140),
`payment_term_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_type` varchar(140),
`payment_type` varchar(140),
`reconcile_effect_on` date,
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0,
`account` varchar(140),
`payment_request` varchar(140),
index `reference_doctype`(`reference_doctype`),
index `reference_name`(`reference_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,565 WARNING database DDL Query made to DB:
create table `tabPayment Terms Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template_name` varchar(140) UNIQUE,
`allocate_payment_based_on_payment_terms` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,627 WARNING database DDL Query made to DB:
create table `tabPOS Invoice Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`pos_invoice` varchar(140),
`posting_date` date,
`customer` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_return` tinyint NOT NULL DEFAULT 0,
`return_against` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,686 WARNING database DDL Query made to DB:
create table `tabPurchase Taxes and Charges Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`is_default` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`tax_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:33,758 WARNING database DDL Query made to DB:
create table `tabRepost Payment Ledger Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`voucher_type` varchar(140),
`voucher_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,026 WARNING database DDL Query made to DB:
create table `tabPOS Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`tax_id` varchar(140),
`pos_profile` varchar(140),
`consolidated_invoice` varchar(140),
`is_pos` tinyint NOT NULL DEFAULT 1,
`is_return` tinyint NOT NULL DEFAULT 0,
`update_billed_amount_in_sales_order` tinyint NOT NULL DEFAULT 0,
`update_billed_amount_in_delivery_note` tinyint NOT NULL DEFAULT 1,
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` tinyint NOT NULL DEFAULT 0,
`due_date` date,
`amended_from` varchar(140),
`return_against` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`po_no` varchar(140),
`po_date` date,
`customer_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`territory` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` longtext,
`company_address` varchar(140),
`company_address_display` longtext,
`company_contact_person` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`set_warehouse` varchar(140),
`update_stock` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`tax_category` varchar(140),
`other_charges_calculation` longtext,
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`loyalty_points` int NOT NULL DEFAULT 0,
`loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`redeem_loyalty_points` tinyint NOT NULL DEFAULT 0,
`loyalty_program` varchar(140),
`loyalty_redemption_account` varchar(140),
`loyalty_redemption_cost_center` varchar(140),
`coupon_code` varchar(140),
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` varchar(140),
`total_advance` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocate_advances_automatically` tinyint NOT NULL DEFAULT 0,
`payment_terms_template` varchar(140),
`cash_bank_account` varchar(140),
`base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_change_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`change_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_for_change_amount` varchar(140),
`write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`write_off_outstanding_amount_automatically` tinyint NOT NULL DEFAULT 0,
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`language` varchar(140),
`select_print_heading` varchar(140),
`inter_company_invoice_reference` varchar(140),
`customer_group` varchar(140),
`is_discounted` tinyint NOT NULL DEFAULT 0,
`utm_source` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`debit_to` varchar(140),
`party_account_currency` varchar(140),
`is_opening` varchar(140) DEFAULT 'No',
`remarks` text,
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`from_date` date,
`to_date` date,
`auto_repeat` varchar(140),
`against_income_account` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `debit_to`(`debit_to`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,212 WARNING database DDL Query made to DB:
create table `tabTax Withholding Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_date` date,
`to_date` date,
`tax_withholding_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`single_threshold` decimal(21,9) NOT NULL DEFAULT 0.0,
`cumulative_threshold` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,328 WARNING database DDL Query made to DB:
create table `tabMode of Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`mode_of_payment` varchar(140) UNIQUE,
`enabled` tinyint NOT NULL DEFAULT 1,
`type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,382 WARNING database DDL Query made to DB:
create table `tabPOS Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,431 WARNING database DDL Query made to DB:
create table `tabLoyalty Program Collection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`tier_name` varchar(140),
`min_spent` decimal(21,9) NOT NULL DEFAULT 0.0,
`collection_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,481 WARNING database DDL Query made to DB:
create table `tabPSOA Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cost_center_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,534 WARNING database DDL Query made to DB:
create table `tabPayment Entry Deduction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`cost_center` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_exchange_gain_loss` tinyint NOT NULL DEFAULT 0,
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,602 WARNING database DDL Query made to DB:
create table `tabPeriod Closing Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`transaction_date` date,
`company` varchar(140),
`fiscal_year` varchar(140),
`period_start_date` date,
`period_end_date` date,
`amended_from` varchar(140),
`closing_account_head` varchar(140),
`gle_processing_status` varchar(140),
`remarks` text,
`error_message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,659 WARNING database DDL Query made to DB:
create table `tabAllowed Dimension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`accounting_dimension` varchar(140),
`dimension_value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,712 WARNING database DDL Query made to DB:
create table `tabTransaction Deletion Record Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`doctype_name` varchar(140),
`docfield_name` varchar(140),
`no_of_docs` int NOT NULL DEFAULT 0,
`done` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,788 WARNING database DDL Query made to DB:
create table `tabSouth Africa VAT Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,887 WARNING database DDL Query made to DB:
create table `tabCoupon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`coupon_name` varchar(140) UNIQUE,
`coupon_type` varchar(140),
`customer` varchar(140),
`coupon_code` varchar(140) UNIQUE,
`from_external_ecomm_platform` tinyint NOT NULL DEFAULT 0,
`pricing_rule` varchar(140),
`valid_from` date,
`valid_upto` date,
`maximum_use` int NOT NULL DEFAULT 0,
`used` int NOT NULL DEFAULT 0,
`description` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:34,972 WARNING database DDL Query made to DB:
create table `tabPOS Closing Entry Taxes` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account_head` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,034 WARNING database DDL Query made to DB:
create table `tabCurrency Exchange Settings Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,109 WARNING database DDL Query made to DB:
create table `tabRepost Payment Ledger` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`posting_date` date,
`voucher_type` varchar(140),
`add_manually` tinyint NOT NULL DEFAULT 0,
`repost_status` varchar(140),
`repost_error_log` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,196 WARNING database DDL Query made to DB:
create table `tabPromotional Scheme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`apply_on` varchar(140) DEFAULT 'Item Code',
`disable` tinyint NOT NULL DEFAULT 0,
`mixed_conditions` tinyint NOT NULL DEFAULT 0,
`is_cumulative` tinyint NOT NULL DEFAULT 0,
`apply_rule_on_other` varchar(140),
`other_item_code` varchar(140),
`other_item_group` varchar(140),
`other_brand` varchar(140),
`selling` tinyint NOT NULL DEFAULT 0,
`buying` tinyint NOT NULL DEFAULT 0,
`applicable_for` varchar(140),
`valid_from` date,
`valid_upto` date,
`company` varchar(140),
`currency` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,274 WARNING database DDL Query made to DB:
create table `tabAdvance Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`add_deduct_tax` varchar(140),
`charge_type` varchar(140),
`row_id` varchar(140),
`account_head` varchar(140),
`description` text,
`included_in_paid_amount` tinyint NOT NULL DEFAULT 0,
`set_by_item_tax_template` tinyint NOT NULL DEFAULT 0,
`cost_center` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
index `account_head`(`account_head`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,338 WARNING database DDL Query made to DB:
create table `tabLedger Merge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`root_type` varchar(140),
`account` varchar(140),
`account_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,538 WARNING database DDL Query made to DB:
create table `tabSales Invoice Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`barcode` varchar(140),
`has_item_scanned` tinyint NOT NULL DEFAULT 0,
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`grant_commission` tinyint NOT NULL DEFAULT 0,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`delivered_by_supplier` tinyint NOT NULL DEFAULT 0,
`income_account` varchar(140),
`is_fixed_asset` tinyint NOT NULL DEFAULT 0,
`asset` varchar(140),
`finance_book` varchar(140),
`expense_account` varchar(140),
`discount_account` varchar(140),
`deferred_revenue_account` varchar(140),
`service_stop_date` date,
`enable_deferred_revenue` tinyint NOT NULL DEFAULT 0,
`service_start_date` date,
`service_end_date` date,
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0,
`incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_rate` text,
`actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`serial_no` text,
`batch_no` varchar(140),
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`sales_order` varchar(140),
`so_detail` varchar(140),
`sales_invoice_item` varchar(140),
`delivery_note` varchar(140),
`dn_detail` varchar(140),
`delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`pos_invoice` varchar(140),
`pos_invoice_item` varchar(140),
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
index `sales_order`(`sales_order`),
index `so_detail`(`so_detail`),
index `delivery_note`(`delivery_note`),
index `dn_detail`(`dn_detail`),
index `pos_invoice`(`pos_invoice`),
index `purchase_order`(`purchase_order`),
index `project`(`project`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,642 WARNING database DDL Query made to DB:
create table `tabBudget Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`budget_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
index `account`(`account`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,700 WARNING database DDL Query made to DB:
create table `tabLedger Merge Accounts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`account_name` varchar(140),
`merged` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,768 WARNING database DDL Query made to DB:
create table `tabLoyalty Point Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`loyalty_program` varchar(140),
`loyalty_program_tier` varchar(140),
`customer` varchar(140),
`invoice_type` varchar(140),
`invoice` varchar(140),
`redeem_against` varchar(140),
`loyalty_points` int NOT NULL DEFAULT 0,
`purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`expiry_date` date,
`posting_date` date,
`company` varchar(140),
`discretionary_reason` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,839 WARNING database DDL Query made to DB:
create table `tabAccounting Dimension` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`label` varchar(140) UNIQUE,
`fieldname` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `document_type`(`document_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:35,910 WARNING database DDL Query made to DB:
create table `tabBank Statement Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`bank_account` varchar(140),
`bank` varchar(140),
`custom_delimiters` tinyint NOT NULL DEFAULT 0,
`delimiter_options` varchar(140) DEFAULT ',;\\t|',
`google_sheets_url` varchar(140),
`import_file` text,
`status` varchar(140) DEFAULT 'Pending',
`template_options` longtext,
`template_warnings` longtext,
`show_failed_logs` tinyint NOT NULL DEFAULT 0,
`reference_doctype` varchar(140) DEFAULT 'Bank Transaction',
`import_type` varchar(140) DEFAULT 'Insert New Records',
`submit_after_import` tinyint NOT NULL DEFAULT 1,
`mute_emails` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,006 WARNING database DDL Query made to DB:
create table `tabTax Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`tax_type` varchar(140) DEFAULT 'Sales',
`use_for_shopping_cart` tinyint NOT NULL DEFAULT 1,
`sales_tax_template` varchar(140),
`purchase_tax_template` varchar(140),
`customer` varchar(140),
`supplier` varchar(140),
`item` varchar(140),
`billing_city` varchar(140),
`billing_county` varchar(140),
`billing_state` varchar(140),
`billing_zipcode` varchar(140),
`billing_country` varchar(140),
`tax_category` varchar(140),
`customer_group` varchar(140),
`supplier_group` varchar(140),
`item_group` varchar(140),
`shipping_city` varchar(140),
`shipping_county` varchar(140),
`shipping_state` varchar(140),
`shipping_zipcode` varchar(140),
`shipping_country` varchar(140),
`from_date` date,
`to_date` date,
`priority` int NOT NULL DEFAULT 1,
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,105 WARNING database DDL Query made to DB:
create table `tabPOS Invoice Merge Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`posting_time` time(6),
`merge_invoices_based_on` varchar(140),
`pos_closing_entry` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`consolidated_invoice` varchar(140),
`consolidated_credit_note` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,174 WARNING database DDL Query made to DB:
create table `tabSubscription Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`plan` varchar(140),
`qty` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,235 WARNING database DDL Query made to DB:
create table `tabTax Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,301 WARNING database DDL Query made to DB:
create table `tabFinance Book` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`finance_book_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,361 WARNING database DDL Query made to DB:
create table `tabSupplier Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,418 WARNING database DDL Query made to DB:
create table `tabLedger Health Monitor Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,481 WARNING database DDL Query made to DB:
create table `tabAccounting Dimension Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`accounting_dimension` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`apply_restriction_on_values` tinyint NOT NULL DEFAULT 1,
`allow_or_restrict` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,572 WARNING database DDL Query made to DB:
create table `tabPOS Opening Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`period_start_date` datetime(6),
`period_end_date` date,
`status` varchar(140) DEFAULT 'Draft',
`posting_date` date,
`set_posting_date` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`pos_profile` varchar(140),
`pos_closing_entry` varchar(140),
`user` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,674 WARNING database DDL Query made to DB:
create table `tabAccount Closing Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`closing_date` date,
`account` varchar(140),
`cost_center` varchar(140),
`debit` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_currency` varchar(140),
`debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`project` varchar(140),
`company` varchar(140),
`finance_book` varchar(140),
`period_closing_voucher` varchar(140),
`is_period_closing_voucher_entry` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `closing_date`(`closing_date`),
index `account`(`account`),
index `company`(`company`),
index `period_closing_voucher`(`period_closing_voucher`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,737 WARNING database DDL Query made to DB:
create table `tabShipping Rule Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`country` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,818 WARNING database DDL Query made to DB:
create table `tabCashier Closing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'POS-CLO-',
`user` varchar(140),
`date` date,
`from_time` time(6),
`time` time(6),
`expense` decimal(21,9) NOT NULL DEFAULT 0.0,
`custody` decimal(21,9) NOT NULL DEFAULT 0.0,
`returns` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:36,910 WARNING database DDL Query made to DB:
create table `tabShare Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,072 WARNING database DDL Query made to DB:
create table `tabJournal Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_system_generated` tinyint NOT NULL DEFAULT 0,
`title` varchar(140),
`voucher_type` varchar(140) DEFAULT 'Journal Entry',
`naming_series` varchar(140),
`finance_book` varchar(140),
`process_deferred_accounting` varchar(140),
`reversal_of` varchar(140),
`tax_withholding_category` varchar(140),
`from_template` varchar(140),
`company` varchar(140),
`posting_date` date,
`apply_tds` tinyint NOT NULL DEFAULT 0,
`cheque_no` varchar(140),
`cheque_date` date,
`user_remark` text,
`total_debit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_credit` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference` decimal(21,9) NOT NULL DEFAULT 0.0,
`multi_currency` tinyint NOT NULL DEFAULT 0,
`total_amount_currency` varchar(140),
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount_in_words` varchar(140),
`clearance_date` date,
`remark` text,
`paid_loan` varchar(140),
`inter_company_journal_entry_reference` varchar(140),
`bill_no` varchar(140),
`bill_date` date,
`due_date` date,
`write_off_based_on` varchar(140) DEFAULT 'Accounts Receivable',
`write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pay_to_recd_from` varchar(140),
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`mode_of_payment` varchar(140),
`payment_order` varchar(140),
`is_opening` varchar(140) DEFAULT 'No',
`stock_entry` varchar(140),
`auto_repeat` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `voucher_type`(`voucher_type`),
index `company`(`company`),
index `posting_date`(`posting_date`),
index `cheque_no`(`cheque_no`),
index `cheque_date`(`cheque_date`),
index `clearance_date`(`clearance_date`),
index `is_opening`(`is_opening`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,167 WARNING database DDL Query made to DB:
create table `tabRepost Allowed Types` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`allowed` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,232 WARNING database DDL Query made to DB:
create table `tabDunning Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dunning_type` varchar(140) UNIQUE,
`is_default` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`dunning_fee` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_of_interest` decimal(21,9) NOT NULL DEFAULT 0.0,
`income_account` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,287 WARNING database DDL Query made to DB:
create table `tabPOS Closing Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`mode_of_payment` varchar(140),
`opening_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`expected_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`closing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,400 WARNING database DDL Query made to DB:
create table `tabExchange Rate Revaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`rounding_loss_allowance` decimal(21,9) NOT NULL DEFAULT 0.05,
`company` varchar(140),
`gain_loss_unbooked` decimal(21,9) NOT NULL DEFAULT 0.0,
`gain_loss_booked` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,465 WARNING database DDL Query made to DB:
create table `tabTax Withholding Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`category_name` varchar(140),
`round_off_tax_amount` tinyint NOT NULL DEFAULT 0,
`consider_party_ledger_amount` tinyint NOT NULL DEFAULT 0,
`tax_on_excess_amount` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,531 WARNING database DDL Query made to DB:
create table `tabMonthly Distribution` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`distribution_id` varchar(140) UNIQUE,
`fiscal_year` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `fiscal_year`(`fiscal_year`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,596 WARNING database DDL Query made to DB:
create table `tabProcess Payment Reconciliation Log Allocations` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`reference_row` varchar(140),
`invoice_type` varchar(140),
`invoice_number` varchar(140),
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`unreconciled_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_advance` varchar(140),
`difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`gain_loss_posting_date` date,
`difference_account` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`reconciled` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,660 WARNING database DDL Query made to DB:
create table `tabParty Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`primary_role` varchar(140),
`secondary_role` varchar(140),
`primary_party` varchar(140),
`secondary_party` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,721 WARNING database DDL Query made to DB:
create table `tabItem Tax Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`company` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,803 WARNING database DDL Query made to DB:
create table `tabProcess Payment Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`receivable_payable_account` varchar(140),
`default_advance_account` varchar(140),
`from_invoice_date` date,
`to_invoice_date` date,
`from_payment_date` date,
`to_payment_date` date,
`cost_center` varchar(140),
`bank_cash_account` varchar(140),
`status` varchar(140),
`error_log` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,876 WARNING database DDL Query made to DB:
create table `tabPayment Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'PMO-',
`company` varchar(140),
`payment_order_type` varchar(140),
`party` varchar(140),
`posting_date` date,
`company_bank` varchar(140),
`company_bank_account` varchar(140),
`account` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,939 WARNING database DDL Query made to DB:
create table `tabBank Clearance Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_document` varchar(140),
`payment_entry` varchar(140),
`against_account` varchar(140),
`amount` varchar(140),
`posting_date` date,
`cheque_number` varchar(140),
`cheque_date` date,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:37,986 WARNING database DDL Query made to DB:
create table `tabPOS Search Fields` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`field` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,047 WARNING database DDL Query made to DB:
create table `tabSales Invoice Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`remarks` text,
`reference_row` varchar(140),
`advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0,
`ref_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference_posting_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,105 WARNING database DDL Query made to DB:
create table `tabAdvance Tax` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`reference_detail` varchar(140),
`account_head` varchar(140),
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,178 WARNING database DDL Query made to DB:
create table `tabSubscription Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`plan_name` varchar(140) UNIQUE,
`currency` varchar(140),
`item` varchar(140),
`price_determination` varchar(140),
`cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list` varchar(140),
`billing_interval` varchar(140) DEFAULT 'Day',
`billing_interval_count` int NOT NULL DEFAULT 1,
`product_price_id` varchar(140),
`payment_gateway` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,237 WARNING database DDL Query made to DB:
create table `tabBank Transaction Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`bank_transaction_field` varchar(140),
`file_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,285 WARNING database DDL Query made to DB:
create table `tabFiscal Year Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,345 WARNING database DDL Query made to DB:
create table `tabOpening Invoice Creation Tool Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`invoice_number` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`temporary_opening_account` varchar(140),
`posting_date` date,
`due_date` date,
`item_name` varchar(140) DEFAULT 'Opening Invoice Item',
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`qty` varchar(140) DEFAULT '1',
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,394 WARNING database DDL Query made to DB:
create sequence if not exists ledger_health_id_seq nocache nocycle
2025-04-22 15:34:38,417 WARNING database DDL Query made to DB:
create table `tabLedger Health` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`voucher_type` varchar(140),
`voucher_no` varchar(140),
`checked_on` datetime(6),
`debit_credit_mismatch` tinyint NOT NULL DEFAULT 0,
`general_and_payment_ledger_mismatch` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,531 WARNING database DDL Query made to DB:
create table `tabPayment Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`company` varchar(140),
`account_type` varchar(140),
`account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`due_date` date,
`voucher_detail_no` varchar(140),
`cost_center` varchar(140),
`finance_book` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`against_voucher_type` varchar(140),
`against_voucher_no` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_currency` varchar(140),
`amount_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`delinked` tinyint NOT NULL DEFAULT 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_date`(`posting_date`),
index `company`(`company`),
index `account`(`account`),
index `party_type`(`party_type`),
index `party`(`party`),
index `voucher_detail_no`(`voucher_detail_no`),
index `voucher_type`(`voucher_type`),
index `voucher_no`(`voucher_no`),
index `against_voucher_type`(`against_voucher_type`),
index `against_voucher_no`(`against_voucher_no`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry`
				ADD INDEX IF NOT EXISTS `against_voucher_no_against_voucher_type_index`(against_voucher_no, against_voucher_type)
2025-04-22 15:34:38,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry`
				ADD INDEX IF NOT EXISTS `voucher_no_voucher_type_index`(voucher_no, voucher_type)
2025-04-22 15:34:38,788 WARNING database DDL Query made to DB:
create table `tabSubscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`party_type` varchar(140),
`party` varchar(140),
`company` varchar(140),
`status` varchar(140),
`start_date` date,
`end_date` date,
`cancelation_date` date,
`trial_period_start` date,
`trial_period_end` date,
`follow_calendar_months` tinyint NOT NULL DEFAULT 0,
`generate_new_invoices_past_due_date` tinyint NOT NULL DEFAULT 0,
`submit_invoice` tinyint NOT NULL DEFAULT 1,
`current_invoice_start` date,
`current_invoice_end` date,
`days_until_due` int NOT NULL DEFAULT 0,
`generate_invoice_at` varchar(140) DEFAULT 'End of the current subscription period',
`number_of_days` int NOT NULL DEFAULT 0,
`cancel_at_period_end` tinyint NOT NULL DEFAULT 0,
`sales_tax_template` varchar(140),
`purchase_tax_template` varchar(140),
`apply_additional_discount` varchar(140),
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,905 WARNING database DDL Query made to DB:
create table `tabShipping Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`shipping_rule_type` varchar(140),
`company` varchar(140),
`account` varchar(140),
`cost_center` varchar(140),
`calculate_based_on` varchar(140) DEFAULT 'Fixed',
`shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:38,969 WARNING database DDL Query made to DB:
create table `tabTax Withholding Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:39,044 WARNING database DDL Query made to DB:
create table `tabBank Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account_name` varchar(140),
`account` varchar(140),
`bank` varchar(140),
`account_type` varchar(140),
`account_subtype` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`is_default` tinyint NOT NULL DEFAULT 0,
`is_company_account` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`iban` varchar(30),
`branch_code` varchar(140),
`bank_account_no` varchar(30),
`integration_id` varchar(140) UNIQUE,
`last_integration_date` date,
`mask` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:39,158 WARNING database DDL Query made to DB:
create table `tabDunning` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'DUNN-.MM.-.YY.-',
`customer` varchar(140),
`customer_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`status` varchar(140) DEFAULT 'Unresolved',
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`dunning_type` varchar(140),
`rate_of_interest` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_interest` decimal(21,9) NOT NULL DEFAULT 0.0,
`dunning_fee` decimal(21,9) NOT NULL DEFAULT 0.0,
`dunning_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_dunning_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`spacer` varchar(140),
`total_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0,
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`language` varchar(140),
`body_text` longtext,
`letter_head` varchar(140),
`closing_text` longtext,
`income_account` varchar(140),
`cost_center` varchar(140),
`amended_from` varchar(140),
`customer_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`company_address` varchar(140),
`company_address_display` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:40,486 WARNING database DDL Query made to DB:
create table `tabPOS Invoice Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`barcode` varchar(140),
`has_item_scanned` tinyint NOT NULL DEFAULT 0,
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`grant_commission` tinyint NOT NULL DEFAULT 0,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`delivered_by_supplier` tinyint NOT NULL DEFAULT 0,
`income_account` varchar(140),
`is_fixed_asset` tinyint NOT NULL DEFAULT 0,
`asset` varchar(140),
`finance_book` varchar(140),
`expense_account` varchar(140),
`deferred_revenue_account` varchar(140),
`service_stop_date` date,
`enable_deferred_revenue` tinyint NOT NULL DEFAULT 0,
`service_start_date` date,
`service_end_date` date,
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0,
`item_tax_rate` text,
`actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`serial_no` text,
`batch_no` varchar(140),
`sales_order` varchar(140),
`so_detail` varchar(140),
`pos_invoice_item` varchar(140),
`delivery_note` varchar(140),
`dn_detail` varchar(140),
`delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`project` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `sales_order`(`sales_order`),
index `so_detail`(`so_detail`),
index `delivery_note`(`delivery_note`),
index `dn_detail`(`dn_detail`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,270 WARNING database DDL Query made to DB:
create table `tabJournal Entry Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template_title` varchar(140) UNIQUE,
`voucher_type` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`is_opening` varchar(140) DEFAULT 'No',
`multi_currency` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,342 WARNING database DDL Query made to DB:
create table `tabDiscounted Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_invoice` varchar(140),
`customer` varchar(140),
`posting_date` date,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit_to` varchar(140),
index `sales_invoice`(`sales_invoice`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,437 WARNING database DDL Query made to DB:
create table `tabApplicable On Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`applicable_on_account` varchar(140),
`is_mandatory` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,507 WARNING database DDL Query made to DB:
create table `tabPromotional Scheme Price Discount` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disable` tinyint NOT NULL DEFAULT 0,
`apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0,
`rule_description` text,
`min_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`min_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_or_discount` varchar(140) DEFAULT 'Discount Percentage',
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_price_list` varchar(140),
`warehouse` varchar(140),
`threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`validate_applied_rule` tinyint NOT NULL DEFAULT 0,
`priority` varchar(140),
`apply_discount_on_rate` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,585 WARNING database DDL Query made to DB:
create table `tabProcess Deferred Accounting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`type` varchar(140),
`account` varchar(140),
`posting_date` date,
`start_date` date,
`end_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,678 WARNING database DDL Query made to DB:
create table `tabInvoice Discounting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`loan_start_date` date,
`loan_period` int NOT NULL DEFAULT 0,
`loan_end_date` date,
`status` varchar(140),
`company` varchar(140),
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`bank_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`short_term_loan` varchar(140),
`bank_account` varchar(140),
`bank_charges_account` varchar(140),
`accounts_receivable_credit` varchar(140),
`accounts_receivable_discounted` varchar(140),
`accounts_receivable_unpaid` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,759 WARNING database DDL Query made to DB:
create table `tabSales Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`charge_type` varchar(140),
`row_id` varchar(140),
`account_head` varchar(140),
`description` text,
`included_in_print_rate` tinyint NOT NULL DEFAULT 0,
`included_in_paid_amount` tinyint NOT NULL DEFAULT 0,
`set_by_item_tax_template` tinyint NOT NULL DEFAULT 0,
`cost_center` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_currency` varchar(140),
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_wise_tax_detail` longtext,
`dont_recompute_tax` tinyint NOT NULL DEFAULT 0,
index `account_head`(`account_head`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,813 WARNING database DDL Query made to DB:
create table `tabCashier Closing Payments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`mode_of_payment` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,919 WARNING database DDL Query made to DB:
create table `tabBudget` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`budget_against` varchar(140) DEFAULT 'Cost Center',
`company` varchar(140),
`cost_center` varchar(140),
`naming_series` varchar(140),
`project` varchar(140),
`fiscal_year` varchar(140),
`monthly_distribution` varchar(140),
`amended_from` varchar(140),
`applicable_on_material_request` tinyint NOT NULL DEFAULT 0,
`action_if_annual_budget_exceeded_on_mr` varchar(140) DEFAULT 'Stop',
`action_if_accumulated_monthly_budget_exceeded_on_mr` varchar(140) DEFAULT 'Warn',
`applicable_on_purchase_order` tinyint NOT NULL DEFAULT 0,
`action_if_annual_budget_exceeded_on_po` varchar(140) DEFAULT 'Stop',
`action_if_accumulated_monthly_budget_exceeded_on_po` varchar(140) DEFAULT 'Warn',
`applicable_on_booking_actual_expenses` tinyint NOT NULL DEFAULT 0,
`action_if_annual_budget_exceeded` varchar(140) DEFAULT 'Stop',
`action_if_accumulated_monthly_budget_exceeded` varchar(140) DEFAULT 'Warn',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:41,971 WARNING database DDL Query made to DB:
create table `tabSales Partner Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_partner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,027 WARNING database DDL Query made to DB:
create table `tabMonthly Distribution Percentage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`month` varchar(140),
`percentage_allocation` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,080 WARNING database DDL Query made to DB:
create table `tabRepost Accounting Ledger Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`voucher_type` varchar(140),
`voucher_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,176 WARNING database DDL Query made to DB:
create table `tabTerritory Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`territory` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,255 WARNING database DDL Query made to DB:
create table `tabLoyalty Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`loyalty_program_name` varchar(140) UNIQUE,
`loyalty_program_type` varchar(140),
`from_date` date,
`to_date` date,
`customer_group` varchar(140),
`customer_territory` varchar(140),
`auto_opt_in` tinyint NOT NULL DEFAULT 0,
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`expiry_duration` int NOT NULL DEFAULT 0,
`expense_account` varchar(140),
`company` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,311 WARNING database DDL Query made to DB:
create table `tabSubscription Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`invoice` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,414 WARNING database DDL Query made to DB:
create table `tabBank Transaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'ACC-BTN-.YYYY.-',
`date` date,
`status` varchar(140) DEFAULT 'Pending',
`bank_account` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`deposit` decimal(21,9) NOT NULL DEFAULT 0.0,
`withdrawal` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`description` text,
`reference_number` varchar(140),
`transaction_id` varchar(140) UNIQUE,
`transaction_type` varchar(50),
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`unallocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`party_type` varchar(140),
`party` varchar(140),
`bank_party_name` varchar(140),
`bank_party_account_number` varchar(140),
`bank_party_iban` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,502 WARNING database DDL Query made to DB:
create table `tabCost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cost_center_name` varchar(140),
`cost_center_number` varchar(140),
`parent_cost_center` varchar(140),
`company` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabCost Center`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:42,625 WARNING database DDL Query made to DB:
create table `tabPayment Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_term` varchar(140),
`description` text,
`due_date` date,
`mode_of_payment` varchar(140),
`invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_type` varchar(140) DEFAULT 'Percentage',
`discount_date` date,
`discount` decimal(21,9) NOT NULL DEFAULT 0.0,
`payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discounted_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,738 WARNING database DDL Query made to DB:
create table `tabPromotional Scheme Product Discount` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disable` tinyint NOT NULL DEFAULT 0,
`apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0,
`rule_description` text,
`min_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`min_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`same_item` tinyint NOT NULL DEFAULT 0,
`free_item` varchar(140),
`free_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`free_item_uom` varchar(140),
`free_item_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`round_free_qty` tinyint NOT NULL DEFAULT 0,
`warehouse` varchar(140),
`threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`priority` varchar(140),
`is_recursive` tinyint NOT NULL DEFAULT 0,
`recurse_for` decimal(21,9) NOT NULL DEFAULT 0.0,
`apply_recursion_over` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:42,798 WARNING database DDL Query made to DB:
create table `tabDunning Letter Text` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`language` varchar(140),
`is_default_language` tinyint NOT NULL DEFAULT 0,
`body_text` longtext,
`closing_text` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,091 WARNING database DDL Query made to DB:
create table `tabPurchase Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`tax_id` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` tinyint NOT NULL DEFAULT 0,
`due_date` date,
`is_paid` tinyint NOT NULL DEFAULT 0,
`is_return` tinyint NOT NULL DEFAULT 0,
`return_against` varchar(140),
`update_outstanding_for_self` tinyint NOT NULL DEFAULT 1,
`update_billed_amount_in_purchase_order` tinyint NOT NULL DEFAULT 0,
`update_billed_amount_in_purchase_receipt` tinyint NOT NULL DEFAULT 1,
`apply_tds` tinyint NOT NULL DEFAULT 0,
`tax_withholding_category` varchar(140),
`amended_from` varchar(140),
`bill_no` varchar(140),
`bill_date` date,
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`use_transaction_date_exchange_rate` tinyint NOT NULL DEFAULT 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`update_stock` tinyint NOT NULL DEFAULT 0,
`set_warehouse` varchar(140),
`set_from_warehouse` varchar(140),
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`use_company_roundoff_cost_center` tinyint NOT NULL DEFAULT 0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` varchar(240),
`total_advance` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`other_charges_calculation` longtext,
`mode_of_payment` varchar(140),
`base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`clearance_date` date,
`cash_bank_account` varchar(140),
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocate_advances_automatically` tinyint NOT NULL DEFAULT 0,
`only_include_allocated_payments` tinyint NOT NULL DEFAULT 0,
`write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`supplier_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` longtext,
`billing_address` varchar(140),
`billing_address_display` longtext,
`payment_terms_template` varchar(140),
`ignore_default_payment_terms_template` tinyint NOT NULL DEFAULT 0,
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) DEFAULT 'Draft',
`per_received` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_to` varchar(140),
`party_account_currency` varchar(140),
`is_opening` varchar(140) DEFAULT 'No',
`against_expense_account` text,
`unrealized_profit_loss_account` varchar(140),
`subscription` varchar(140),
`auto_repeat` varchar(140),
`from_date` date,
`to_date` date,
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`on_hold` tinyint NOT NULL DEFAULT 0,
`release_date` date,
`hold_comment` text,
`is_internal_supplier` tinyint NOT NULL DEFAULT 0,
`represents_company` varchar(140),
`supplier_group` varchar(140),
`sender` varchar(140),
`inter_company_invoice_reference` varchar(140),
`is_old_subcontracting_flow` tinyint NOT NULL DEFAULT 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `bill_no`(`bill_no`),
index `credit_to`(`credit_to`),
index `release_date`(`release_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,253 WARNING database DDL Query made to DB:
create table `tabPOS Payment Method` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`default` tinyint NOT NULL DEFAULT 0,
`allow_in_returns` tinyint NOT NULL DEFAULT 0,
`mode_of_payment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,316 WARNING database DDL Query made to DB:
create table `tabPayment Order Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`reference_name` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`supplier` varchar(140),
`payment_request` varchar(140),
`mode_of_payment` varchar(140),
`bank_account` varchar(140),
`account` varchar(140),
`payment_reference` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,399 WARNING database DDL Query made to DB:
create table `tabProcess Payment Reconciliation Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`process_pr` varchar(140),
`status` varchar(140),
`allocated` tinyint NOT NULL DEFAULT 0,
`reconciled` tinyint NOT NULL DEFAULT 0,
`total_allocations` int NOT NULL DEFAULT 0,
`reconciled_entries` int NOT NULL DEFAULT 0,
`error_log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,457 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts CC` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cc` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,552 WARNING database DDL Query made to DB:
create table `tabBank Guarantee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`bg_type` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`customer` varchar(140),
`supplier` varchar(140),
`project` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`start_date` date,
`validity` int NOT NULL DEFAULT 0,
`end_date` date,
`bank` varchar(140),
`bank_account` varchar(140),
`account` varchar(140),
`bank_account_no` varchar(140),
`iban` varchar(140),
`branch_code` varchar(140),
`swift_number` varchar(140),
`more_information` longtext,
`bank_guarantee_number` varchar(140) UNIQUE,
`name_of_beneficiary` varchar(140),
`margin_money` decimal(21,9) NOT NULL DEFAULT 0.0,
`charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`fixed_deposit_number` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,609 WARNING database DDL Query made to DB:
create table `tabCustomer Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`customer` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,718 WARNING database DDL Query made to DB:
create table `tabPOS Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`customer` varchar(140),
`country` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`warehouse` varchar(140),
`utm_source` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`company_address` varchar(140),
`hide_images` tinyint NOT NULL DEFAULT 0,
`hide_unavailable_items` tinyint NOT NULL DEFAULT 0,
`auto_add_item_to_cart` tinyint NOT NULL DEFAULT 0,
`validate_stock_on_save` tinyint NOT NULL DEFAULT 0,
`print_receipt_on_order_complete` tinyint NOT NULL DEFAULT 0,
`update_stock` tinyint NOT NULL DEFAULT 1,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`allow_rate_change` tinyint NOT NULL DEFAULT 0,
`allow_discount_change` tinyint NOT NULL DEFAULT 0,
`disable_grand_total_to_default_mop` tinyint NOT NULL DEFAULT 0,
`print_format` varchar(140),
`letter_head` varchar(140),
`tc_name` varchar(140),
`select_print_heading` varchar(140),
`selling_price_list` varchar(140),
`currency` varchar(140),
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`write_off_limit` decimal(21,9) NOT NULL DEFAULT 1.0,
`account_for_change_amount` varchar(140),
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`income_account` varchar(140),
`expense_account` varchar(140),
`taxes_and_charges` varchar(140),
`tax_category` varchar(140),
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`cost_center` varchar(140),
`project` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
