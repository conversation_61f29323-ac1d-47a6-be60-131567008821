2025-04-22 15:35:15,073 WARNING database DDL Query made to DB:
create table `tabAsset Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset_category_name` varchar(140) UNIQUE,
`enable_cwip_accounting` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,172 WARNING database DDL Query made to DB:
create table `tabAsset Value Adjustment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`asset_category` varchar(140),
`date` date,
`finance_book` varchar(140),
`amended_from` varchar(140),
`current_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`new_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference_account` varchar(140),
`journal_entry` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,266 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Stock Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`warehouse` varchar(140),
`purchase_receipt_item` varchar(140),
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`serial_no` text,
`batch_no` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,476 WARNING database DDL Query made to DB:
create table `tabAsset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`asset_name` varchar(140),
`asset_category` varchar(140),
`location` varchar(140),
`image` text,
`status` varchar(140) DEFAULT 'Draft',
`company` varchar(140),
`asset_owner` varchar(140) DEFAULT 'Company',
`asset_owner_company` varchar(140),
`is_existing_asset` tinyint NOT NULL DEFAULT 0,
`is_composite_asset` tinyint NOT NULL DEFAULT 0,
`purchase_receipt` varchar(140),
`purchase_receipt_item` varchar(140),
`purchase_invoice` varchar(140),
`purchase_invoice_item` varchar(140),
`purchase_date` date,
`available_for_use_date` date,
`gross_purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`asset_quantity` int NOT NULL DEFAULT 1,
`additional_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`disposal_date` date,
`calculate_depreciation` tinyint NOT NULL DEFAULT 0,
`opening_accumulated_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`opening_number_of_booked_depreciations` int NOT NULL DEFAULT 0,
`is_fully_depreciated` tinyint NOT NULL DEFAULT 0,
`depreciation_method` varchar(140),
`value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_number_of_depreciations` int NOT NULL DEFAULT 0,
`frequency_of_depreciation` int NOT NULL DEFAULT 0,
`next_depreciation_date` date,
`policy_number` varchar(140),
`insurer` varchar(140),
`insured_value` varchar(140),
`insurance_start_date` date,
`insurance_end_date` date,
`comprehensive_insurance` varchar(140),
`cost_center` varchar(140),
`custodian` varchar(140),
`default_finance_book` varchar(140),
`depr_entry_posting_status` varchar(140),
`booked_fixed_asset` tinyint NOT NULL DEFAULT 0,
`customer` varchar(140),
`supplier` varchar(140),
`department` varchar(140),
`split_from` varchar(140),
`journal_entry_for_scrap` varchar(140),
`amended_from` varchar(140),
`maintenance_required` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,548 WARNING database DDL Query made to DB:
create table `tabAsset Repair Consumed Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_quantity` varchar(140),
`total_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`serial_no` text,
`serial_and_batch_bundle` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,647 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset_name` varchar(140) UNIQUE,
`asset_category` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`maintenance_team` varchar(140),
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,719 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,809 WARNING database DDL Query made to DB:
create table `tabAsset Movement Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`source_location` varchar(140),
`from_employee` varchar(140),
`asset_name` varchar(140),
`target_location` varchar(140),
`to_employee` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,878 WARNING database DDL Query made to DB:
create table `tabAsset Finance Book` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`finance_book` varchar(140),
`depreciation_method` varchar(140),
`frequency_of_depreciation` int NOT NULL DEFAULT 0,
`total_number_of_depreciations` int NOT NULL DEFAULT 0,
`depreciation_start_date` date,
`salvage_value_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`expected_value_after_useful_life` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_of_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`daily_prorata_based` tinyint NOT NULL DEFAULT 0,
`shift_based` tinyint NOT NULL DEFAULT 0,
`value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_number_of_booked_depreciations` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:15,952 WARNING database DDL Query made to DB:
create table `tabDepreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`schedule_date` date,
`depreciation_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`accumulated_depreciation_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`journal_entry` varchar(140),
`shift` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,062 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset_maintenance` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`maintenance_type` varchar(140),
`periodicity` varchar(140),
`has_certificate` tinyint NOT NULL DEFAULT 0,
`certificate_attachement` text,
`maintenance_status` varchar(140),
`assign_to_name` varchar(140),
`task_assignee_email` varchar(140),
`due_date` date,
`completion_date` date,
`description` varchar(140),
`actions_performed` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,174 WARNING database DDL Query made to DB:
create table `tabAsset Repair Purchase Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`purchase_invoice` varchar(140),
`expense_account` varchar(140),
`repair_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,249 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`expense_account` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`uom` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,351 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`opening_accumulated_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`opening_number_of_booked_depreciations` int NOT NULL DEFAULT 0,
`finance_book` varchar(140),
`finance_book_id` int NOT NULL DEFAULT 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int NOT NULL DEFAULT 0,
`rate_of_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0,
`daily_prorata_based` tinyint NOT NULL DEFAULT 0,
`shift_based` tinyint NOT NULL DEFAULT 0,
`frequency_of_depreciation` int NOT NULL DEFAULT 0,
`expected_value_after_useful_life` decimal(21,9) NOT NULL DEFAULT 0.0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,412 WARNING database DDL Query made to DB:
create table `tabLinked Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,489 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Asset Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`asset` varchar(140),
`asset_name` varchar(140),
`finance_book` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`current_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`asset_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`fixed_asset_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,570 WARNING database DDL Query made to DB:
create table `tabAsset Movement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`purpose` varchar(140),
`transaction_date` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,645 WARNING database DDL Query made to DB:
create table `tabAsset Shift Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`shift_name` varchar(140) UNIQUE,
`shift_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`default` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,764 WARNING database DDL Query made to DB:
create table `tabWebsite Filter Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,837 WARNING database DDL Query made to DB:
create table `tabWebsite Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`attribute` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:16,915 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`scheduled_date` date,
`actual_date` date,
`sales_person` varchar(140),
`completion_status` varchar(140) DEFAULT 'Pending',
`serial_no` text,
`item_reference` varchar(140),
index `item_code`(`item_code`),
index `scheduled_date`(`scheduled_date`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,021 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`start_date` date,
`end_date` date,
`periodicity` varchar(140),
`no_of_visits` int NOT NULL DEFAULT 0,
`sales_person` varchar(140),
`serial_no` text,
`sales_order` varchar(140),
`serial_and_batch_bundle` varchar(140),
index `item_code`(`item_code`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `sales_order`(`sales_order`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,138 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`address_display` longtext,
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`maintenance_schedule` varchar(140),
`maintenance_schedule_detail` varchar(140),
`mntc_date` date,
`mntc_time` time(6),
`completion_status` varchar(140),
`maintenance_type` varchar(140) DEFAULT 'Unscheduled',
`customer_feedback` text,
`status` varchar(140) DEFAULT 'Draft',
`amended_from` varchar(140),
`company` varchar(140),
`customer_address` varchar(140),
`contact_person` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,242 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`transaction_date` date,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`contact_display` text,
`customer_address` varchar(140),
`address_display` longtext,
`territory` varchar(140),
`customer_group` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,330 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit Purpose` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`service_person` varchar(140),
`serial_no` varchar(140),
`description` longtext,
`work_done` text,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`maintenance_schedule_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,422 WARNING database DDL Query made to DB:
create table `tabLower Deduction Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`tax_withholding_category` varchar(140),
`fiscal_year` varchar(140),
`company` varchar(140),
`certificate_no` varchar(140) UNIQUE,
`supplier` varchar(140),
`pan_no` varchar(140),
`valid_from` date,
`valid_upto` date,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`certificate_limit` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,512 WARNING database DDL Query made to DB:
create table `tabSouth Africa VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,580 WARNING database DDL Query made to DB:
create table `tabUAE VAT Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,671 WARNING database DDL Query made to DB:
create table `tabImport Supplier Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`invoice_series` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`supplier_group` varchar(140),
`tax_account` varchar(140),
`default_buying_price_list` varchar(140),
`zip_file` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,737 WARNING database DDL Query made to DB:
create table `tabUAE VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:17,990 WARNING database DDL Query made to DB:
create table `tabQuality Procedure Process` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`process_description` longtext,
`procedure` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,066 WARNING database DDL Query made to DB:
create table `tabQuality Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`goal` varchar(140) UNIQUE,
`frequency` varchar(140) DEFAULT 'None',
`procedure` varchar(140),
`weekday` varchar(140),
`date` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,187 WARNING database DDL Query made to DB:
create table `tabQuality Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`corrective_preventive` varchar(140) DEFAULT 'Corrective',
`review` varchar(140),
`feedback` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`date` date,
`goal` varchar(140),
`procedure` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,256 WARNING database DDL Query made to DB:
create table `tabNon Conformance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` varchar(140),
`procedure` varchar(140),
`process_owner` varchar(140),
`full_name` varchar(140),
`status` varchar(140),
`details` longtext,
`corrective_action` longtext,
`preventive_action` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,331 WARNING database DDL Query made to DB:
create table `tabQuality Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`goal` varchar(140),
`date` date,
`procedure` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`additional_information` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,408 WARNING database DDL Query made to DB:
create table `tabQuality Review Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`review` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,482 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`parameter` varchar(140),
`rating` varchar(140) DEFAULT '1',
`feedback` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,543 WARNING database DDL Query made to DB:
create table `tabQuality Goal Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,616 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,788 WARNING database DDL Query made to DB:
create table `tabQuality Action Resolution` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`problem` longtext,
`resolution` longtext,
`status` varchar(140),
`responsible` varchar(140),
`completion_by` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,859 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,918 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Agenda` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`agenda` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:18,999 WARNING database DDL Query made to DB:
create table `tabQuality Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,068 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Minutes` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`minute` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,134 WARNING database DDL Query made to DB:
create table `tabQuality Meeting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140) DEFAULT 'Open',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,220 WARNING database DDL Query made to DB:
create table `tabQuality Procedure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`quality_procedure_name` varchar(140) UNIQUE,
`process_owner` varchar(140),
`process_owner_full_name` varchar(140),
`parent_quality_procedure` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`lft` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,338 WARNING database DDL Query made to DB:
create table `tabCommunication Medium` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`communication_channel` varchar(140),
`communication_medium_type` varchar(140),
`catch_all` varchar(140),
`provider` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,407 WARNING database DDL Query made to DB:
create table `tabCommunication Medium Timeslot` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
`employee_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,488 WARNING database DDL Query made to DB:
create table `tabTelephony Call Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`call_type` varchar(140) UNIQUE,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,565 WARNING database DDL Query made to DB:
create table `tabIncoming Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`call_routing` varchar(140) DEFAULT 'Sequential',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,641 WARNING database DDL Query made to DB:
create table `tabVoice Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
`call_receiving_device` varchar(140) DEFAULT 'Computer',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,709 WARNING database DDL Query made to DB:
create table `tabIncoming Call Handling Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6) DEFAULT '9:00:00',
`to_time` time(6) DEFAULT '17:00:00',
`agent_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,802 WARNING database DDL Query made to DB:
create table `tabCall Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`id` varchar(140) UNIQUE,
`from` varchar(140),
`to` varchar(140),
`call_received_by` varchar(140),
`employee_user_id` varchar(140),
`medium` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`type` varchar(140),
`customer` varchar(140),
`status` varchar(140),
`duration` decimal(21,9),
`recording_url` varchar(140),
`type_of_call` varchar(140),
`summary` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:19,935 WARNING database DDL Query made to DB:
create table `tabBulk Transaction Log Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_doctype` varchar(140),
`transaction_name` varchar(140),
`date` date,
`time` time(6),
`transaction_status` varchar(140),
`error_description` longtext,
`to_doctype` varchar(140),
`retried` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `from_doctype`(`from_doctype`),
index `transaction_name`(`transaction_name`),
index `date`(`date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,070 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`bom` varchar(140),
`include_exploded_items` tinyint NOT NULL DEFAULT 0,
`schedule_date` date,
`expected_delivery_date` date,
`description` longtext,
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`received_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rm_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`service_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`warehouse` varchar(140),
`expense_account` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`job_card` varchar(140),
`purchase_order_item` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
`subcontracting_conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
index `item_code`(`item_code`),
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,171 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`purchase_order_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
index `item_code`(`item_code`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,255 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0,
`reference_name` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`available_qty_for_consumption` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`current_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`subcontracting_order` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,389 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`is_scrap_item` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`brand` varchar(140),
`image` text,
`received_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rejected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rm_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`service_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`scrap_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rm_supp_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`warehouse` varchar(140),
`subcontracting_order` varchar(140),
`subcontracting_order_item` varchar(140),
`subcontracting_receipt_item` varchar(140),
`job_card` varchar(140),
`rejected_warehouse` varchar(140),
`bom` varchar(140),
`include_exploded_items` tinyint NOT NULL DEFAULT 0,
`quality_inspection` varchar(140),
`schedule_date` date,
`reference_name` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
index `item_code`(`item_code`),
index `subcontracting_order`(`subcontracting_order`),
index `subcontracting_order_item`(`subcontracting_order_item`),
index `job_card`(`job_card`),
index `purchase_order`(`purchase_order`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,561 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` tinyint NOT NULL DEFAULT 0,
`is_return` tinyint NOT NULL DEFAULT 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` varchar(240),
`bill_no` varchar(140),
`bill_date` date,
`supplier_address` varchar(140),
`contact_person` varchar(140),
`address_display` longtext,
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` longtext,
`billing_address` varchar(140),
`billing_address_display` longtext,
`distribute_additional_costs_based_on` varchar(140) DEFAULT 'Qty',
`total_additional_costs` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`range` varchar(140),
`represents_company` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`per_returned` decimal(21,9) NOT NULL DEFAULT 0.0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`language` varchar(140),
`instructions` text,
`select_print_heading` varchar(140),
`remarks` text,
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `status`(`status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,664 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0,
`reserve_warehouse` varchar(140),
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,749 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_active` tinyint NOT NULL DEFAULT 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:20,926 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{supplier_name}',
`naming_series` varchar(140),
`purchase_order` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_warehouse` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`set_reserve_warehouse` varchar(140),
`supplier_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` longtext,
`billing_address` varchar(140),
`billing_address_display` longtext,
`distribute_additional_costs_based_on` varchar(140) DEFAULT 'Qty',
`total_additional_costs` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Draft',
`per_received` decimal(21,9) NOT NULL DEFAULT 0.0,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:21,023 WARNING database DDL Query made to DB:
create table `tabCommon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`code_list` varchar(140),
`title` varchar(300),
`common_code` varchar(300),
`description` text,
`additional_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_list`(`code_list`),
index `common_code`(`common_code`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:21,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommon Code`
				ADD INDEX IF NOT EXISTS `code_list_common_code_index`(code_list, common_code)
2025-04-22 15:35:21,175 WARNING database DDL Query made to DB:
create table `tabCode List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`canonical_uri` varchar(140),
`url` varchar(140),
`default_common_code` varchar(140),
`version` varchar(140),
`publisher` varchar(140),
`publisher_id` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:36:15,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_quantity` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:15,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabBisect Nodes` MODIFY `balance_sheet_summary` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `profit_loss_summary` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `generated` tinyint NOT NULL DEFAULT 0, MODIFY `difference` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:16,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` MODIFY `credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `transaction_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit_in_transaction_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `debit_in_transaction_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_cancelled` tinyint NOT NULL DEFAULT 0, MODIFY `debit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `to_rename` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:16,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` MODIFY `include_ageing` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_cr_dr_notes` tinyint NOT NULL DEFAULT 0, MODIFY `show_net_values_in_party_account` tinyint NOT NULL DEFAULT 0, MODIFY `include_break` tinyint NOT NULL DEFAULT 1, MODIFY `filter_duration` int NOT NULL DEFAULT 1, MODIFY `based_on_payment_terms` tinyint NOT NULL DEFAULT 0, MODIFY `show_remarks` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_exchange_rate_revaluation_journals` tinyint NOT NULL DEFAULT 0, MODIFY `primary_mandatory` tinyint NOT NULL DEFAULT 1, MODIFY `enable_auto_email` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:16,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Condition` MODIFY `shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `from_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `to_value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:16,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Detail` MODIFY `rule_applied` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:16,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax Template Detail` MODIFY `tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:16,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Term` MODIFY `credit_days` int NOT NULL DEFAULT 0, MODIFY `credit_months` int NOT NULL DEFAULT 0, MODIFY `discount_validity` int NOT NULL DEFAULT 0, MODIFY `discount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:16,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Timesheet` MODIFY `billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:17,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rm_supp_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `landed_cost_voucher_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rejected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `enable_deferred_expense` tinyint NOT NULL DEFAULT 0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 1, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `item_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sales_incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:17,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `apply_tax_withholding_amount` tinyint NOT NULL DEFAULT 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_received_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reconcile_on_advance_payment_date` tinyint NOT NULL DEFAULT 0, MODIFY `custom_remarks` tinyint NOT NULL DEFAULT 0, MODIFY `received_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_received_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `book_advance_payments_in_separate_party_account` tinyint NOT NULL DEFAULT 0, MODIFY `paid_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_amount_after_tax` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `unallocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `target_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `source_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:17,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `include_in_gross` tinyint NOT NULL DEFAULT 0, MODIFY `tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:17,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `payment_term_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:17,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Terms Template` MODIFY `allocate_payment_based_on_payment_terms` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:17,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Reference` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:17,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges Template` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:18,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_stock` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `write_off_outstanding_amount_automatically` tinyint NOT NULL DEFAULT 0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_billed_amount_in_sales_order` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `change_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `redeem_loyalty_points` tinyint NOT NULL DEFAULT 0, MODIFY `update_billed_amount_in_delivery_note` tinyint NOT NULL DEFAULT 1, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocate_advances_automatically` tinyint NOT NULL DEFAULT 0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_advance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_pos` tinyint NOT NULL DEFAULT 1, MODIFY `is_discounted` tinyint NOT NULL DEFAULT 0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `loyalty_points` int NOT NULL DEFAULT 0, MODIFY `write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_change_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:18,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Rate` MODIFY `tax_withholding_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `cumulative_threshold` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `single_threshold` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:18,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` MODIFY `enabled` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:18,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program Collection` MODIFY `collection_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_spent` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:18,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_exchange_gain_loss` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:19,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record Details` MODIFY `done` tinyint NOT NULL DEFAULT 0, MODIFY `no_of_docs` int NOT NULL DEFAULT 0
2025-04-22 15:36:19,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabCoupon Code` MODIFY `maximum_use` int NOT NULL DEFAULT 0, MODIFY `from_external_ecomm_platform` tinyint NOT NULL DEFAULT 0, MODIFY `used` int NOT NULL DEFAULT 0
2025-04-22 15:36:19,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Taxes` MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:19,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Payment Ledger` MODIFY `add_manually` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:19,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme` MODIFY `is_cumulative` tinyint NOT NULL DEFAULT 0, MODIFY `buying` tinyint NOT NULL DEFAULT 0, MODIFY `mixed_conditions` tinyint NOT NULL DEFAULT 0, MODIFY `disable` tinyint NOT NULL DEFAULT 0, MODIFY `selling` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:19,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_by_item_tax_template` tinyint NOT NULL DEFAULT 0, MODIFY `included_in_paid_amount` tinyint NOT NULL DEFAULT 0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:19,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Merge` MODIFY `is_group` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:19,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grant_commission` tinyint NOT NULL DEFAULT 0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `enable_deferred_revenue` tinyint NOT NULL DEFAULT 0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_item_scanned` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:20,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabBudget Account` MODIFY `budget_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:20,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Merge Accounts` MODIFY `merged` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:20,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Point Entry` MODIFY `loyalty_points` int NOT NULL DEFAULT 0, MODIFY `purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:20,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:20,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Statement Import` MODIFY `submit_after_import` tinyint NOT NULL DEFAULT 1, MODIFY `mute_emails` tinyint NOT NULL DEFAULT 1, MODIFY `custom_delimiters` tinyint NOT NULL DEFAULT 0, MODIFY `delimiter_options` varchar(140) DEFAULT ',;\\t|', MODIFY `show_failed_logs` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:20,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Rule` MODIFY `use_for_shopping_cart` tinyint NOT NULL DEFAULT 1, MODIFY `priority` int NOT NULL DEFAULT 1
2025-04-22 15:36:20,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Plan Detail` MODIFY `qty` int NOT NULL DEFAULT 0
2025-04-22 15:36:20,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Category` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:21,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Health Monitor Company` ADD INDEX `modified`(`modified`)
2025-04-22 15:36:21,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension Filter` MODIFY `apply_restriction_on_values` tinyint NOT NULL DEFAULT 1, MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:21,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Opening Entry` MODIFY `set_posting_date` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:21,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount Closing Balance` MODIFY `debit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_period_closing_voucher_entry` tinyint NOT NULL DEFAULT 0, MODIFY `credit` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:21,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabCashier Closing` MODIFY `expense` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `custody` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returns` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:21,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_debit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_credit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 0, MODIFY `is_system_generated` tinyint NOT NULL DEFAULT 0, MODIFY `multi_currency` tinyint NOT NULL DEFAULT 0, MODIFY `total_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:21,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Allowed Types` MODIFY `allowed` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:22,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Type` MODIFY `rate_of_interest` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `dunning_fee` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:22,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Detail` MODIFY `expected_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `closing_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:22,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabExchange Rate Revaluation` MODIFY `rounding_loss_allowance` decimal(21,9) NOT NULL DEFAULT 0.05, MODIFY `gain_loss_booked` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `gain_loss_unbooked` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:22,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Category` MODIFY `round_off_tax_amount` tinyint NOT NULL DEFAULT 0, MODIFY `consider_party_ledger_amount` tinyint NOT NULL DEFAULT 0, MODIFY `tax_on_excess_amount` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:22,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` MODIFY `difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `unreconciled_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reconciled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:22,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax Template` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:23,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ref_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:23,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Tax` MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:23,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Plan` MODIFY `billing_interval_count` int NOT NULL DEFAULT 1, MODIFY `cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:23,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpening Invoice Creation Tool Item` MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:23,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Health` MODIFY `debit_credit_mismatch` tinyint NOT NULL DEFAULT 0, MODIFY `general_and_payment_ledger_mismatch` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:23,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry` MODIFY `amount_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `delinked` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:23,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription` MODIFY `cancel_at_period_end` tinyint NOT NULL DEFAULT 0, MODIFY `follow_calendar_months` tinyint NOT NULL DEFAULT 0, MODIFY `days_until_due` int NOT NULL DEFAULT 0, MODIFY `submit_invoice` tinyint NOT NULL DEFAULT 1, MODIFY `generate_new_invoices_past_due_date` tinyint NOT NULL DEFAULT 0, MODIFY `additional_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `number_of_days` int NOT NULL DEFAULT 0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:24,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:24,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` MODIFY `is_company_account` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:24,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning` MODIFY `rate_of_interest` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `dunning_fee` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_interest` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_dunning_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `dunning_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:25,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_item_scanned` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `enable_deferred_revenue` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `grant_commission` tinyint NOT NULL DEFAULT 0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:25,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Template` MODIFY `multi_currency` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:26,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiscounted Invoice` MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:26,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabApplicable On Account` MODIFY `is_mandatory` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:26,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Price Discount` MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `validate_applied_rule` tinyint NOT NULL DEFAULT 0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_discount_on_rate` tinyint NOT NULL DEFAULT 0, MODIFY `apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0, MODIFY `disable` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:26,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Discounting` MODIFY `bank_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `loan_period` int NOT NULL DEFAULT 0, MODIFY `total_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:27,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` MODIFY `included_in_print_rate` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `included_in_paid_amount` tinyint NOT NULL DEFAULT 0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_by_item_tax_template` tinyint NOT NULL DEFAULT 0, MODIFY `tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `dont_recompute_tax` tinyint NOT NULL DEFAULT 0, MODIFY `base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:27,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabCashier Closing Payments` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:27,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabBudget` MODIFY `applicable_on_purchase_order` tinyint NOT NULL DEFAULT 0, MODIFY `applicable_on_booking_actual_expenses` tinyint NOT NULL DEFAULT 0, MODIFY `applicable_on_material_request` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:27,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabMonthly Distribution Percentage` MODIFY `percentage_allocation` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:27,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `expiry_duration` int NOT NULL DEFAULT 0, MODIFY `auto_opt_in` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:28,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `withdrawal` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `unallocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `deposit` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:28,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabCost Center` MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:36:28,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discounted_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_outstanding` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:28,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Product Discount` MODIFY `round_free_qty` tinyint NOT NULL DEFAULT 0, MODIFY `min_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `free_item_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `recurse_for` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `same_item` tinyint NOT NULL DEFAULT 0, MODIFY `free_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_recursion_over` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable` tinyint NOT NULL DEFAULT 0, MODIFY `apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0, MODIFY `is_recursive` tinyint NOT NULL DEFAULT 0, MODIFY `threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:28,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Letter Text` MODIFY `is_default_language` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:28,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `allocate_advances_automatically` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_default_payment_terms_template` tinyint NOT NULL DEFAULT 0, MODIFY `taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_outstanding_for_self` tinyint NOT NULL DEFAULT 1, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_transaction_date_exchange_rate` tinyint NOT NULL DEFAULT 0, MODIFY `tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_paid` tinyint NOT NULL DEFAULT 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_billed_amount_in_purchase_receipt` tinyint NOT NULL DEFAULT 1, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_stock` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `use_company_roundoff_cost_center` tinyint NOT NULL DEFAULT 0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `update_billed_amount_in_purchase_order` tinyint NOT NULL DEFAULT 0, MODIFY `only_include_allocated_payments` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_advance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_old_subcontracting_flow` tinyint NOT NULL DEFAULT 0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_internal_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_received` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `on_hold` tinyint NOT NULL DEFAULT 0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:29,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Payment Method` MODIFY `allow_in_returns` tinyint NOT NULL DEFAULT 0, MODIFY `default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:29,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Order Reference` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:29,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log` MODIFY `allocated` tinyint NOT NULL DEFAULT 0, MODIFY `total_allocations` int NOT NULL DEFAULT 0, MODIFY `reconciled` tinyint NOT NULL DEFAULT 0, MODIFY `reconciled_entries` int NOT NULL DEFAULT 0
2025-04-22 15:36:29,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `validity` int NOT NULL DEFAULT 0, MODIFY `charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_money` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:29,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` MODIFY `print_receipt_on_order_complete` tinyint NOT NULL DEFAULT 0, MODIFY `allow_discount_change` tinyint NOT NULL DEFAULT 0, MODIFY `disable_grand_total_to_default_mop` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `update_stock` tinyint NOT NULL DEFAULT 1, MODIFY `validate_stock_on_save` tinyint NOT NULL DEFAULT 0, MODIFY `auto_add_item_to_cart` tinyint NOT NULL DEFAULT 0, MODIFY `hide_images` tinyint NOT NULL DEFAULT 0, MODIFY `allow_rate_change` tinyint NOT NULL DEFAULT 0, MODIFY `hide_unavailable_items` tinyint NOT NULL DEFAULT 0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `write_off_limit` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:29,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:29,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Gateway Account` MODIFY `is_default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:30,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Field` MODIFY `read_only` tinyint NOT NULL DEFAULT 0, MODIFY `reqd` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:30,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `dont_enforce_free_item_qty` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `free_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `validate_applied_rule` tinyint NOT NULL DEFAULT 0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `recurse_for` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `same_item` tinyint NOT NULL DEFAULT 0, MODIFY `round_free_qty` tinyint NOT NULL DEFAULT 0, MODIFY `apply_discount_on_rate` tinyint NOT NULL DEFAULT 0, MODIFY `disable` tinyint NOT NULL DEFAULT 0, MODIFY `threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0, MODIFY `buying` tinyint NOT NULL DEFAULT 0, MODIFY `has_priority` tinyint NOT NULL DEFAULT 0, MODIFY `coupon_code_based` tinyint NOT NULL DEFAULT 0, MODIFY `is_cumulative` tinyint NOT NULL DEFAULT 0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `free_item_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_recursion_over` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_recursive` tinyint NOT NULL DEFAULT 0, MODIFY `mixed_conditions` tinyint NOT NULL DEFAULT 0, MODIFY `selling` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:30,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `included_in_paid_amount` tinyint NOT NULL DEFAULT 0, MODIFY `is_tax_withholding_account` tinyint NOT NULL DEFAULT 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `included_in_print_rate` tinyint NOT NULL DEFAULT 0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_by_item_tax_template` tinyint NOT NULL DEFAULT 0, MODIFY `base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:30,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabShareholder` MODIFY `is_company` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:30,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` MODIFY `exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ref_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:30,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabCheque Print Template` MODIFY `starting_position_from_top_edge` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_print_format` tinyint NOT NULL DEFAULT 0, MODIFY `acc_no_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 4.0, MODIFY `amt_in_figures_from_top_edge` decimal(21,9) NOT NULL DEFAULT 3.5, MODIFY `amt_in_word_width` decimal(21,9) NOT NULL DEFAULT 15.0, MODIFY `signatory_from_top_edge` decimal(21,9) NOT NULL DEFAULT 6.0, MODIFY `signatory_from_left_edge` decimal(21,9) NOT NULL DEFAULT 15.0, MODIFY `cheque_height` decimal(21,9) NOT NULL DEFAULT 9.0, MODIFY `amt_in_words_from_left_edge` decimal(21,9) NOT NULL DEFAULT 4.0, MODIFY `is_account_payable` tinyint NOT NULL DEFAULT 1, MODIFY `cheque_width` decimal(21,9) NOT NULL DEFAULT 20.0, MODIFY `amt_in_figures_from_left_edge` decimal(21,9) NOT NULL DEFAULT 16.0, MODIFY `amt_in_words_from_top_edge` decimal(21,9) NOT NULL DEFAULT 3.0, MODIFY `date_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 15.0, MODIFY `acc_no_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 5.0, MODIFY `acc_pay_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 9.0, MODIFY `amt_in_words_line_spacing` decimal(21,9) NOT NULL DEFAULT 0.5, MODIFY `date_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `payer_name_from_left_edge` decimal(21,9) NOT NULL DEFAULT 3.0, MODIFY `acc_pay_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `payer_name_from_top_edge` decimal(21,9) NOT NULL DEFAULT 2.0
2025-04-22 15:36:30,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabCost Center Allocation Percentage` MODIFY `percentage` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:30,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Terms Template Detail` MODIFY `discount_validity` int NOT NULL DEFAULT 0, MODIFY `discount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit_months` int NOT NULL DEFAULT 0, MODIFY `credit_days` int NOT NULL DEFAULT 0, MODIFY `invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:31,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Balance` MODIFY `is_company` tinyint NOT NULL DEFAULT 0, MODIFY `from_no` int NOT NULL DEFAULT 0, MODIFY `to_no` int NOT NULL DEFAULT 0, MODIFY `amount` int NOT NULL DEFAULT 0, MODIFY `rate` int NOT NULL DEFAULT 0, MODIFY `no_of_shares` int NOT NULL DEFAULT 0
2025-04-22 15:36:31,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Payments` MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:31,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year` MODIFY `is_short_year` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `auto_created` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:31,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabExchange Rate Revaluation Account` MODIFY `current_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `balance_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `zero_balance` tinyint NOT NULL DEFAULT 0, MODIFY `new_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `balance_in_base_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `new_balance_in_base_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `new_balance_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:31,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Payment` MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:32,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabOverdue Payment` MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discounted_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `dunning_level` int NOT NULL DEFAULT 1, MODIFY `payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `interest` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:32,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile User` MODIFY `default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:32,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabUnreconcile Payment Entries` MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `unlinked` tinyint NOT NULL DEFAULT 0
