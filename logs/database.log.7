2025-04-22 15:34:43,802 WARNING database DDL Query made to <PERSON>:
create table `tabBank` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`bank_name` varchar(140) UNIQUE,
`swift_number` varchar(140) UNIQUE,
`website` varchar(140),
`plaid_access_token` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,859 WARNING database DDL Query made to DB:
create table `tabTax Withheld Vouchers` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`voucher_type` varchar(140),
`voucher_name` varchar(140),
`taxable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,918 WARNING database DDL Query made to DB:
create table `tabMode of Payment Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`default_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:43,979 WARNING database DDL Query made to DB:
create table `tabPayment Gateway Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_gateway` varchar(140),
`payment_channel` varchar(140) DEFAULT 'Email',
`is_default` tinyint NOT NULL DEFAULT 0,
`payment_account` varchar(140),
`currency` varchar(140),
`message` text DEFAULT 'Please click on the link below to make your payment',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,044 WARNING database DDL Query made to DB:
create table `tabPricing Rule Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_group` varchar(140),
`uom` varchar(140),
index `item_group`(`item_group`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,109 WARNING database DDL Query made to DB:
create table `tabPOS Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` text,
`default_value` varchar(140),
`reqd` tinyint NOT NULL DEFAULT 0,
`read_only` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,161 WARNING database DDL Query made to DB:
create table `tabCurrency Exchange Settings Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,347 WARNING database DDL Query made to DB:
create table `tabPricing Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'PRLE-.####',
`title` varchar(140),
`disable` tinyint NOT NULL DEFAULT 0,
`apply_on` varchar(140) DEFAULT 'Item Code',
`price_or_product_discount` varchar(140),
`warehouse` varchar(140),
`mixed_conditions` tinyint NOT NULL DEFAULT 0,
`is_cumulative` tinyint NOT NULL DEFAULT 0,
`coupon_code_based` tinyint NOT NULL DEFAULT 0,
`apply_rule_on_other` varchar(140),
`other_item_code` varchar(140),
`other_item_group` varchar(140),
`other_brand` varchar(140),
`selling` tinyint NOT NULL DEFAULT 0,
`buying` tinyint NOT NULL DEFAULT 0,
`applicable_for` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`sales_partner` varchar(140),
`campaign` varchar(140),
`supplier` varchar(140),
`supplier_group` varchar(140),
`min_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`min_amt` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_amt` decimal(21,9) NOT NULL DEFAULT 0.0,
`same_item` tinyint NOT NULL DEFAULT 0,
`free_item` varchar(140),
`free_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`free_item_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`free_item_uom` varchar(140),
`round_free_qty` tinyint NOT NULL DEFAULT 0,
`dont_enforce_free_item_qty` tinyint NOT NULL DEFAULT 0,
`is_recursive` tinyint NOT NULL DEFAULT 0,
`recurse_for` decimal(21,9) NOT NULL DEFAULT 0.0,
`apply_recursion_over` decimal(21,9) NOT NULL DEFAULT 0.0,
`valid_from` date,
`valid_upto` date,
`company` varchar(140),
`currency` varchar(140),
`margin_type` varchar(140) DEFAULT 'Percentage',
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_or_discount` varchar(140) DEFAULT 'Discount Percentage',
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_price_list` varchar(140),
`condition` longtext,
`apply_multiple_pricing_rules` tinyint NOT NULL DEFAULT 0,
`apply_discount_on_rate` tinyint NOT NULL DEFAULT 0,
`threshold_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`validate_applied_rule` tinyint NOT NULL DEFAULT 0,
`rule_description` text,
`has_priority` tinyint NOT NULL DEFAULT 0,
`priority` varchar(140),
`promotional_scheme_id` varchar(140),
`promotional_scheme` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `warehouse`(`warehouse`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,467 WARNING database DDL Query made to DB:
create table `tabPurchase Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`category` varchar(140) DEFAULT 'Total',
`add_deduct_tax` varchar(140) DEFAULT 'Add',
`charge_type` varchar(140) DEFAULT 'On Net Total',
`row_id` varchar(140),
`included_in_print_rate` tinyint NOT NULL DEFAULT 0,
`included_in_paid_amount` tinyint NOT NULL DEFAULT 0,
`account_head` varchar(140),
`description` text,
`is_tax_withholding_account` tinyint NOT NULL DEFAULT 0,
`set_by_item_tax_template` tinyint NOT NULL DEFAULT 0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`account_currency` varchar(140),
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_amount_after_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_wise_tax_detail` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,538 WARNING database DDL Query made to DB:
create table `tabShareholder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`folio_no` varchar(140) UNIQUE,
`company` varchar(140),
`is_company` tinyint NOT NULL DEFAULT 0,
`contact_list` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,603 WARNING database DDL Query made to DB:
create table `tabPurchase Invoice Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`remarks` text,
`reference_row` varchar(140),
`advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`exchange_gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0,
`ref_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`difference_posting_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,691 WARNING database DDL Query made to DB:
create table `tabCheque Print Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`has_print_format` tinyint NOT NULL DEFAULT 0,
`bank_name` varchar(140) UNIQUE,
`cheque_size` varchar(140) DEFAULT 'Regular',
`starting_position_from_top_edge` decimal(21,9) NOT NULL DEFAULT 0.0,
`cheque_width` decimal(21,9) NOT NULL DEFAULT 20.0,
`cheque_height` decimal(21,9) NOT NULL DEFAULT 9.0,
`scanned_cheque` text,
`is_account_payable` tinyint NOT NULL DEFAULT 1,
`acc_pay_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 1.0,
`acc_pay_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 9.0,
`message_to_show` varchar(140) DEFAULT 'Acc. Payee',
`date_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 1.0,
`date_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 15.0,
`payer_name_from_top_edge` decimal(21,9) NOT NULL DEFAULT 2.0,
`payer_name_from_left_edge` decimal(21,9) NOT NULL DEFAULT 3.0,
`amt_in_words_from_top_edge` decimal(21,9) NOT NULL DEFAULT 3.0,
`amt_in_words_from_left_edge` decimal(21,9) NOT NULL DEFAULT 4.0,
`amt_in_word_width` decimal(21,9) NOT NULL DEFAULT 15.0,
`amt_in_words_line_spacing` decimal(21,9) NOT NULL DEFAULT 0.5,
`amt_in_figures_from_top_edge` decimal(21,9) NOT NULL DEFAULT 3.5,
`amt_in_figures_from_left_edge` decimal(21,9) NOT NULL DEFAULT 16.0,
`acc_no_dist_from_top_edge` decimal(21,9) NOT NULL DEFAULT 5.0,
`acc_no_dist_from_left_edge` decimal(21,9) NOT NULL DEFAULT 4.0,
`signatory_from_top_edge` decimal(21,9) NOT NULL DEFAULT 6.0,
`signatory_from_left_edge` decimal(21,9) NOT NULL DEFAULT 15.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,752 WARNING database DDL Query made to DB:
create table `tabCost Center Allocation Percentage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cost_center` varchar(140),
`percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,821 WARNING database DDL Query made to DB:
create table `tabPayment Terms Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_term` varchar(140),
`description` text,
`invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0,
`mode_of_payment` varchar(140),
`due_date_based_on` varchar(140),
`credit_days` int NOT NULL DEFAULT 0,
`credit_months` int NOT NULL DEFAULT 0,
`discount_type` varchar(140) DEFAULT 'Percentage',
`discount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_validity_based_on` varchar(140) DEFAULT 'Day(s) after invoice date',
`discount_validity` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,873 WARNING database DDL Query made to DB:
create table `tabCampaign Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`campaign` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,943 WARNING database DDL Query made to DB:
create table `tabShare Balance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`share_type` varchar(140),
`from_no` int NOT NULL DEFAULT 0,
`rate` int NOT NULL DEFAULT 0,
`no_of_shares` int NOT NULL DEFAULT 0,
`to_no` int NOT NULL DEFAULT 0,
`amount` int NOT NULL DEFAULT 0,
`is_company` tinyint NOT NULL DEFAULT 0,
`current_state` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:44,999 WARNING database DDL Query made to DB:
create table `tabBank Transaction Payments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_document` varchar(140),
`payment_entry` varchar(140),
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,050 WARNING database DDL Query made to DB:
create table `tabJournal Entry Template Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,118 WARNING database DDL Query made to DB:
create table `tabCost Center Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`main_cost_center` varchar(140),
`company` varchar(140),
`valid_from` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,237 WARNING database DDL Query made to DB:
create table `tabFiscal Year` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`year` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`is_short_year` tinyint NOT NULL DEFAULT 0,
`year_start_date` date,
`year_end_date` date,
`auto_created` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,352 WARNING database DDL Query made to DB:
create table `tabPOS Customer Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`customer_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,419 WARNING database DDL Query made to DB:
create table `tabExchange Rate Revaluation Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`account_currency` varchar(140),
`balance_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`new_balance_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`current_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`new_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`balance_in_base_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`new_balance_in_base_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`gain_loss` decimal(21,9) NOT NULL DEFAULT 0.0,
`zero_balance` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,482 WARNING database DDL Query made to DB:
create table `tabSales Invoice Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`default` tinyint NOT NULL DEFAULT 0,
`mode_of_payment` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`reference_no` varchar(140),
`account` varchar(140),
`type` varchar(140),
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`clearance_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,553 WARNING database DDL Query made to DB:
create table `tabOverdue Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_invoice` varchar(140),
`payment_schedule` varchar(140),
`dunning_level` int NOT NULL DEFAULT 1,
`payment_term` varchar(140),
`description` text,
`due_date` date,
`overdue_days` varchar(140),
`mode_of_payment` varchar(140),
`invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0,
`payment_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discounted_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`interest` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,657 WARNING database DDL Query made to DB:
create table `tabParty Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`account` varchar(140),
`advance_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,709 WARNING database DDL Query made to DB:
create table `tabPOS Profile User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`default` tinyint NOT NULL DEFAULT 0,
`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,770 WARNING database DDL Query made to DB:
create table `tabUnreconcile Payment Entries` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_currency` varchar(140),
`unlinked` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:45,837 WARNING database DDL Query made to DB:
create table `tabProcess Subscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`subscription` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,178 WARNING database DDL Query made to DB:
create table `tabSales Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` text,
`tax_id` varchar(140),
`company` varchar(140),
`company_tax_id` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` tinyint NOT NULL DEFAULT 0,
`due_date` date,
`is_pos` tinyint NOT NULL DEFAULT 0,
`pos_profile` varchar(140),
`is_consolidated` tinyint NOT NULL DEFAULT 0,
`is_return` tinyint NOT NULL DEFAULT 0,
`return_against` varchar(140),
`update_outstanding_for_self` tinyint NOT NULL DEFAULT 1,
`update_billed_amount_in_sales_order` tinyint NOT NULL DEFAULT 0,
`update_billed_amount_in_delivery_note` tinyint NOT NULL DEFAULT 1,
`is_debit_note` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`update_stock` tinyint NOT NULL DEFAULT 0,
`set_warehouse` varchar(140),
`set_target_warehouse` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` text,
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`use_company_roundoff_cost_center` tinyint NOT NULL DEFAULT 0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` text,
`total_advance` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`apply_discount_on` varchar(15) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`coupon_code` varchar(140),
`is_cash_or_non_trade_discount` tinyint NOT NULL DEFAULT 0,
`additional_discount_account` varchar(140),
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`other_charges_calculation` longtext,
`total_billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`cash_bank_account` varchar(140),
`base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_change_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`change_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`account_for_change_amount` varchar(140),
`allocate_advances_automatically` tinyint NOT NULL DEFAULT 0,
`only_include_allocated_payments` tinyint NOT NULL DEFAULT 0,
`write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`write_off_outstanding_amount_automatically` tinyint NOT NULL DEFAULT 0,
`write_off_account` varchar(140),
`write_off_cost_center` varchar(140),
`redeem_loyalty_points` tinyint NOT NULL DEFAULT 0,
`loyalty_points` int NOT NULL DEFAULT 0,
`loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`loyalty_program` varchar(140),
`dont_create_loyalty_points` tinyint NOT NULL DEFAULT 0,
`loyalty_redemption_account` varchar(140),
`loyalty_redemption_cost_center` varchar(140),
`customer_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`territory` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` longtext,
`dispatch_address_name` varchar(140),
`dispatch_address` longtext,
`company_address` varchar(140),
`company_address_display` longtext,
`company_contact_person` varchar(140),
`ignore_default_payment_terms_template` tinyint NOT NULL DEFAULT 0,
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`po_no` varchar(140),
`po_date` date,
`debit_to` varchar(140),
`party_account_currency` varchar(140),
`is_opening` varchar(4) DEFAULT 'No',
`unrealized_profit_loss_account` varchar(140),
`against_income_account` text,
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`subscription` varchar(140),
`from_date` date,
`auto_repeat` varchar(140),
`to_date` date,
`status` varchar(30) DEFAULT 'Draft',
`inter_company_invoice_reference` varchar(140),
`represents_company` varchar(140),
`customer_group` varchar(140),
`utm_source` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`utm_content` varchar(140),
`is_internal_customer` tinyint NOT NULL DEFAULT 0,
`is_discounted` tinyint NOT NULL DEFAULT 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `posting_date`(`posting_date`),
index `return_against`(`return_against`),
index `project`(`project`),
index `debit_to`(`debit_to`),
index `inter_company_invoice_reference`(`inter_company_invoice_reference`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,300 WARNING database DDL Query made to DB:
create table `tabJournal Entry Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account` varchar(140),
`account_type` varchar(140),
`bank_account` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`account_currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit` decimal(21,9) NOT NULL DEFAULT 0.0,
`reference_type` varchar(140),
`reference_name` varchar(140),
`reference_due_date` date,
`reference_detail_no` varchar(140),
`is_advance` varchar(140),
`user_remark` text,
`against_account` text,
index `account`(`account`),
index `party_type`(`party_type`),
index `reference_type`(`reference_type`),
index `reference_name`(`reference_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,412 WARNING database DDL Query made to DB:
create table `tabAdvance Payment Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`against_voucher_type` varchar(140),
`against_voucher_no` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`event` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,474 WARNING database DDL Query made to DB:
create table `tabSales Taxes and Charges Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`is_default` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`tax_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,531 WARNING database DDL Query made to DB:
create table `tabPSOA Project` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`project_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,587 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts Customer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`customer` varchar(140),
`customer_name` varchar(140),
`billing_email` varchar(140),
`primary_email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,727 WARNING database DDL Query made to DB:
create table `tabPayment Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_request_type` varchar(140) DEFAULT 'Inward',
`transaction_date` date,
`failed_reason` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`mode_of_payment` varchar(140),
`party_type` varchar(140),
`party` varchar(140),
`party_name` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`is_a_subscription` tinyint NOT NULL DEFAULT 0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`party_account_currency` varchar(140),
`bank_account` varchar(140),
`bank` varchar(140),
`bank_account_no` varchar(140),
`account` varchar(140),
`iban` varchar(140),
`branch_code` varchar(140),
`swift_number` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`print_format` varchar(140),
`email_to` varchar(140),
`subject` varchar(140),
`payment_gateway_account` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`make_sales_invoice` tinyint NOT NULL DEFAULT 0,
`message` text,
`mute_email` tinyint NOT NULL DEFAULT 0,
`payment_gateway` varchar(140),
`payment_account` varchar(140),
`payment_channel` varchar(140),
`payment_order` varchar(140),
`amended_from` varchar(140),
`payment_url` varchar(500),
`phone_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,783 WARNING database DDL Query made to DB:
create table `tabClosed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`closed` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,838 WARNING database DDL Query made to DB:
create table `tabPricing Rule Brand` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`brand` varchar(140),
`uom` varchar(140),
index `brand`(`brand`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,905 WARNING database DDL Query made to DB:
create table `tabBank Account Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account_type` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:46,973 WARNING database DDL Query made to DB:
create table `tabPricing Rule Item Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`uom` varchar(140),
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,138 WARNING database DDL Query made to DB:
create table `tabRepost Accounting Ledger` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`delete_cancelled_entries` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,243 WARNING database DDL Query made to DB:
create table `tabAccounting Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`period_name` varchar(140) UNIQUE,
`start_date` date,
`end_date` date,
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,302 WARNING database DDL Query made to DB:
create table `tabLoyalty Point Entry Redemption` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_invoice` varchar(140),
`redemption_date` date,
`redeemed_points` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,352 WARNING database DDL Query made to DB:
create table `tabAllowed To Transact With` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,455 WARNING database DDL Query made to DB:
create table `tabPOS Opening Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`mode_of_payment` varchar(140),
`opening_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,552 WARNING database DDL Query made to DB:
create table `tabShare Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`transfer_type` varchar(140),
`date` date,
`from_shareholder` varchar(140),
`from_folio_no` varchar(140),
`to_shareholder` varchar(140),
`to_folio_no` varchar(140),
`equity_or_liability_account` varchar(140),
`asset_account` varchar(140),
`share_type` varchar(140),
`from_no` int NOT NULL DEFAULT 0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`no_of_shares` int NOT NULL DEFAULT 0,
`to_no` int NOT NULL DEFAULT 0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`company` varchar(140),
`remarks` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,621 WARNING database DDL Query made to DB:
create table `tabAccounting Dimension Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`reference_document` varchar(140),
`default_dimension` varchar(140),
`mandatory_for_bs` tinyint NOT NULL DEFAULT 0,
`mandatory_for_pl` tinyint NOT NULL DEFAULT 0,
`automatically_post_balancing_accounting_entry` tinyint NOT NULL DEFAULT 0,
`offsetting_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:47,687 WARNING database DDL Query made to DB:
create table `tabBank Account Subtype` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account_subtype` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:48,487 WARNING database DDL Query made to DB:
create sequence if not exists crm_note_id_seq nocache nocycle
2025-04-22 15:34:48,506 WARNING database DDL Query made to DB:
create table `tabCRM Note` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`note` longtext,
`added_by` varchar(140),
`added_on` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:48,560 WARNING database DDL Query made to DB:
create table `tabAvailability Of Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:48,627 WARNING database DDL Query made to DB:
create table `tabCampaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`campaign_name` varchar(140),
`naming_series` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:48,786 WARNING database DDL Query made to DB:
create table `tabCompetitor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`competitor_name` varchar(140) UNIQUE,
`website` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:48,895 WARNING database DDL Query made to DB:
create table `tabCompetitor Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`competitor` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,041 WARNING database DDL Query made to DB:
create table `tabLead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`salutation` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`lead_name` varchar(140),
`job_title` varchar(140),
`gender` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140) DEFAULT 'Lead',
`customer` varchar(140),
`type` varchar(140),
`request_type` varchar(140),
`email_id` varchar(140),
`website` varchar(140),
`mobile_no` varchar(140),
`whatsapp_no` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`company_name` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`industry` varchar(140),
`market_segment` varchar(140),
`territory` varchar(140),
`fax` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`utm_source` varchar(140),
`utm_content` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`qualification_status` varchar(140),
`qualified_by` varchar(140),
`qualified_on` date,
`company` varchar(140),
`language` varchar(140),
`image` text,
`title` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`unsubscribed` tinyint NOT NULL DEFAULT 0,
`blog_subscriber` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lead_name`(`lead_name`),
index `lead_owner`(`lead_owner`),
index `status`(`status`),
index `email_id`(`email_id`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,161 WARNING database DDL Query made to DB:
create table `tabProspect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company_name` varchar(140) UNIQUE,
`customer_group` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`market_segment` varchar(140),
`industry` varchar(140),
`territory` varchar(140),
`prospect_owner` varchar(140),
`website` varchar(140),
`fax` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,241 WARNING database DDL Query made to DB:
create table `tabCampaign Email Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_template` varchar(140),
`send_after_days` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,294 WARNING database DDL Query made to DB:
create table `tabContract Template Fulfilment Terms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`requirement` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,366 WARNING database DDL Query made to DB:
create table `tabProspect Lead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lead` varchar(140),
`lead_name` varchar(140),
`email` varchar(140),
`mobile_no` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,438 WARNING database DDL Query made to DB:
create table `tabEmail Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`campaign_name` varchar(140),
`email_campaign_for` varchar(140) DEFAULT 'Lead',
`recipient` varchar(140),
`sender` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,512 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lost_reason` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,615 WARNING database DDL Query made to DB:
create table `tabContract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`party_type` varchar(140) DEFAULT 'Customer',
`is_signed` tinyint NOT NULL DEFAULT 0,
`party_name` varchar(140),
`party_user` varchar(140),
`status` varchar(140),
`fulfilment_status` varchar(140),
`start_date` date,
`end_date` date,
`signee` varchar(140),
`signed_on` datetime(6),
`ip_address` varchar(140),
`contract_template` varchar(140),
`contract_terms` longtext,
`requires_fulfilment` tinyint NOT NULL DEFAULT 0,
`fulfilment_deadline` date,
`signee_company` longtext,
`signed_by_company` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,698 WARNING database DDL Query made to DB:
create table `tabLost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,751 WARNING database DDL Query made to DB:
create table `tabAppointment Booking Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,813 WARNING database DDL Query made to DB:
create table `tabContract Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`contract_terms` longtext,
`requires_fulfilment` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,891 WARNING database DDL Query made to DB:
create table `tabOpportunity Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`brand` varchar(140),
`item_group` varchar(140),
`description` longtext,
`image` text,
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:49,959 WARNING database DDL Query made to DB:
create table `tabContract Fulfilment Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fulfilled` tinyint NOT NULL DEFAULT 0,
`requirement` varchar(140),
`notes` text,
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,012 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,067 WARNING database DDL Query made to DB:
create table `tabOpportunity Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,153 WARNING database DDL Query made to DB:
create table `tabAppointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`scheduled_time` datetime(6),
`status` varchar(140),
`customer_name` varchar(140),
`customer_phone_number` varchar(140),
`customer_skype` varchar(140),
`customer_email` varchar(140),
`customer_details` longtext,
`appointment_with` varchar(140),
`party` varchar(140),
`calendar_event` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,225 WARNING database DDL Query made to DB:
create table `tabMarket Segment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`market_segment` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,288 WARNING database DDL Query made to DB:
create sequence if not exists prospect_opportunity_id_seq nocache nocycle
2025-04-22 15:34:50,310 WARNING database DDL Query made to DB:
create table `tabProspect Opportunity` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`opportunity` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`stage` varchar(140),
`deal_owner` varchar(140),
`probability` decimal(21,9) NOT NULL DEFAULT 0.0,
`expected_closing` date,
`currency` varchar(140),
`contact_person` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,369 WARNING database DDL Query made to DB:
create table `tabSales Stage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`stage_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,585 WARNING database DDL Query made to DB:
create table `tabOpportunity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`opportunity_from` varchar(140),
`party_name` varchar(140),
`customer_name` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`opportunity_type` varchar(140) DEFAULT 'Sales',
`opportunity_owner` varchar(140),
`sales_stage` varchar(140) DEFAULT 'Prospecting',
`expected_closing` date,
`probability` decimal(21,9) NOT NULL DEFAULT 100.0,
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`customer_group` varchar(140),
`industry` varchar(140),
`market_segment` varchar(140),
`website` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`territory` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`opportunity_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_opportunity_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`utm_source` varchar(140),
`utm_content` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`company` varchar(140),
`transaction_date` date,
`language` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`first_response_time` decimal(21,9),
`order_lost_reason` text,
`contact_person` varchar(140),
`job_title` varchar(140),
`contact_email` varchar(140),
`contact_mobile` varchar(140),
`whatsapp` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`customer_address` varchar(140),
`address_display` longtext,
`contact_display` text,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer_group`(`customer_group`),
index `territory`(`territory`),
index `company`(`company`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,798 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item Supplied` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`reserve_warehouse` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:50,945 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`billing_address` varchar(140),
`billing_address_display` longtext,
`vendor` varchar(140),
`transaction_date` date,
`schedule_date` date,
`status` varchar(140),
`amended_from` varchar(140),
`email_template` varchar(140),
`send_attached_files` tinyint NOT NULL DEFAULT 1,
`send_document_print` tinyint NOT NULL DEFAULT 0,
`message_for_supplier` longtext,
`incoterm` varchar(140),
`named_place` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`opportunity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,076 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`standing_name` varchar(140),
`standing_color` varchar(140),
`min_grade` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_grade` decimal(21,9) NOT NULL DEFAULT 0.0,
`warn_rfqs` tinyint NOT NULL DEFAULT 0,
`warn_pos` tinyint NOT NULL DEFAULT 0,
`prevent_rfqs` tinyint NOT NULL DEFAULT 0,
`prevent_pos` tinyint NOT NULL DEFAULT 0,
`notify_supplier` tinyint NOT NULL DEFAULT 0,
`notify_employee` tinyint NOT NULL DEFAULT 0,
`employee_link` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,252 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`brand` varchar(140),
`product_bundle` varchar(140),
`schedule_date` date,
`expected_delivery_date` date,
`item_group` varchar(140),
`description` longtext,
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`subcontracted_quantity` decimal(21,9) NOT NULL DEFAULT 0.0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`last_purchase_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`apply_tds` tinyint NOT NULL DEFAULT 1,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`material_request` varchar(140),
`material_request_item` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`sales_order_packed_item` varchar(140),
`supplier_quotation` varchar(140),
`supplier_quotation_item` varchar(140),
`delivered_by_supplier` tinyint NOT NULL DEFAULT 0,
`against_blanket_order` tinyint NOT NULL DEFAULT 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`received_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0,
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`bom` varchar(140),
`include_exploded_items` tinyint NOT NULL DEFAULT 0,
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`is_fixed_asset` tinyint NOT NULL DEFAULT 0,
`item_tax_rate` longtext,
`production_plan` varchar(140),
`production_plan_item` varchar(140),
`production_plan_sub_assembly_item` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
`job_card` varchar(140),
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `sales_order`(`sales_order`),
index `sales_order_item`(`sales_order_item`),
index `job_card`(`job_card`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-04-22 15:34:51,495 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`transaction_date` date,
`valid_till` date,
`quotation_number` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` varchar(240),
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address` varchar(140),
`shipping_address_display` longtext,
`billing_address` varchar(140),
`billing_address_display` longtext,
`tc_name` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`auto_repeat` varchar(140),
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`opportunity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `company`(`company`),
index `status`(`status`),
index `transaction_date`(`transaction_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,643 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`criteria_name` varchar(140) UNIQUE,
`max_score` decimal(21,9) NOT NULL DEFAULT 100.0,
`formula` text,
`weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,730 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`schedule_date` date,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`warehouse` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`project_name` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,788 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`variable_label` varchar(140),
`description` text,
`value` decimal(21,9) NOT NULL DEFAULT 0.0,
`param_name` varchar(140),
`path` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,858 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`criteria_name` varchar(140),
`score` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_score` decimal(21,9) NOT NULL DEFAULT 100.0,
`formula` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:51,935 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`variable_label` varchar(140) UNIQUE,
`is_custom` tinyint NOT NULL DEFAULT 0,
`param_name` varchar(140) UNIQUE,
`path` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,016 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier` varchar(140) UNIQUE,
`supplier_score` varchar(140),
`indicator_color` varchar(140),
`status` varchar(140),
`period` varchar(140) DEFAULT 'Per Month',
`weighting_function` text DEFAULT '{total_score} * max( 0, min ( 1 , (12 - {period_number}) / 12) )',
`warn_rfqs` tinyint NOT NULL DEFAULT 0,
`warn_pos` tinyint NOT NULL DEFAULT 0,
`prevent_rfqs` tinyint NOT NULL DEFAULT 0,
`prevent_pos` tinyint NOT NULL DEFAULT 0,
`notify_supplier` tinyint NOT NULL DEFAULT 0,
`notify_employee` tinyint NOT NULL DEFAULT 0,
`employee` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,105 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier` varchar(140),
`naming_series` varchar(140),
`total_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`start_date` date,
`end_date` date,
`scorecard` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,175 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`standing_name` varchar(140) UNIQUE,
`standing_color` varchar(140),
`min_grade` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_grade` decimal(21,9) NOT NULL DEFAULT 0.0,
`warn_rfqs` tinyint NOT NULL DEFAULT 0,
`warn_pos` tinyint NOT NULL DEFAULT 0,
`prevent_rfqs` tinyint NOT NULL DEFAULT 0,
`prevent_pos` tinyint NOT NULL DEFAULT 0,
`notify_supplier` tinyint NOT NULL DEFAULT 0,
`notify_employee` tinyint NOT NULL DEFAULT 0,
`employee_link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,321 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`lead_time_days` int NOT NULL DEFAULT 0,
`expected_delivery_date` date,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`prevdoc_doctype` varchar(140),
`material_request` varchar(140),
`sales_order` varchar(140),
`request_for_quotation` varchar(140),
`material_request_item` varchar(140),
`request_for_quotation_item` varchar(140),
`item_tax_rate` longtext,
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
index `material_request`(`material_request`),
index `sales_order`(`sales_order`),
index `material_request_item`(`material_request_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,443 WARNING database DDL Query made to DB:
create table `tabSupplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`supplier_name` varchar(140),
`country` varchar(140),
`supplier_group` varchar(140),
`supplier_type` varchar(140) DEFAULT 'Company',
`is_transporter` tinyint NOT NULL DEFAULT 0,
`image` text,
`default_currency` varchar(140),
`default_bank_account` varchar(140),
`default_price_list` varchar(140),
`is_internal_supplier` tinyint NOT NULL DEFAULT 0,
`represents_company` varchar(140),
`supplier_details` text,
`website` varchar(140),
`language` varchar(140),
`tax_id` varchar(140),
`tax_category` varchar(140),
`tax_withholding_category` varchar(140),
`supplier_primary_address` varchar(140),
`primary_address` text,
`supplier_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`payment_terms` varchar(140),
`allow_purchase_invoice_creation_without_purchase_order` tinyint NOT NULL DEFAULT 0,
`allow_purchase_invoice_creation_without_purchase_receipt` tinyint NOT NULL DEFAULT 0,
`is_frozen` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`warn_rfqs` tinyint NOT NULL DEFAULT 0,
`warn_pos` tinyint NOT NULL DEFAULT 0,
`prevent_rfqs` tinyint NOT NULL DEFAULT 0,
`prevent_pos` tinyint NOT NULL DEFAULT 0,
`on_hold` tinyint NOT NULL DEFAULT 0,
`hold_type` varchar(140),
`release_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,692 WARNING database DDL Query made to DB:
create table `tabPurchase Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`order_confirmation_no` varchar(140),
`order_confirmation_date` date,
`transaction_date` date,
`schedule_date` date,
`company` varchar(140),
`apply_tds` tinyint NOT NULL DEFAULT 0,
`tax_withholding_category` varchar(140),
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`supplier_warehouse` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`set_from_warehouse` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`set_reserve_warehouse` varchar(140),
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0,
`taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(240),
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`in_words` varchar(240),
`advance_paid` decimal(21,9) NOT NULL DEFAULT 0.0,
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` longtext,
`billing_address` varchar(140),
`billing_address_display` longtext,
`customer` varchar(140),
`customer_name` varchar(140),
`customer_contact_person` varchar(140),
`customer_contact_display` text,
`customer_contact_mobile` text,
`customer_contact_email` longtext,
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) DEFAULT 'Draft',
`advance_payment_status` varchar(140),
`per_billed` decimal(21,9) NOT NULL DEFAULT 0.0,
`per_received` decimal(21,9) NOT NULL DEFAULT 0.0,
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`from_date` date,
`to_date` date,
`auto_repeat` varchar(140),
`is_internal_supplier` tinyint NOT NULL DEFAULT 0,
`represents_company` varchar(140),
`ref_sq` varchar(140),
`party_account_currency` varchar(140),
`inter_company_order_reference` varchar(140),
`is_old_subcontracting_flow` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,774 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Supplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier` varchar(140),
`contact` varchar(140),
`quote_status` varchar(140),
`supplier_name` varchar(140),
`email_id` varchar(140),
`send_email` tinyint NOT NULL DEFAULT 1,
`email_sent` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:52,865 WARNING database DDL Query made to DB:
create table `tabPurchase Receipt Item Supplied` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`reference_name` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`current_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`batch_no` varchar(140),
`serial_no` text,
`purchase_order` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,044 WARNING database DDL Query made to DB:
create table `tabActivity Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`activity_type` varchar(140) UNIQUE,
`costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,107 WARNING database DDL Query made to DB:
create table `tabProject Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`task` varchar(140),
`subject` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,215 WARNING database DDL Query made to DB:
create table `tabTimesheet Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`activity_type` varchar(140),
`from_time` datetime(6),
`description` text,
`expected_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`to_time` datetime(6),
`hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`completed` tinyint NOT NULL DEFAULT 0,
`project` varchar(140),
`project_name` varchar(140),
`task` varchar(140),
`is_billable` tinyint NOT NULL DEFAULT 0,
`sales_invoice` varchar(140),
`billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,285 WARNING database DDL Query made to DB:
create table `tabProject User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`email` varchar(140),
`image` varchar(140),
`full_name` varchar(140),
`welcome_email_sent` tinyint NOT NULL DEFAULT 0,
`view_attachments` tinyint NOT NULL DEFAULT 0,
`hide_timesheets` tinyint NOT NULL DEFAULT 0,
`project_status` text,
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,403 WARNING database DDL Query made to DB:
create table `tabTask` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` varchar(140),
`project` varchar(140),
`issue` varchar(140),
`type` varchar(140),
`color` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`is_template` tinyint NOT NULL DEFAULT 0,
`status` varchar(140),
`priority` varchar(140),
`task_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`parent_task` varchar(140),
`completed_by` varchar(140),
`completed_on` date,
`exp_start_date` datetime(6),
`expected_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`start` int NOT NULL DEFAULT 0,
`exp_end_date` datetime(6),
`progress` decimal(21,9) NOT NULL DEFAULT 0.0,
`duration` int NOT NULL DEFAULT 0,
`is_milestone` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`depends_on_tasks` longtext,
`act_start_date` date,
`actual_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`act_end_date` date,
`total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`review_date` date,
`closing_date` date,
`department` varchar(140),
`company` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`template_task` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `subject`(`subject`),
index `project`(`project`),
index `priority`(`priority`),
index `parent_task`(`parent_task`),
index `exp_end_date`(`exp_end_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:53,510 WARNING database DDL Query made to DB:
create table `tabActivity Cost` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`activity_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,588 WARNING database DDL Query made to DB:
create table `tabDependent Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`task` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,729 WARNING database DDL Query made to DB:
create table `tabTimesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) DEFAULT '{employee_name}',
`naming_series` varchar(140),
`company` varchar(140),
`customer` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 1.0,
`sales_invoice` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`parent_project` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`total_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billable_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billed_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`per_billed` decimal(21,9) NOT NULL DEFAULT 0.0,
`note` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,804 WARNING database DDL Query made to DB:
create table `tabTask Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,871 WARNING database DDL Query made to DB:
create table `tabProject Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`project_type` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:53,934 WARNING database DDL Query made to DB:
create table `tabProject Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`project_type` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
