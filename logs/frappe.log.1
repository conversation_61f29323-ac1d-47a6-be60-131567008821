2025-04-25 12:33:58,462 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:58,635 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:58,637 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:58,863 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:58,865 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:59,062 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 12:33:59,065 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 246, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'webshop'
2025-04-25 13:41:33,401 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'is_private': 'NaN', 'folder': 'Home', 'file_url': 'https://store.storeimages.cdn-apple.com/1/as-images.apple.com/is/iphone-15-model-unselect-gallery-1-202309?wid=5120&hei=2880&fmt=webp&qlt=70&.v=aVFiZEF4WDEvUWJNSU5HRDg4cklnTGdzSmpObkZCM3MrNmJ5SkhESlNDZ1UwRE05YU1MZ0lYWk55ZU5FOENXWkpFd0xhWDVibStLdGRYRmxkNGI4VTdpMGJRT0ppMjh4RlRZQkc0Q3FZZEI4UW55RWdXT3BFc2NrR2VEb1pCOGo&traceId=1', 'doctype': 'Item', 'docname': 'Iphone 15', 'fieldname': 'image', 'cmd': 'upload_file'}
2025-04-25 16:25:23,401 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'Item Group', 'name': 'new-item-group-anovahyzkx', 'cmd': 'frappe.realtime.has_permission'}
2025-04-25 16:26:45,042 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doc': '{"name":"Iphone 15","owner":"<EMAIL>","creation":"2025-04-25 13:39:46.392901","modified":"2025-04-25 13:42:46.977955","modified_by":"<EMAIL>","docstatus":0,"idx":0,"naming_series":"STO-ITEM-.YYYY.-","item_code":"Iphone 15","item_name":"Iphone 15","item_group":"Phone and Accessories","stock_uom":"Nos","disabled":0,"allow_alternative_item":0,"is_stock_item":1,"has_variants":0,"opening_stock":0,"valuation_rate":2500000,"standard_rate":0,"is_fixed_asset":0,"auto_create_assets":0,"is_grouped_asset":0,"over_delivery_receipt_allowance":0,"over_billing_allowance":0,"description":"Iphone 15","shelf_life_in_days":0,"end_of_life":"2099-12-31","default_material_request_type":"Purchase","valuation_method":"","weight_per_unit":0,"allow_negative_stock":0,"has_batch_no":0,"create_new_batch":0,"has_expiry_date":0,"retain_sample":0,"sample_quantity":0,"has_serial_no":0,"variant_based_on":"Item Attribute","enable_deferred_expense":0,"no_of_months_exp":0,"enable_deferred_revenue":0,"no_of_months":0,"min_order_qty":0,"safety_stock":0,"is_purchase_item":1,"lead_time_days":0,"last_purchase_rate":0,"is_customer_provided_item":0,"delivered_by_supplier":0,"country_of_origin":"Tanzania","grant_commission":1,"is_sales_item":1,"max_discount":0,"inspection_required_before_purchase":0,"inspection_required_before_delivery":0,"include_item_in_manufacturing":1,"is_sub_contracted_item":0,"customer_code":"","published_in_website":0,"total_projected_qty":0,"doctype":"Item","uoms":[{"name":"03ok4nceoq","owner":"<EMAIL>","creation":"2025-04-25 13:39:46.439581","modified":"2025-04-25 13:42:46.977955","modified_by":"<EMAIL>","docstatus":0,"idx":1,"uom":"Nos","conversion_factor":1,"parent":"Iphone 15","parentfield":"uoms","parenttype":"Item","doctype":"UOM Conversion Detail"}],"barcodes":[],"reorder_levels":[],"attributes":[],"item_defaults":[{"name":"03ol55taj5","owner":"<EMAIL>","creation":"2025-04-25 13:39:46.449485","modified":"2025-04-25 13:42:46.977955","modified_by":"<EMAIL>","docstatus":0,"idx":1,"company":"E-Commerce System","default_warehouse":"Stores - ES","parent":"Iphone 15","parentfield":"item_defaults","parenttype":"Item","doctype":"Item Default"}],"supplier_items":[],"customer_items":[],"taxes":[],"__onload":{"stock_exists":0,"asset_naming_series":"ACC-ASS-.YYYY.-"},"image":"/files/Iphone15.webp","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-25 17:19:09,432 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'doctype': 'Item Price', 'name': 'new-item-price-jhsmmsbkku', 'cmd': 'frappe.realtime.has_permission'}
2025-04-25 17:29:18,474 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'item_code': 'Iphone 15', 'qty': '1', 'with_items': '0', 'cmd': 'webshop.webshop.shopping_cart.cart.update_cart'}
2025-04-25 17:29:41,520 ERROR frappe New Exception collected in error log
Site: work
Form Dict: {'item_code': 'Iphone 15', 'qty': '1', 'with_items': '0', 'cmd': 'webshop.webshop.shopping_cart.cart.update_cart'}
2025-04-28 14:27:06,441 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-04-28 14:27:06,445 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-04-28 14:27:13,341 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-04-28 14:27:13,345 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 97, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 270, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1374, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1215, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 190, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 202, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1180, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1210, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1151, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-07-01 13:20:29,553 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:29,561 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:33,775 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:33,782 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,010 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,017 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,225 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,228 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,457 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
2025-07-01 13:20:35,468 ERROR frappe Unable to load translations
Site: work
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 101, in application
    init_request(request)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/app.py", line 174, in init_request
    frappe.init(site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 275, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1389, in setup_module_map
    apps = get_installed_apps(_ensure_on_bench=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 65, in wrapper
    return _cache[func][args_key]
           ~~~~~~~~~~~~^^^^^^^^^^
KeyError: ((), <object object at 0x73acb7924f70>, frozenset({('_ensure_on_bench', True)}))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 253, in hget
    value = generator()
            ^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/translate.py", line 189, in get_translations_from_apps
    for app in apps or frappe.get_installed_apps(_ensure_on_bench=True):
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/utils/caching.py", line 71, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 1267, in get_installed_apps
    connect()
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/__init__.py", line 321, in connect
    local.db = get_db(
               ^^^^^^^
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/__init__.py", line 79, in get_db
    import frappe.database.mariadb.mysqlclient
  File "/home/<USER>/Desktop/work-bench/apps/frappe/frappe/database/mariadb/mysqlclient.py", line 4, in <module>
    import MySQLdb
  File "/home/<USER>/Desktop/work-bench/env/lib/python3.12/site-packages/MySQLdb/__init__.py", line 17, in <module>
    from . import _mysql
ImportError: libmysqlclient.so.20: cannot open shared object file: No such file or directory
