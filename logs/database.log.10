2025-04-22 15:32:34,962 WARNING database DDL Query made to DB:
CREATE USER IF NOT EXISTS '_a9a78d24ac870bfb'@'localhost' IDENTIFIED BY 'Wd8WG1dqHpsv6sWH'
2025-04-22 15:32:34,965 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_a9a78d24ac870bfb`
2025-04-22 15:32:34,971 WARNING database DDL Query made to DB:
CREATE DATABASE `_a9a78d24ac870bfb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-04-22 15:32:35,383 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VA<PERSON>HA<PERSON>(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` TINYINT NOT NULL DEFAULT 0,
				<PERSON><PERSON>AR<PERSON> KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:35,397 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published TINYINT not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-04-22 15:32:35,415 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:35,901 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`color` varchar(140) DEFAULT 'Blue',
`custom` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:36,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` tinyint NOT NULL DEFAULT 0, ADD COLUMN `is_virtual` tinyint NOT NULL DEFAULT 0, ADD COLUMN `not_nullable` tinyint NOT NULL DEFAULT 0, ADD COLUMN `sort_options` tinyint NOT NULL DEFAULT 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` tinyint NOT NULL DEFAULT 0, ADD COLUMN `sticky` tinyint NOT NULL DEFAULT 0, ADD COLUMN `make_attachment_public` tinyint NOT NULL DEFAULT 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-04-22 15:32:36,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `show_dashboard` tinyint NOT NULL DEFAULT 0, MODIFY `oldfieldname` varchar(140), MODIFY `oldfieldtype` varchar(140), MODIFY `fetch_if_empty` tinyint NOT NULL DEFAULT 0, MODIFY `hidden` tinyint NOT NULL DEFAULT 0, MODIFY `bold` tinyint NOT NULL DEFAULT 0, MODIFY `allow_in_quick_entry` tinyint NOT NULL DEFAULT 0, MODIFY `translatable` tinyint NOT NULL DEFAULT 0, MODIFY `print_hide` tinyint NOT NULL DEFAULT 0, MODIFY `print_hide_if_no_value` tinyint NOT NULL DEFAULT 0, MODIFY `report_hide` tinyint NOT NULL DEFAULT 0, MODIFY `depends_on` longtext, MODIFY `collapsible` tinyint NOT NULL DEFAULT 0, MODIFY `collapsible_depends_on` longtext, MODIFY `hide_border` tinyint NOT NULL DEFAULT 0, MODIFY `in_list_view` tinyint NOT NULL DEFAULT 0, MODIFY `in_standard_filter` tinyint NOT NULL DEFAULT 0, MODIFY `in_preview` tinyint NOT NULL DEFAULT 0, MODIFY `in_filter` tinyint NOT NULL DEFAULT 0, MODIFY `in_global_search` tinyint NOT NULL DEFAULT 0, MODIFY `read_only` tinyint NOT NULL DEFAULT 0, MODIFY `allow_on_submit` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_user_permissions` tinyint NOT NULL DEFAULT 0, MODIFY `allow_bulk_edit` tinyint NOT NULL DEFAULT 0, MODIFY `permlevel` int NOT NULL DEFAULT 0, MODIFY `ignore_xss_filter` tinyint NOT NULL DEFAULT 0, MODIFY `hide_days` tinyint NOT NULL DEFAULT 0, MODIFY `unique` tinyint NOT NULL DEFAULT 0, MODIFY `fieldtype` varchar(140) DEFAULT 'Data', MODIFY `no_copy` tinyint NOT NULL DEFAULT 0, MODIFY `label` varchar(140), MODIFY `set_only_once` tinyint NOT NULL DEFAULT 0, MODIFY `remember_last_selected_value` tinyint NOT NULL DEFAULT 0, MODIFY `fieldname` varchar(140), MODIFY `mandatory_depends_on` longtext, MODIFY `reqd` tinyint NOT NULL DEFAULT 0, MODIFY `read_only_depends_on` longtext, MODIFY `search_index` tinyint NOT NULL DEFAULT 0, MODIFY `print_width` varchar(10), MODIFY `width` varchar(10), MODIFY `hide_seconds` tinyint NOT NULL DEFAULT 0, MODIFY `length` int NOT NULL DEFAULT 0, MODIFY `columns` int NOT NULL DEFAULT 0, MODIFY `precision` varchar(140)
2025-04-22 15:32:36,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` tinyint NOT NULL DEFAULT 0, ADD COLUMN `select` tinyint NOT NULL DEFAULT 0
2025-04-22 15:32:36,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `report` tinyint NOT NULL DEFAULT 1, MODIFY `read` tinyint NOT NULL DEFAULT 1, MODIFY `amend` tinyint NOT NULL DEFAULT 0, MODIFY `submit` tinyint NOT NULL DEFAULT 0, MODIFY `delete` tinyint NOT NULL DEFAULT 1, MODIFY `print` tinyint NOT NULL DEFAULT 1, MODIFY `role` varchar(140), MODIFY `export` tinyint NOT NULL DEFAULT 1, MODIFY `permlevel` int NOT NULL DEFAULT 0, MODIFY `create` tinyint NOT NULL DEFAULT 1, MODIFY `write` tinyint NOT NULL DEFAULT 1, MODIFY `cancel` tinyint NOT NULL DEFAULT 0, MODIFY `share` tinyint NOT NULL DEFAULT 1, MODIFY `email` tinyint NOT NULL DEFAULT 1, MODIFY `import` tinyint NOT NULL DEFAULT 0
2025-04-22 15:32:36,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` tinyint NOT NULL DEFAULT 0, ADD COLUMN `custom` tinyint NOT NULL DEFAULT 0
2025-04-22 15:32:36,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` tinyint NOT NULL DEFAULT 0, ADD COLUMN `is_child_table` tinyint NOT NULL DEFAULT 0, ADD COLUMN `custom` tinyint NOT NULL DEFAULT 0
2025-04-22 15:32:36,420 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ui_tour` tinyint NOT NULL DEFAULT 0,
`is_table_field` tinyint NOT NULL DEFAULT 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) DEFAULT 'Bottom',
`hide_buttons` tinyint NOT NULL DEFAULT 0,
`popover_element` tinyint NOT NULL DEFAULT 0,
`modal_trigger` tinyint NOT NULL DEFAULT 0,
`offset_x` int NOT NULL DEFAULT 0,
`offset_y` int NOT NULL DEFAULT 0,
`next_on_click` tinyint NOT NULL DEFAULT 0,
`label` varchar(140),
`fieldtype` varchar(140) DEFAULT '0',
`has_next_condition` tinyint NOT NULL DEFAULT 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:36,491 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) DEFAULT 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` tinyint NOT NULL DEFAULT 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` tinyint NOT NULL DEFAULT 0,
`track_steps` tinyint NOT NULL DEFAULT 0,
`is_standard` tinyint NOT NULL DEFAULT 0,
`save_on_complete` tinyint NOT NULL DEFAULT 0,
`first_document` tinyint NOT NULL DEFAULT 0,
`include_name_field` tinyint NOT NULL DEFAULT 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:36,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `protect_attached_files` tinyint NOT NULL DEFAULT 0, ADD COLUMN `is_calendar_and_gantt` tinyint NOT NULL DEFAULT 0, ADD COLUMN `quick_entry` tinyint NOT NULL DEFAULT 0, ADD COLUMN `grid_page_length` int NOT NULL DEFAULT 50, ADD COLUMN `track_views` tinyint NOT NULL DEFAULT 0, ADD COLUMN `queue_in_background` tinyint NOT NULL DEFAULT 0, ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `documentation` varchar(140), ADD COLUMN `allow_events_in_timeline` tinyint NOT NULL DEFAULT 0, ADD COLUMN `allow_auto_repeat` tinyint NOT NULL DEFAULT 0, ADD COLUMN `make_attachments_public` tinyint NOT NULL DEFAULT 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` tinyint NOT NULL DEFAULT 0, ADD COLUMN `show_preview_popup` tinyint NOT NULL DEFAULT 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `index_web_pages_for_search` tinyint NOT NULL DEFAULT 1, ADD COLUMN `row_format` varchar(140) DEFAULT 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-04-22 15:32:36,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `restrict_to_domain` varchar(140), MODIFY `sort_order` varchar(140) DEFAULT 'DESC', MODIFY `editable_grid` tinyint NOT NULL DEFAULT 1, MODIFY `autoname` varchar(140), MODIFY `sort_field` varchar(140) DEFAULT 'creation', MODIFY `is_tree` tinyint NOT NULL DEFAULT 0, MODIFY `_user_tags` text, MODIFY `issingle` tinyint NOT NULL DEFAULT 0, MODIFY `istable` tinyint NOT NULL DEFAULT 0, MODIFY `default_print_format` varchar(140), MODIFY `read_only` tinyint NOT NULL DEFAULT 0, MODIFY `track_changes` tinyint NOT NULL DEFAULT 0, MODIFY `search_fields` varchar(140), MODIFY `translated_doctype` tinyint NOT NULL DEFAULT 0, MODIFY `is_published_field` varchar(140), MODIFY `engine` varchar(140) DEFAULT 'InnoDB', MODIFY `is_submittable` tinyint NOT NULL DEFAULT 0, MODIFY `title_field` varchar(140), MODIFY `allow_rename` tinyint NOT NULL DEFAULT 1, MODIFY `route` varchar(140), MODIFY `color` varchar(140), MODIFY `show_title_field_in_link` tinyint NOT NULL DEFAULT 0, MODIFY `in_create` tinyint NOT NULL DEFAULT 0, MODIFY `icon` varchar(140), MODIFY `timeline_field` varchar(140), MODIFY `website_search_field` varchar(140), MODIFY `show_name_in_global_search` tinyint NOT NULL DEFAULT 0, MODIFY `allow_import` tinyint NOT NULL DEFAULT 0, MODIFY `subject_field` varchar(140), MODIFY `max_attachments` int NOT NULL DEFAULT 0, MODIFY `has_web_view` tinyint NOT NULL DEFAULT 0, MODIFY `hide_toolbar` tinyint NOT NULL DEFAULT 0, MODIFY `document_type` varchar(140), MODIFY `image_field` varchar(140), MODIFY `allow_guest_to_view` tinyint NOT NULL DEFAULT 0, MODIFY `allow_copy` tinyint NOT NULL DEFAULT 0, MODIFY `sender_field` varchar(140), MODIFY `beta` tinyint NOT NULL DEFAULT 0, MODIFY `email_append_to` tinyint NOT NULL DEFAULT 0, MODIFY `is_virtual` tinyint NOT NULL DEFAULT 0, MODIFY `custom` tinyint NOT NULL DEFAULT 0, MODIFY `migration_hash` varchar(140), MODIFY `track_seen` tinyint NOT NULL DEFAULT 0, MODIFY `module` varchar(140)
2025-04-22 15:32:36,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-04-22 15:32:36,863 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role_name` varchar(140) UNIQUE,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`is_custom` tinyint NOT NULL DEFAULT 0,
`desk_access` tinyint NOT NULL DEFAULT 1,
`two_factor_auth` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:36,958 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,069 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_system_generated` tinyint NOT NULL DEFAULT 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(140),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int NOT NULL DEFAULT 0,
`link_filters` json,
`fieldtype` varchar(140) DEFAULT 'Data',
`precision` varchar(140),
`hide_seconds` tinyint NOT NULL DEFAULT 0,
`hide_days` tinyint NOT NULL DEFAULT 0,
`options` text,
`sort_options` tinyint NOT NULL DEFAULT 0,
`fetch_from` text,
`fetch_if_empty` tinyint NOT NULL DEFAULT 0,
`collapsible` tinyint NOT NULL DEFAULT 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` tinyint NOT NULL DEFAULT 0,
`reqd` tinyint NOT NULL DEFAULT 0,
`unique` tinyint NOT NULL DEFAULT 0,
`is_virtual` tinyint NOT NULL DEFAULT 0,
`read_only` tinyint NOT NULL DEFAULT 0,
`ignore_user_permissions` tinyint NOT NULL DEFAULT 0,
`hidden` tinyint NOT NULL DEFAULT 0,
`print_hide` tinyint NOT NULL DEFAULT 0,
`print_hide_if_no_value` tinyint NOT NULL DEFAULT 0,
`print_width` varchar(140),
`no_copy` tinyint NOT NULL DEFAULT 0,
`allow_on_submit` tinyint NOT NULL DEFAULT 0,
`in_list_view` tinyint NOT NULL DEFAULT 0,
`in_standard_filter` tinyint NOT NULL DEFAULT 0,
`in_global_search` tinyint NOT NULL DEFAULT 0,
`in_preview` tinyint NOT NULL DEFAULT 0,
`bold` tinyint NOT NULL DEFAULT 0,
`report_hide` tinyint NOT NULL DEFAULT 0,
`search_index` tinyint NOT NULL DEFAULT 0,
`allow_in_quick_entry` tinyint NOT NULL DEFAULT 0,
`ignore_xss_filter` tinyint NOT NULL DEFAULT 0,
`translatable` tinyint NOT NULL DEFAULT 0,
`hide_border` tinyint NOT NULL DEFAULT 0,
`show_dashboard` tinyint NOT NULL DEFAULT 0,
`description` text,
`permlevel` int NOT NULL DEFAULT 0,
`width` varchar(140),
`columns` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,136 WARNING database DDL Query made to DB:
create table `tabProperty Setter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_system_generated` tinyint NOT NULL DEFAULT 0,
`doctype_or_field` varchar(140),
`doc_type` varchar(140),
`field_name` varchar(140),
`row_name` varchar(140),
`module` varchar(140),
`property` varchar(140),
`property_type` varchar(140),
`value` text,
`default_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `doc_type`(`doc_type`),
index `field_name`(`field_name`),
index `property`(`property`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,228 WARNING database DDL Query made to DB:
create table `tabWeb Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`route` varchar(140) UNIQUE,
`published` tinyint NOT NULL DEFAULT 0,
`doc_type` varchar(140),
`module` varchar(140),
`is_standard` tinyint NOT NULL DEFAULT 0,
`introduction_text` longtext,
`anonymous` tinyint NOT NULL DEFAULT 0,
`login_required` tinyint NOT NULL DEFAULT 0,
`apply_document_permissions` tinyint NOT NULL DEFAULT 0,
`allow_edit` tinyint NOT NULL DEFAULT 0,
`allow_multiple` tinyint NOT NULL DEFAULT 0,
`allow_delete` tinyint NOT NULL DEFAULT 0,
`allow_incomplete` tinyint NOT NULL DEFAULT 0,
`allow_comments` tinyint NOT NULL DEFAULT 0,
`allow_print` tinyint NOT NULL DEFAULT 0,
`print_format` varchar(140),
`max_attachment_size` int NOT NULL DEFAULT 0,
`show_attachments` tinyint NOT NULL DEFAULT 0,
`hide_navbar` tinyint NOT NULL DEFAULT 0,
`hide_footer` tinyint NOT NULL DEFAULT 0,
`allowed_embedding_domains` text,
`condition_json` json,
`show_list` tinyint NOT NULL DEFAULT 0,
`list_title` varchar(140),
`show_sidebar` tinyint NOT NULL DEFAULT 0,
`website_sidebar` varchar(140),
`button_label` varchar(140) DEFAULT 'Save',
`banner_image` text,
`breadcrumbs` longtext,
`success_title` varchar(140),
`success_url` varchar(140),
`success_message` text,
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`client_script` longtext,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,286 WARNING database DDL Query made to DB:
create table `tabWeb Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`type` varchar(140) DEFAULT 'Section',
`standard` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,357 WARNING database DDL Query made to DB:
create table `tabWeb Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
`allow_read_on_all_link_options` tinyint NOT NULL DEFAULT 0,
`reqd` tinyint NOT NULL DEFAULT 0,
`read_only` tinyint NOT NULL DEFAULT 0,
`show_in_filter` tinyint NOT NULL DEFAULT 0,
`hidden` tinyint NOT NULL DEFAULT 0,
`options` text,
`max_length` int NOT NULL DEFAULT 0,
`max_value` int NOT NULL DEFAULT 0,
`precision` varchar(140),
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`description` text,
`default` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,409 WARNING database DDL Query made to DB:
create table `tabPortal Menu Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`enabled` tinyint NOT NULL DEFAULT 0,
`route` varchar(140),
`reference_doctype` varchar(140),
`role` varchar(140),
`target` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,481 WARNING database DDL Query made to DB:
create table `tabNumber Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_standard` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`label` varchar(140),
`type` varchar(140),
`report_name` varchar(140),
`method` varchar(140),
`function` varchar(140),
`aggregate_function_based_on` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`report_field` varchar(140),
`report_function` varchar(140),
`is_public` tinyint NOT NULL DEFAULT 0,
`currency` varchar(140),
`filters_config` longtext,
`show_percentage_stats` tinyint NOT NULL DEFAULT 1,
`stats_time_interval` varchar(140) DEFAULT 'Daily',
`filters_json` longtext,
`dynamic_filters_json` longtext,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,570 WARNING database DDL Query made to DB:
create table `tabDashboard Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_standard` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`chart_name` varchar(140) UNIQUE,
`chart_type` varchar(140),
`report_name` varchar(140),
`use_report_chart` tinyint NOT NULL DEFAULT 0,
`x_field` varchar(140),
`source` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`based_on` varchar(140),
`value_based_on` varchar(140),
`group_by_type` varchar(140) DEFAULT 'Count',
`group_by_based_on` varchar(140),
`aggregate_function_based_on` varchar(140),
`number_of_groups` int NOT NULL DEFAULT 0,
`is_public` tinyint NOT NULL DEFAULT 0,
`heatmap_year` varchar(140),
`timespan` varchar(140),
`from_date` date,
`to_date` date,
`time_interval` varchar(140),
`timeseries` tinyint NOT NULL DEFAULT 0,
`type` varchar(140) DEFAULT 'Line',
`currency` varchar(140),
`filters_json` longtext,
`dynamic_filters_json` longtext,
`custom_options` longtext,
`color` varchar(140),
`last_synced_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,631 WARNING database DDL Query made to DB:
create table `tabDashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dashboard_name` varchar(140) UNIQUE,
`is_default` tinyint NOT NULL DEFAULT 0,
`is_standard` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`chart_options` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,680 WARNING database DDL Query made to DB:
create table `tabOnboarding Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,764 WARNING database DDL Query made to DB:
create table `tabOnboarding Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`is_complete` tinyint NOT NULL DEFAULT 0,
`is_skipped` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`intro_video_url` varchar(140),
`action` varchar(140),
`action_label` varchar(140),
`reference_document` varchar(140),
`show_full_form` tinyint NOT NULL DEFAULT 0,
`show_form_tour` tinyint NOT NULL DEFAULT 0,
`form_tour` varchar(140),
`is_single` tinyint NOT NULL DEFAULT 0,
`reference_report` varchar(140),
`report_reference_doctype` varchar(140),
`report_type` varchar(140),
`report_description` varchar(140),
`path` varchar(140),
`callback_title` varchar(140),
`callback_message` text,
`validate_action` tinyint NOT NULL DEFAULT 1,
`field` varchar(140),
`value_to_validate` varchar(140),
`video_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,827 WARNING database DDL Query made to DB:
create table `tabOnboarding Step Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`step` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,882 WARNING database DDL Query made to DB:
create table `tabModule Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`module` varchar(140),
`success_message` varchar(140),
`documentation_url` varchar(140),
`is_complete` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:37,945 WARNING database DDL Query made to DB:
create table `tabWorkspace Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`type` varchar(140) DEFAULT 'Link',
`label` varchar(140),
`icon` varchar(140),
`description` longtext,
`hidden` tinyint NOT NULL DEFAULT 0,
`link_type` varchar(140),
`link_to` varchar(140),
`report_ref_doctype` varchar(140),
`dependencies` varchar(140),
`only_for` varchar(140),
`onboard` tinyint NOT NULL DEFAULT 0,
`is_query_report` tinyint NOT NULL DEFAULT 0,
`link_count` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,011 WARNING database DDL Query made to DB:
create table `tabWorkspace Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`chart_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,077 WARNING database DDL Query made to DB:
create table `tabWorkspace Shortcut` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`type` varchar(140),
`link_to` varchar(140),
`url` varchar(140),
`doc_view` varchar(140),
`kanban_board` varchar(140),
`label` varchar(140),
`icon` varchar(140),
`restrict_to_domain` varchar(140),
`report_ref_doctype` varchar(140),
`stats_filter` longtext,
`color` varchar(140),
`format` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,132 WARNING database DDL Query made to DB:
create table `tabWorkspace Quick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`label` varchar(140),
`quick_list_filter` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,191 WARNING database DDL Query made to DB:
create table `tabWorkspace Number Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`number_card_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,239 WARNING database DDL Query made to DB:
create table `tabWorkspace Custom Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`custom_block_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,333 WARNING database DDL Query made to DB:
create table `tabWorkspace` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140) UNIQUE,
`title` varchar(140),
`sequence_id` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_user` varchar(140),
`parent_page` varchar(140),
`module` varchar(140),
`app` varchar(140),
`type` varchar(140) DEFAULT 'Workspace',
`link_type` varchar(140),
`link_to` varchar(140),
`external_link` varchar(140),
`icon` varchar(140),
`indicator_color` varchar(140),
`restrict_to_domain` varchar(140),
`hide_custom` tinyint NOT NULL DEFAULT 0,
`public` tinyint NOT NULL DEFAULT 0,
`is_hidden` tinyint NOT NULL DEFAULT 0,
`content` longtext DEFAULT '[]',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `restrict_to_domain`(`restrict_to_domain`),
index `public`(`public`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,417 WARNING database DDL Query made to DB:
create table `tabPage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`system_page` tinyint NOT NULL DEFAULT 0,
`page_name` varchar(140) UNIQUE,
`title` varchar(140),
`icon` varchar(140),
`module` varchar(140),
`restrict_to_domain` varchar(140),
`standard` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,496 WARNING database DDL Query made to DB:
create table `tabReport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`report_name` varchar(140) UNIQUE,
`ref_doctype` varchar(140),
`reference_report` varchar(140),
`is_standard` varchar(140),
`module` varchar(140),
`report_type` varchar(140),
`letter_head` varchar(140),
`add_total_row` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`prepared_report` tinyint NOT NULL DEFAULT 0,
`add_translate_data` tinyint NOT NULL DEFAULT 0,
`timeout` int NOT NULL DEFAULT 0,
`query` longtext,
`report_script` longtext,
`javascript` longtext,
`json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,563 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`source_name` varchar(140) UNIQUE,
`module` varchar(140),
`timeseries` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,757 WARNING database DDL Query made to DB:
create table `tabPrint Format` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`doc_type` varchar(140),
`module` varchar(140),
`default_print_language` varchar(140),
`standard` varchar(140) DEFAULT 'No',
`custom_format` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`pdf_generator` varchar(140) DEFAULT 'wkhtmltopdf',
`print_format_type` varchar(140) DEFAULT 'Jinja',
`raw_printing` tinyint NOT NULL DEFAULT 0,
`html` longtext,
`raw_commands` longtext,
`margin_top` decimal(21,9) NOT NULL DEFAULT 15.0,
`margin_bottom` decimal(21,9) NOT NULL DEFAULT 15.0,
`margin_left` decimal(21,9) NOT NULL DEFAULT 15.0,
`margin_right` decimal(21,9) NOT NULL DEFAULT 15.0,
`align_labels_right` tinyint NOT NULL DEFAULT 0,
`show_section_headings` tinyint NOT NULL DEFAULT 0,
`line_breaks` tinyint NOT NULL DEFAULT 0,
`absolute_value` tinyint NOT NULL DEFAULT 0,
`font_size` int NOT NULL DEFAULT 14,
`font` varchar(140),
`page_number` varchar(140) DEFAULT 'Hide',
`css` longtext,
`format_data` longtext,
`print_format_builder` tinyint NOT NULL DEFAULT 0,
`print_format_builder_beta` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,864 WARNING database DDL Query made to DB:
create table `tabWeb Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`route` varchar(140) UNIQUE,
`dynamic_route` tinyint NOT NULL DEFAULT 0,
`published` tinyint NOT NULL DEFAULT 1,
`module` varchar(140),
`content_type` varchar(140) DEFAULT 'Page Builder',
`slideshow` varchar(140),
`dynamic_template` tinyint NOT NULL DEFAULT 0,
`main_section` longtext,
`main_section_md` longtext,
`main_section_html` longtext,
`context_script` longtext,
`javascript` longtext,
`insert_style` tinyint NOT NULL DEFAULT 0,
`text_align` varchar(140),
`css` longtext,
`full_width` tinyint NOT NULL DEFAULT 1,
`show_title` tinyint NOT NULL DEFAULT 0,
`start_date` datetime(6),
`end_date` datetime(6),
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`show_sidebar` tinyint NOT NULL DEFAULT 0,
`website_sidebar` varchar(140),
`enable_comments` tinyint NOT NULL DEFAULT 0,
`header` longtext,
`breadcrumbs` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:38,940 WARNING database DDL Query made to DB:
create table `tabWebsite Theme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`theme` varchar(140) UNIQUE,
`module` varchar(140) DEFAULT 'Website',
`custom` tinyint NOT NULL DEFAULT 1,
`google_font` varchar(140),
`font_size` varchar(140),
`font_properties` varchar(140) DEFAULT 'wght@300;400;500;600;700;800',
`button_rounded_corners` tinyint NOT NULL DEFAULT 1,
`button_shadows` tinyint NOT NULL DEFAULT 0,
`button_gradients` tinyint NOT NULL DEFAULT 0,
`primary_color` varchar(140),
`text_color` varchar(140),
`light_color` varchar(140),
`dark_color` varchar(140),
`background_color` varchar(140),
`custom_overrides` longtext,
`custom_scss` longtext,
`theme_scss` longtext,
`theme_url` varchar(140),
`js` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,068 WARNING database DDL Query made to DB:
create table `tabNotification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`is_standard` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`channel` varchar(140) DEFAULT 'Email',
`slack_webhook_url` varchar(140),
`subject` varchar(140),
`event` varchar(140),
`document_type` varchar(140),
`method` varchar(140),
`date_changed` varchar(140),
`datetime_changed` varchar(140),
`days_in_advance` int NOT NULL DEFAULT 0,
`minutes_offset` int NOT NULL DEFAULT 0,
`datetime_last_run` datetime(6),
`value_changed` varchar(140),
`sender` varchar(140),
`send_system_notification` tinyint NOT NULL DEFAULT 0,
`sender_email` varchar(140),
`condition` longtext,
`set_property_after_alert` varchar(140),
`property_value` varchar(140),
`send_to_all_assignees` tinyint NOT NULL DEFAULT 0,
`message_type` varchar(140) DEFAULT 'Markdown',
`message` longtext DEFAULT 'Add your message here',
`attach_print` tinyint NOT NULL DEFAULT 0,
`print_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `event`(`event`),
index `document_type`(`document_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,151 WARNING database DDL Query made to DB:
create table `tabPrint Style` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`print_style_name` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`standard` tinyint NOT NULL DEFAULT 0,
`css` longtext,
`preview` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,213 WARNING database DDL Query made to DB:
create table `tabClient Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dt` varchar(140),
`view` varchar(140) DEFAULT 'Form',
`module` varchar(140),
`enabled` tinyint NOT NULL DEFAULT 0,
`script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,285 WARNING database DDL Query made to DB:
create table `tabServer Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`script_type` varchar(140),
`reference_doctype` varchar(140),
`event_frequency` varchar(140),
`cron_format` varchar(140),
`doctype_event` varchar(140),
`api_method` varchar(140),
`allow_guest` tinyint NOT NULL DEFAULT 0,
`module` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`script` longtext,
`enable_rate_limit` tinyint NOT NULL DEFAULT 0,
`rate_limit_count` int NOT NULL DEFAULT 5,
`rate_limit_seconds` int NOT NULL DEFAULT 86400,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `module`(`module`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue` MODIFY `defkey` varchar(140)
2025-04-22 15:32:39,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue`
				ADD INDEX IF NOT EXISTS `defaultvalue_parent_parenttype_index`(parent, parenttype)
2025-04-22 15:32:39,486 WARNING database DDL Query made to DB:
create table `tabUser Role Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role_profile` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,573 WARNING database DDL Query made to DB:
create table `tabReport Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` varchar(140),
`width` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,633 WARNING database DDL Query made to DB:
create table `tabSuccess Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ref_doctype` varchar(140) UNIQUE,
`first_success_message` varchar(140) DEFAULT 'Congratulations on first creations',
`message` varchar(140) DEFAULT 'Successfully created',
`next_actions` varchar(140),
`action_timeout` int NOT NULL DEFAULT 7,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,683 WARNING database DDL Query made to DB:
create table `tabPackage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`package_name` varchar(140),
`readme` longtext,
`license_type` varchar(140),
`license` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,739 WARNING database DDL Query made to DB:
create table `tabVersion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ref_doctype` varchar(140),
`docname` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion`
				ADD INDEX IF NOT EXISTS `ref_doctype_docname_index`(ref_doctype, docname)
2025-04-22 15:32:39,847 WARNING database DDL Query made to DB:
create table `tabActivity Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` text,
`content` longtext,
`communication_date` datetime(6),
`ip_address` varchar(140),
`operation` varchar(140),
`status` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`timeline_doctype` varchar(140),
`timeline_name` varchar(140),
`link_doctype` varchar(140),
`link_name` varchar(140),
`user` varchar(140),
`full_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:39,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-04-22 15:32:39,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `timeline_doctype_timeline_name_index`(timeline_doctype, timeline_name)
2025-04-22 15:32:40,012 WARNING database DDL Query made to DB:
create table `tabModule Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`module_profile_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,105 WARNING database DDL Query made to DB:
create table `tabNavbar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_label` varchar(140),
`item_type` varchar(140),
`route` varchar(140),
`action` varchar(140),
`hidden` tinyint NOT NULL DEFAULT 0,
`is_standard` tinyint NOT NULL DEFAULT 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,211 WARNING database DDL Query made to DB:
create table `tabTranslation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`contributed` tinyint NOT NULL DEFAULT 0,
`language` varchar(140),
`source_text` longtext,
`context` varchar(140),
`translated_text` longtext,
`contribution_status` varchar(140),
`contribution_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `language`(`language`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,259 WARNING database DDL Query made to DB:
create table `tabData Import Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`data_import` varchar(140),
`row_indexes` longtext,
`success` tinyint NOT NULL DEFAULT 0,
`docname` varchar(140),
`messages` longtext,
`exception` text,
`log_index` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,322 WARNING database DDL Query made to DB:
create table `tabAccess Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`export_from` varchar(140),
`user` varchar(140),
`reference_document` varchar(140),
`timestamp` datetime(6),
`file_type` varchar(140),
`method` varchar(140),
`report_name` varchar(140),
`filters` longtext,
`page` longtext,
`columns` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,382 WARNING database DDL Query made to DB:
create table `tabUser Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`allow` varchar(140),
`for_value` varchar(140),
`is_default` tinyint NOT NULL DEFAULT 0,
`apply_to_all_doctypes` tinyint NOT NULL DEFAULT 1,
`applicable_for` varchar(140),
`hide_descendants` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,442 WARNING database DDL Query made to DB:
create table `tabUser Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`is_custom` tinyint NOT NULL DEFAULT 0,
`read` tinyint NOT NULL DEFAULT 1,
`write` tinyint NOT NULL DEFAULT 0,
`create` tinyint NOT NULL DEFAULT 0,
`submit` tinyint NOT NULL DEFAULT 0,
`cancel` tinyint NOT NULL DEFAULT 0,
`amend` tinyint NOT NULL DEFAULT 0,
`delete` tinyint NOT NULL DEFAULT 0,
`email` tinyint NOT NULL DEFAULT 1,
`share` tinyint NOT NULL DEFAULT 1,
`print` tinyint NOT NULL DEFAULT 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,498 WARNING database DDL Query made to DB:
create table `tabUser Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_account` varchar(140),
`email_id` varchar(140),
`awaiting_password` tinyint NOT NULL DEFAULT 0,
`used_oauth` tinyint NOT NULL DEFAULT 0,
`enable_outgoing` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,551 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`field` varchar(140),
`condition` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,603 WARNING database DDL Query made to DB:
create table `tabAmended Document Naming Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140) UNIQUE,
`action` varchar(140) DEFAULT 'Amend Counter',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,665 WARNING database DDL Query made to DB:
create table `tabSubmission Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140),
`job_id` varchar(140),
`ended_at` datetime(6),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`exception` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_docname`(`ref_docname`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,801 WARNING database DDL Query made to DB:
create table `tabRole Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role_profile` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,870 WARNING database DDL Query made to DB:
create table `tabPackage Release` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`package` varchar(140),
`publish` tinyint NOT NULL DEFAULT 0,
`path` text,
`major` int NOT NULL DEFAULT 0,
`minor` int NOT NULL DEFAULT 0,
`patch` int NOT NULL DEFAULT 0,
`release_notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:40,927 WARNING database DDL Query made to DB:
create table `tabDomain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`domain` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,034 WARNING database DDL Query made to DB:
create table `tabDocument Share Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`key` varchar(140),
`expires_on` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `reference_docname`(`reference_docname`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,080 WARNING database DDL Query made to DB:
create table `tabUser Type Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,128 WARNING database DDL Query made to DB:
create table `tabLogs To Clear` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ref_doctype` varchar(140),
`days` int NOT NULL DEFAULT 30,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,178 WARNING database DDL Query made to DB:
create table `tabCommunication Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-04-22 15:32:41,276 WARNING database DDL Query made to DB:
create table `tabTransaction Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`row_index` varchar(140),
`reference_doctype` varchar(140),
`document_name` varchar(140),
`timestamp` datetime(6),
`checksum_version` varchar(140),
`previous_hash` text,
`transaction_hash` text,
`chaining_hash` text,
`data` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,331 WARNING database DDL Query made to DB:
create table `tabUser Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,392 WARNING database DDL Query made to DB:
create table `tabComment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`comment_type` varchar(140) DEFAULT 'Comment',
`comment_email` varchar(140),
`subject` text,
`comment_by` varchar(140),
`published` tinyint NOT NULL DEFAULT 0,
`seen` tinyint NOT NULL DEFAULT 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`content` longtext,
`ip_address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabComment`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-04-22 15:32:41,501 WARNING database DDL Query made to DB:
create table `tabModule Def` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`module_name` varchar(140) UNIQUE,
`app_name` varchar(140),
`restrict_to_domain` varchar(140),
`package` varchar(140),
`custom` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,720 WARNING database DDL Query made to DB:
create table `tabScheduled Job Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140),
`scheduled_job_type` varchar(140),
`details` longtext,
`debug_log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,777 WARNING database DDL Query made to DB:
create table `tabUser Social Login` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`provider` varchar(140),
`username` varchar(140),
`userid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,853 WARNING database DDL Query made to DB:
create table `tabDocShare` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`share_doctype` varchar(140),
`share_name` varchar(140),
`read` tinyint NOT NULL DEFAULT 0,
`write` tinyint NOT NULL DEFAULT 0,
`share` tinyint NOT NULL DEFAULT 0,
`submit` tinyint NOT NULL DEFAULT 0,
`everyone` tinyint NOT NULL DEFAULT 0,
`notify_by_email` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `share_doctype`(`share_doctype`),
index `share_name`(`share_name`),
index `everyone`(`everyone`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:41,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `user_share_doctype_index`(user, share_doctype)
2025-04-22 15:32:41,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `share_doctype_share_name_index`(share_doctype, share_name)
2025-04-22 15:32:42,109 WARNING database DDL Query made to DB:
create table `tabUser Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,159 WARNING database DDL Query made to DB:
create table `tabUser Select Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,218 WARNING database DDL Query made to DB:
create table `tabSMS Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sender_name` varchar(140),
`sent_on` date,
`message` text,
`no_of_requested_sms` int NOT NULL DEFAULT 0,
`requested_numbers` longtext,
`no_of_sent_sms` int NOT NULL DEFAULT 0,
`sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,315 WARNING database DDL Query made to DB:
create table `tabReport Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`fieldtype` varchar(140),
`fieldname` varchar(140),
`mandatory` tinyint NOT NULL DEFAULT 0,
`wildcard_filter` tinyint NOT NULL DEFAULT 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,367 WARNING database DDL Query made to DB:
create table `tabError Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`seen` tinyint NOT NULL DEFAULT 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`method` varchar(140),
`error` longtext,
`trace_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index creation(creation))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,426 WARNING database DDL Query made to DB:
create table `tabDeleted Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`deleted_name` varchar(140),
`deleted_doctype` varchar(140),
`restored` tinyint NOT NULL DEFAULT 0,
`new_name` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,488 WARNING database DDL Query made to DB:
create table `tabDynamic Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabDynamic Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-04-22 15:32:42,576 WARNING database DDL Query made to DB:
create table `tabBlock Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,660 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`priority` int NOT NULL DEFAULT 0,
`prefix` varchar(140),
`counter` int NOT NULL DEFAULT 0,
`prefix_digits` int NOT NULL DEFAULT 5,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,764 WARNING database DDL Query made to DB:
create table `tabSession Default` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ref_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,816 WARNING database DDL Query made to DB:
create table `tabPatch Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`patch` longtext,
`skipped` tinyint NOT NULL DEFAULT 0,
`traceback` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:42,965 WARNING database DDL Query made to DB:
create table `tabScheduled Job Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`stopped` tinyint NOT NULL DEFAULT 0,
`method` varchar(140),
`server_script` varchar(140),
`scheduler_event` varchar(140),
`frequency` varchar(140),
`cron_format` varchar(140) NOT NULL DEFAULT '',
`create_log` tinyint NOT NULL DEFAULT 0,
`last_execution` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `server_script`(`server_script`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,004 WARNING database DDL Query made to DB:
alter table `tabScheduled Job Type`
					add unique `unique_scheduled_job`(frequency, cron_format, method)
2025-04-22 15:32:43,078 WARNING database DDL Query made to DB:
create table `tabPermission Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`changed_by` varchar(140),
`status` varchar(140),
`for_doctype` varchar(140),
`for_document` varchar(140),
`reference_type` varchar(140),
`reference` varchar(140),
`changes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,242 WARNING database DDL Query made to DB:
create table `tabCustom DocPerm` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`parent` varchar(140),
`role` varchar(140),
`if_owner` tinyint NOT NULL DEFAULT 0,
`permlevel` int NOT NULL DEFAULT 0,
`select` tinyint NOT NULL DEFAULT 0,
`read` tinyint NOT NULL DEFAULT 1,
`write` tinyint NOT NULL DEFAULT 0,
`create` tinyint NOT NULL DEFAULT 0,
`delete` tinyint NOT NULL DEFAULT 0,
`submit` tinyint NOT NULL DEFAULT 0,
`cancel` tinyint NOT NULL DEFAULT 0,
`amend` tinyint NOT NULL DEFAULT 0,
`report` tinyint NOT NULL DEFAULT 0,
`export` tinyint NOT NULL DEFAULT 1,
`import` tinyint NOT NULL DEFAULT 0,
`share` tinyint NOT NULL DEFAULT 0,
`print` tinyint NOT NULL DEFAULT 0,
`email` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent`(`parent`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,400 WARNING database DDL Query made to DB:
create table `tabPackage Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`attach_package` text,
`activate` tinyint NOT NULL DEFAULT 0,
`force` tinyint NOT NULL DEFAULT 0,
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,494 WARNING database DDL Query made to DB:
create table `tabPrepared Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140) DEFAULT 'Queued',
`report_name` varchar(140),
`job_id` varchar(140),
`report_end_time` datetime(6),
`peak_memory_usage` int NOT NULL DEFAULT 0,
`error_message` text,
`filters` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `report_name`(`report_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,652 WARNING database DDL Query made to DB:
create table `tabLog Setting User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,777 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:43,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` ADD COLUMN `is_private` tinyint NOT NULL DEFAULT 0, ADD COLUMN `file_type` varchar(140), ADD COLUMN `is_home_folder` tinyint NOT NULL DEFAULT 0, ADD COLUMN `is_attachments_folder` tinyint NOT NULL DEFAULT 0, ADD COLUMN `thumbnail_url` text, ADD COLUMN `folder` varchar(255), ADD COLUMN `is_folder` tinyint NOT NULL DEFAULT 0, ADD COLUMN `attached_to_field` varchar(140), ADD COLUMN `old_parent` varchar(140), ADD COLUMN `content_hash` varchar(140), ADD COLUMN `uploaded_to_dropbox` tinyint NOT NULL DEFAULT 0, ADD COLUMN `uploaded_to_google_drive` tinyint NOT NULL DEFAULT 0, ADD COLUMN `_user_tags` text, ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-04-22 15:32:43,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` MODIFY `file_size` bigint(20) NOT NULL DEFAULT 0, MODIFY `attached_to_doctype` varchar(140), MODIFY `file_url` longtext, MODIFY `attached_to_name` varchar(140), MODIFY `file_name` varchar(140)
2025-04-22 15:32:44,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` DROP INDEX `attached_to_name`, DROP INDEX `attached_to_doctype`
2025-04-22 15:32:44,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `attached_to_doctype_attached_to_name_index`(attached_to_doctype, attached_to_name)
2025-04-22 15:32:44,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-04-22 15:32:44,154 WARNING database DDL Query made to DB:
create table `tabInstalled Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`app_name` varchar(140),
`app_version` varchar(140),
`git_branch` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:44,337 WARNING database DDL Query made to DB:
create table `tabCommunication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` text,
`communication_medium` varchar(140),
`sender` varchar(255),
`recipients` longtext,
`cc` longtext,
`bcc` longtext,
`phone_no` varchar(140),
`delivery_status` varchar(140),
`content` longtext,
`text_content` longtext,
`communication_type` varchar(140) DEFAULT 'Communication',
`status` varchar(140),
`sent_or_received` varchar(140),
`communication_date` datetime(6),
`read_receipt` tinyint NOT NULL DEFAULT 0,
`send_after` datetime(6),
`sender_full_name` varchar(140),
`read_by_recipient` tinyint NOT NULL DEFAULT 0,
`read_by_recipient_on` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`email_account` varchar(140),
`in_reply_to` varchar(140),
`user` varchar(140),
`email_template` varchar(140),
`unread_notification_sent` tinyint NOT NULL DEFAULT 0,
`seen` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`message_id` text,
`uid` int NOT NULL DEFAULT 0,
`imap_folder` varchar(140),
`email_status` varchar(140),
`has_attachment` tinyint NOT NULL DEFAULT 0,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `reference_owner`(`reference_owner`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:44,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-04-22 15:32:44,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `status_communication_type_index`(status, communication_type)
2025-04-22 15:32:44,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-04-22 15:32:44,910 WARNING database DDL Query made to DB:
create table `tabData Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`import_type` varchar(140),
`import_file` text,
`payload_count` int NOT NULL DEFAULT 0,
`google_sheets_url` varchar(140),
`use_csv_sniffer` tinyint NOT NULL DEFAULT 0,
`custom_delimiters` tinyint NOT NULL DEFAULT 0,
`delimiter_options` varchar(140) DEFAULT ',;\\t|',
`status` varchar(140) DEFAULT 'Pending',
`submit_after_import` tinyint NOT NULL DEFAULT 0,
`mute_emails` tinyint NOT NULL DEFAULT 1,
`template_options` longtext,
`template_warnings` longtext,
`show_failed_logs` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:46,516 WARNING database DDL Query made to DB:
create table `tabUser` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`email` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`username` varchar(140) UNIQUE,
`language` varchar(140),
`time_zone` varchar(140),
`send_welcome_email` tinyint NOT NULL DEFAULT 1,
`unsubscribed` tinyint NOT NULL DEFAULT 0,
`user_image` text,
`role_profile_name` varchar(140),
`module_profile` varchar(140),
`home_settings` longtext,
`gender` varchar(140),
`birth_date` date,
`interest` text,
`phone` varchar(140),
`location` varchar(140),
`bio` text,
`mobile_no` varchar(140) UNIQUE,
`mute_sounds` tinyint NOT NULL DEFAULT 0,
`desk_theme` varchar(140),
`code_editor_type` varchar(140) DEFAULT 'vscode',
`banner_image` text,
`search_bar` tinyint NOT NULL DEFAULT 1,
`notifications` tinyint NOT NULL DEFAULT 1,
`list_sidebar` tinyint NOT NULL DEFAULT 1,
`bulk_actions` tinyint NOT NULL DEFAULT 1,
`view_switcher` tinyint NOT NULL DEFAULT 1,
`form_sidebar` tinyint NOT NULL DEFAULT 1,
`timeline` tinyint NOT NULL DEFAULT 1,
`dashboard` tinyint NOT NULL DEFAULT 1,
`new_password` text,
`logout_all_sessions` tinyint NOT NULL DEFAULT 1,
`reset_password_key` varchar(140),
`last_reset_password_key_generated_on` datetime(6),
`last_password_reset_date` date,
`redirect_url` text,
`document_follow_notify` tinyint NOT NULL DEFAULT 0,
`document_follow_frequency` varchar(140) DEFAULT 'Daily',
`follow_created_documents` tinyint NOT NULL DEFAULT 0,
`follow_commented_documents` tinyint NOT NULL DEFAULT 0,
`follow_liked_documents` tinyint NOT NULL DEFAULT 0,
`follow_assigned_documents` tinyint NOT NULL DEFAULT 0,
`follow_shared_documents` tinyint NOT NULL DEFAULT 0,
`email_signature` longtext,
`thread_notify` tinyint NOT NULL DEFAULT 1,
`send_me_a_copy` tinyint NOT NULL DEFAULT 0,
`allowed_in_mentions` tinyint NOT NULL DEFAULT 1,
`default_workspace` varchar(140),
`default_app` varchar(140),
`simultaneous_sessions` int NOT NULL DEFAULT 2,
`restrict_ip` text,
`last_ip` varchar(140),
`login_after` int NOT NULL DEFAULT 0,
`user_type` varchar(140) DEFAULT 'System User',
`last_active` datetime(6),
`login_before` int NOT NULL DEFAULT 0,
`bypass_restrict_ip_check_if_2fa_enabled` tinyint NOT NULL DEFAULT 0,
`last_login` varchar(140),
`last_known_versions` text,
`api_key` varchar(140) UNIQUE,
`api_secret` text,
`onboarding_status` text DEFAULT '{}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `last_active`(`last_active`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,071 WARNING database DDL Query made to DB:
create table `tabUser Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_standard` tinyint NOT NULL DEFAULT 0,
`role` varchar(140),
`apply_user_permission_on` varchar(140),
`user_id_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,266 WARNING database DDL Query made to DB:
create table `tabLanguage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`language_code` varchar(140) UNIQUE,
`language_name` varchar(140),
`flag` varchar(140),
`based_on` varchar(140),
`date_format` varchar(140),
`time_format` varchar(140),
`number_format` varchar(140),
`first_day_of_the_week` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,337 WARNING database DDL Query made to DB:
create table `tabView Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`viewed_by` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,421 WARNING database DDL Query made to DB:
create table `tabHas Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`domain` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,573 WARNING database DDL Query made to DB:
create table `tabSMS Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`parameter` varchar(140),
`value` varchar(255),
`header` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,672 WARNING database DDL Query made to DB:
create table `tabCustom Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`page` varchar(140),
`report` varchar(140),
`ref_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:47,963 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`route` varchar(140),
`group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,285 WARNING database DDL Query made to DB:
create table `tabWebsite Meta Tag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,419 WARNING database DDL Query made to DB:
create table `tabBlog Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`published` tinyint NOT NULL DEFAULT 1,
`title` varchar(140),
`route` varchar(140) UNIQUE,
`preview_image` text,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,495 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`status` varchar(140),
`partial` tinyint NOT NULL DEFAULT 0,
`fields` text,
`filtered_by` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,572 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`image` text,
`heading` varchar(140),
`description` text,
`url` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,645 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email` varchar(140),
`status` varchar(140) DEFAULT 'Pending Verification',
`anonymization_matrix` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,779 WARNING database DDL Query made to DB:
create table `tabUTM Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`slug` varchar(140) UNIQUE,
`campaign_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:48,930 WARNING database DDL Query made to DB:
create table `tabHelp Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`category_name` varchar(140),
`category_description` text,
`published` tinyint NOT NULL DEFAULT 0,
`help_articles` int NOT NULL DEFAULT 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:49,035 WARNING database DDL Query made to DB:
create table `tabCompany History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`year` varchar(140),
`highlight` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:49,151 WARNING database DDL Query made to DB:
create table `tabWeb Page View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`path` varchar(140),
`referrer` varchar(140),
`browser` varchar(140),
`browser_version` varchar(140),
`is_unique` varchar(140),
`time_zone` varchar(140),
`user_agent` varchar(140),
`source` varchar(140),
`campaign` varchar(140),
`medium` varchar(140),
`visitor_id` varchar(140),
`content` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `path`(`path`),
index `visitor_id`(`visitor_id`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:49,239 WARNING database DDL Query made to DB:
create table `tabSocial Link Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`social_link_type` varchar(140),
`color` varchar(140),
`background_color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:49,329 WARNING database DDL Query made to DB:
create table `tabAbout Us Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`full_name` varchar(140),
`image_link` text,
`bio` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:49,393 WARNING database DDL Query made to DB:
create table `tabWebsite Route Meta` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:50,147 WARNING database DDL Query made to DB:
create table `tabPersonal Data Download Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`user_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:50,865 WARNING database DDL Query made to DB:
create table `tabHelp Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`category` varchar(140),
`published` tinyint NOT NULL DEFAULT 0,
`author` varchar(140) DEFAULT 'user_fullname',
`level` varchar(140),
`content` longtext,
`likes` int NOT NULL DEFAULT 0,
`route` varchar(140),
`helpful` int NOT NULL DEFAULT 0,
`not_helpful` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:51,483 WARNING database DDL Query made to DB:
create table `tabDiscussion Topic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:51,706 WARNING database DDL Query made to DB:
create table `tabDiscussion Reply` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`topic` varchar(140),
`reply` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:51,772 WARNING database DDL Query made to DB:
create table `tabWebsite Route Redirect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`source` text,
`target` text,
`redirect_http_status` varchar(140) DEFAULT '301',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
