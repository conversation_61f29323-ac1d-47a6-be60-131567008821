2025-04-22 15:37:16,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Supplied Item` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, M<PERSON><PERSON>Y `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, <PERSON>ODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:16,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting <PERSON><PERSON>` MODIFY `finished_good_qty` decimal(21,9) NOT NULL DEFAULT 1.0, <PERSON><PERSON><PERSON>Y `is_active` tinyint NOT NULL DEFAULT 1, <PERSON><PERSON><PERSON><PERSON> `service_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:16,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order` MODIFY `total_additional_costs` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_received` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:18,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabUTM Campaign` ADD COLUMN `crm_campaign` varchar(140)
2025-04-22 15:37:19,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` ADD COLUMN `tax_category` varchar(140), ADD COLUMN `is_your_company_address` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:19,672 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:19,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `is_billing_contact` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:19,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0
2025-04-22 15:39:40,599 WARNING database DDL Query made to DB:
create table `tabPayment Gateway` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`gateway` varchar(140) UNIQUE,
`gateway_settings` varchar(140),
`gateway_controller` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:40,686 WARNING database DDL Query made to DB:
create table `tabBraintree Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`gateway_name` varchar(140),
`merchant_id` varchar(140),
`public_key` varchar(140),
`private_key` text,
`use_sandbox` tinyint NOT NULL DEFAULT 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:40,970 WARNING database DDL Query made to DB:
create table `tabGoCardless Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`gateway_name` varchar(140) UNIQUE,
`access_token` varchar(140),
`webhooks_secret` varchar(140),
`use_sandbox` tinyint NOT NULL DEFAULT 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:41,078 WARNING database DDL Query made to DB:
create table `tabMpesa Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_gateway_name` varchar(140) UNIQUE,
`consumer_key` varchar(140),
`consumer_secret` text,
`initiator_name` varchar(140),
`till_number` varchar(140),
`transaction_limit` decimal(21,9) NOT NULL DEFAULT 150000.0,
`sandbox` tinyint NOT NULL DEFAULT 0,
`business_shortcode` varchar(140),
`online_passkey` text,
`security_credential` text,
`account_balance` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:41,220 WARNING database DDL Query made to DB:
create table `tabGoCardless Mandate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disabled` tinyint NOT NULL DEFAULT 0,
`mandate` varchar(140) UNIQUE,
`gocardless_customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:41,292 WARNING database DDL Query made to DB:
create table `tabStripe Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`gateway_name` varchar(140),
`publishable_key` varchar(140),
`secret_key` text,
`header_img` text,
`redirect_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:42,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` ADD COLUMN `currency` varchar(140), ADD COLUMN `amount_field` varchar(140), ADD COLUMN `payment_button_help` text, ADD COLUMN `amount_based_on_field` tinyint NOT NULL DEFAULT 0, ADD COLUMN `amount` decimal(21,9) NOT NULL DEFAULT 0.0, ADD COLUMN `payment_gateway` varchar(140), ADD COLUMN `payment_button_label` varchar(140) DEFAULT 'Buy Now', ADD COLUMN `accept_payment` tinyint NOT NULL DEFAULT 0
2025-04-22 15:39:42,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` MODIFY `is_standard` tinyint NOT NULL DEFAULT 0, MODIFY `condition_json` json, MODIFY `show_attachments` tinyint NOT NULL DEFAULT 0, MODIFY `allow_incomplete` tinyint NOT NULL DEFAULT 0, MODIFY `show_list` tinyint NOT NULL DEFAULT 0, MODIFY `allow_edit` tinyint NOT NULL DEFAULT 0, MODIFY `allow_delete` tinyint NOT NULL DEFAULT 0, MODIFY `allow_comments` tinyint NOT NULL DEFAULT 0, MODIFY `apply_document_permissions` tinyint NOT NULL DEFAULT 0, MODIFY `show_sidebar` tinyint NOT NULL DEFAULT 0, MODIFY `published` tinyint NOT NULL DEFAULT 0, MODIFY `max_attachment_size` int NOT NULL DEFAULT 0, MODIFY `hide_navbar` tinyint NOT NULL DEFAULT 0, MODIFY `allow_multiple` tinyint NOT NULL DEFAULT 0, MODIFY `hide_footer` tinyint NOT NULL DEFAULT 0, MODIFY `anonymous` tinyint NOT NULL DEFAULT 0, MODIFY `login_required` tinyint NOT NULL DEFAULT 0, MODIFY `allow_print` tinyint NOT NULL DEFAULT 0
2025-04-22 15:39:42,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Mandate` ADD COLUMN `customer` varchar(140)
2025-04-22 15:39:42,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Mandate` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:39:53,572 WARNING database DDL Query made to DB:
create table `tabJob Requisition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`designation` varchar(140),
`department` varchar(140),
`no_of_positions` int NOT NULL DEFAULT 0,
`expected_compensation` decimal(21,9) NOT NULL DEFAULT 0.0,
`company` varchar(140),
`status` varchar(140),
`requested_by` varchar(140),
`requested_by_name` varchar(140),
`requested_by_dept` varchar(140),
`requested_by_designation` varchar(140),
`posting_date` date,
`completed_on` date,
`expected_by` date,
`time_to_fill` decimal(21,9),
`description` longtext,
`reason_for_requesting` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:53,703 WARNING database DDL Query made to DB:
create table `tabEmployee Boarding Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`activity_name` varchar(140),
`user` varchar(140),
`role` varchar(140),
`begin_on` int NOT NULL DEFAULT 0,
`duration` int NOT NULL DEFAULT 0,
`task` varchar(140),
`task_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_for_employee_creation` tinyint NOT NULL DEFAULT 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:53,767 WARNING database DDL Query made to DB:
create table `tabJob Offer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`offer_term` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:53,862 WARNING database DDL Query made to DB:
create table `tabAppraisal Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cycle_name` varchar(140) UNIQUE,
`company` varchar(140),
`status` varchar(140) DEFAULT 'Not Started',
`start_date` date,
`end_date` date,
`description` longtext,
`kra_evaluation_method` varchar(140) DEFAULT 'Automated Based on Goal Progress',
`calculate_final_score_based_on_formula` tinyint NOT NULL DEFAULT 0,
`final_score_formula` longtext,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:53,950 WARNING database DDL Query made to DB:
create table `tabAppointment Letter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`company` varchar(140),
`appointment_date` date,
`appointment_letter_template` varchar(140),
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,021 WARNING database DDL Query made to DB:
create table `tabExpected Skill Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`skill` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,083 WARNING database DDL Query made to DB:
create table `tabAppointment Letter content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,148 WARNING database DDL Query made to DB:
create table `tabLeave Block List Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`block_date` date,
`reason` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,216 WARNING database DDL Query made to DB:
create table `tabJob Offer Term Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,307 WARNING database DDL Query made to DB:
create table `tabLeave Block List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`leave_block_list_name` varchar(140) UNIQUE,
`company` varchar(140),
`applies_to_all_departments` tinyint NOT NULL DEFAULT 0,
`leave_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,388 WARNING database DDL Query made to DB:
create table `tabEmployee Separation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,482 WARNING database DDL Query made to DB:
create table `tabShift Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`shift_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`company` varchar(140),
`approver` varchar(140),
`from_date` date,
`to_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,602 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-04-22 15:39:54,628 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` tinyint NOT NULL DEFAULT 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,730 WARNING database DDL Query made to DB:
create table `tabInterview Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`interview` varchar(140),
`interview_round` varchar(140),
`job_applicant` varchar(140),
`interviewer` varchar(140),
`result` varchar(140),
`average_rating` decimal(3,2),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,827 WARNING database DDL Query made to DB:
create table `tabAppraisee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`appraisal_template` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:54,919 WARNING database DDL Query made to DB:
create table `tabEmployee Checkin` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`log_type` varchar(140),
`shift` varchar(140),
`time` datetime(6),
`device_id` varchar(140),
`skip_auto_attendance` tinyint NOT NULL DEFAULT 0,
`attendance` varchar(140),
`latitude` decimal(21,9) NOT NULL DEFAULT 0.0,
`longitude` decimal(21,9) NOT NULL DEFAULT 0.0,
`geolocation` longtext,
`shift_start` datetime(6),
`shift_end` datetime(6),
`offshift` tinyint NOT NULL DEFAULT 0,
`shift_actual_start` datetime(6),
`shift_actual_end` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift`(`shift`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,131 WARNING database DDL Query made to DB:
create table `tabVehicle Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`license_plate` varchar(140),
`employee` varchar(140),
`model` varchar(140),
`make` varchar(140),
`date` date,
`odometer` int NOT NULL DEFAULT 0,
`last_odometer` int NOT NULL DEFAULT 0,
`fuel_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price` decimal(21,9) NOT NULL DEFAULT 0.0,
`supplier` varchar(140),
`invoice` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,284 WARNING database DDL Query made to DB:
create table `tabLeave Encashment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_period` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`leave_balance` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_encashable_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`encashment_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`encashment_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pay_via_payment_entry` tinyint NOT NULL DEFAULT 0,
`expense_account` varchar(140),
`payable_account` varchar(140),
`posting_date` date,
`currency` varchar(140),
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`encashment_date` date,
`additional_salary` varchar(140),
`amended_from` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,434 WARNING database DDL Query made to DB:
create table `tabAppointment Letter Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template_name` varchar(140) UNIQUE,
`introduction` longtext,
`closing_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,554 WARNING database DDL Query made to DB:
create table `tabEmployee Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`posting_date` date,
`company` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`purpose` text,
`advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pending_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`claimed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`return_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`advance_account` varchar(140),
`mode_of_payment` varchar(140),
`repay_unclaimed_amount_from_salary` tinyint NOT NULL DEFAULT 0,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,700 WARNING database DDL Query made to DB:
create table `tabShift Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`shift_type` varchar(140),
`shift_location` varchar(140),
`status` varchar(140) DEFAULT 'Active',
`start_date` date,
`end_date` date,
`shift_request` varchar(140),
`shift_schedule_assignment` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift_type`(`shift_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,833 WARNING database DDL Query made to DB:
create table `tabShift Schedule Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`shift_schedule` varchar(140),
`shift_location` varchar(140),
`shift_status` varchar(140) DEFAULT 'Active',
`enabled` tinyint NOT NULL DEFAULT 1,
`create_shifts_after` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:55,917 WARNING database DDL Query made to DB:
create table `tabLeave Block List Allow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`allow_user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,003 WARNING database DDL Query made to DB:
create table `tabFull and Final Asset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference` varchar(140),
`asset_name` varchar(140),
`date` datetime(6),
`actual_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`account` varchar(140),
`action` varchar(140) DEFAULT 'Return',
`status` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,188 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,296 WARNING database DDL Query made to DB:
create table `tabEmployee Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`first_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`date` date,
`status` varchar(140),
`for_designation` varchar(140),
`email` varchar(140) UNIQUE,
`contact_no` varchar(140),
`resume_link` varchar(140),
`current_employer` varchar(140),
`current_job_title` varchar(140),
`resume` text,
`referrer` varchar(140),
`referrer_name` varchar(140),
`is_applicable_for_referral_bonus` tinyint NOT NULL DEFAULT 1,
`referral_payment_status` varchar(140),
`department` varchar(140),
`qualification_reason` longtext,
`work_references` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,399 WARNING database DDL Query made to DB:
create table `tabLeave Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_date` date,
`to_date` date,
`is_active` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`optional_holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,485 WARNING database DDL Query made to DB:
create table `tabVehicle Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`service_item` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,581 WARNING database DDL Query made to DB:
create table `tabExpense Claim Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`expense_date` date,
`expense_type` varchar(140),
`default_account` varchar(140),
`description` longtext,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`sanctioned_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,707 WARNING database DDL Query made to DB:
create table `tabTravel Itinerary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`travel_from` varchar(140),
`travel_to` varchar(140),
`mode_of_travel` varchar(140),
`meal_preference` varchar(140),
`travel_advance_required` tinyint NOT NULL DEFAULT 0,
`advance_amount` varchar(140),
`departure_date` datetime(6),
`arrival_date` datetime(6),
`lodging_required` tinyint NOT NULL DEFAULT 0,
`preferred_area_for_lodging` varchar(140),
`check_in_date` date,
`check_out_date` date,
`other_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,791 WARNING database DDL Query made to DB:
create table `tabTravel Request Costing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`expense_type` varchar(140),
`sponsored_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`funded_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,875 WARNING database DDL Query made to DB:
create table `tabTraining Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`course` varchar(140),
`training_event` varchar(140),
`event_name` varchar(140),
`trainer_name` varchar(140),
`feedback` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:56,997 WARNING database DDL Query made to DB:
create table `tabEmployee Training` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`training` varchar(140),
`training_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,074 WARNING database DDL Query made to DB:
create table `tabAppraisal KRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`kra` varchar(140),
`per_weightage` decimal(21,9) NOT NULL DEFAULT 0.0,
`goal_completion` decimal(21,9) NOT NULL DEFAULT 0.0,
`goal_score` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,152 WARNING database DDL Query made to DB:
create table `tabFull and Final Outstanding Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`component` varchar(140),
`reference_document_type` varchar(140),
`reference_document` varchar(140),
`account` varchar(140),
`paid_via_salary_slip` tinyint NOT NULL DEFAULT 0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Unsettled',
`remark` text,
index `reference_document`(`reference_document`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,294 WARNING database DDL Query made to DB:
create table `tabGoal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`goal_name` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`parent_goal` varchar(140),
`progress` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Pending',
`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`appraisal_cycle` varchar(140),
`kra` varchar(140),
`description` longtext,
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,404 WARNING database DDL Query made to DB:
create table `tabInterview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`interview_round` varchar(140),
`job_applicant` varchar(140),
`job_opening` varchar(140),
`designation` varchar(140),
`resume_link` varchar(140),
`status` varchar(140) DEFAULT 'Pending',
`scheduled_on` date,
`from_time` time(6),
`to_time` time(6),
`expected_average_rating` decimal(3,2),
`average_rating` decimal(3,2),
`interview_summary` text,
`reminded` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,535 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`daily_work_summary_group` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`email_sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,620 WARNING database DDL Query made to DB:
create table `tabLeave Policy Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`leave_type` varchar(140),
`annual_allocation` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,721 WARNING database DDL Query made to DB:
create table `tabSkill Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`skill` varchar(140),
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,799 WARNING database DDL Query made to DB:
create table `tabJob Applicant Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`source_name` varchar(140) UNIQUE,
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,907 WARNING database DDL Query made to DB:
create table `tabInterest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`interest` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:57,994 WARNING database DDL Query made to DB:
create table `tabKRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,063 WARNING database DDL Query made to DB:
create table `tabDepartment Approver` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`approver` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,132 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`criteria` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,275 WARNING database DDL Query made to DB:
create table `tabJob Offer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`applicant_email` varchar(140),
`status` varchar(140),
`offer_date` date,
`designation` varchar(140),
`company` varchar(140),
`job_offer_term_template` varchar(140),
`select_terms` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,390 WARNING database DDL Query made to DB:
create table `tabExit Interview` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`email` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`department` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`ref_doctype` varchar(140),
`questionnaire_email_sent` tinyint NOT NULL DEFAULT 0,
`reference_document_name` varchar(140),
`interview_summary` longtext,
`employee_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,480 WARNING database DDL Query made to DB:
create table `tabStaffing Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`department` varchar(140),
`from_date` date,
`to_date` date,
`total_estimated_budget` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,609 WARNING database DDL Query made to DB:
create table `tabJob Opening` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`posted_on` datetime(6),
`closes_on` date,
`closed_on` date,
`company` varchar(140),
`department` varchar(140),
`employment_type` varchar(140),
`location` varchar(140),
`staffing_plan` varchar(140),
`planned_vacancies` int NOT NULL DEFAULT 0,
`job_requisition` varchar(140),
`vacancies` int NOT NULL DEFAULT 0,
`publish` tinyint NOT NULL DEFAULT 0,
`route` varchar(140) UNIQUE,
`publish_applications_received` tinyint NOT NULL DEFAULT 1,
`job_application_route` varchar(140),
`description` longtext,
`currency` varchar(140),
`lower_range` decimal(21,9) NOT NULL DEFAULT 0.0,
`upper_range` decimal(21,9) NOT NULL DEFAULT 0.0,
`salary_per` varchar(140) DEFAULT 'Month',
`publish_salary_range` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,804 WARNING database DDL Query made to DB:
create table `tabLeave Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`company` varchar(140),
`department` varchar(140),
`from_date` date,
`to_date` date,
`half_day` tinyint NOT NULL DEFAULT 0,
`half_day_date` date,
`total_leave_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`leave_balance` decimal(21,9) NOT NULL DEFAULT 0.0,
`leave_approver` varchar(140),
`leave_approver_name` varchar(140),
`follow_via_email` tinyint NOT NULL DEFAULT 1,
`posting_date` date,
`status` varchar(140) DEFAULT 'Open',
`salary_slip` varchar(140),
`color` varchar(140),
`letter_head` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `leave_type`(`leave_type`),
index `from_date`(`from_date`),
index `to_date`(`to_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:58,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application`
				ADD INDEX IF NOT EXISTS `employee_from_date_to_date_index`(employee, from_date, to_date)
2025-04-22 15:39:59,005 WARNING database DDL Query made to DB:
create table `tabInterview Round` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`round_name` varchar(140) UNIQUE,
`interview_type` varchar(140),
`expected_average_rating` decimal(3,2),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,085 WARNING database DDL Query made to DB:
create table `tabOffer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`offer_term` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,250 WARNING database DDL Query made to DB:
create table `tabExpense Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`expense_approver` varchar(140),
`approval_status` varchar(140) DEFAULT 'Draft',
`total_sanctioned_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_advance_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_claimed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount_reimbursed` decimal(21,9) NOT NULL DEFAULT 0.0,
`posting_date` date,
`is_paid` tinyint NOT NULL DEFAULT 0,
`mode_of_payment` varchar(140),
`payable_account` varchar(140),
`clearance_date` date,
`remark` text,
`project` varchar(140),
`cost_center` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`task` varchar(140),
`amended_from` varchar(140),
`delivery_trip` varchar(140),
`vehicle_log` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `approval_status`(`approval_status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,405 WARNING database DDL Query made to DB:
create table `tabLeave Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_type` varchar(140),
`from_date` date,
`to_date` date,
`new_leaves_allocated` decimal(21,9) NOT NULL DEFAULT 0.0,
`carry_forward` tinyint NOT NULL DEFAULT 0,
`unused_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_leaves_allocated` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_leaves_encashed` decimal(21,9) NOT NULL DEFAULT 0.0,
`compensatory_request` varchar(140),
`leave_period` varchar(140),
`leave_policy` varchar(140),
`leave_policy_assignment` varchar(140),
`carry_forwarded_leaves_count` decimal(21,9) NOT NULL DEFAULT 0.0,
`expired` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `employee_name`(`employee_name`),
index `leave_type`(`leave_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,540 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`job_applicant` varchar(140),
`job_offer` varchar(140),
`employee_onboarding_template` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) DEFAULT 'Pending',
`project` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`holiday_list` varchar(140),
`date_of_joining` date,
`boarding_begins_on` date,
`notify_users_by_email` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,672 WARNING database DDL Query made to DB:
create table `tabIdentification Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`identification_document_type` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,799 WARNING database DDL Query made to DB:
create table `tabAttendance Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`from_date` date,
`to_date` date,
`half_day` tinyint NOT NULL DEFAULT 0,
`half_day_date` date,
`include_holidays` tinyint NOT NULL DEFAULT 0,
`shift` varchar(140),
`reason` varchar(140),
`explanation` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,886 WARNING database DDL Query made to DB:
create table `tabAppraisal Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`kra` text,
`per_weightage` decimal(21,9) NOT NULL DEFAULT 0.0,
`score` decimal(21,9) NOT NULL DEFAULT 0.0,
`score_earned` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:39:59,972 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,175 WARNING database DDL Query made to DB:
create table `tabEmployee Grievance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`employee_name` varchar(140),
`designation` varchar(140),
`date` date,
`status` varchar(140) DEFAULT 'Open',
`reports_to` varchar(140),
`grievance_against_party` varchar(140),
`grievance_against` varchar(140),
`grievance_type` varchar(140),
`associated_document_type` varchar(140),
`associated_document` varchar(140),
`description` text,
`cause_of_grievance` text,
`resolved_by` varchar(140),
`resolution_date` date,
`employee_responsible` varchar(140),
`resolution_detail` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,267 WARNING database DDL Query made to DB:
create table `tabInterview Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,371 WARNING database DDL Query made to DB:
create table `tabExpense Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`account_head` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,441 WARNING database DDL Query made to DB:
create table `tabEmployee Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`skill` varchar(140),
`proficiency` decimal(3,2),
`evaluation_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,516 WARNING database DDL Query made to DB:
create table `tabEmployment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee_type_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,629 WARNING database DDL Query made to DB:
create table `tabTraining Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`event_name` varchar(140) UNIQUE,
`training_program` varchar(140),
`event_status` varchar(140),
`has_certificate` tinyint NOT NULL DEFAULT 0,
`type` varchar(140),
`level` varchar(140),
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`course` varchar(140),
`location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`introduction` longtext,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,701 WARNING database DDL Query made to DB:
create table `tabGrievance Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,814 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`location_name` varchar(140) UNIQUE,
`checkin_radius` int NOT NULL DEFAULT 0,
`latitude` decimal(21,9) NOT NULL DEFAULT 0.0,
`longitude` decimal(21,9) NOT NULL DEFAULT 0.0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:00,985 WARNING database DDL Query made to DB:
create table `tabJob Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`applicant_name` varchar(140),
`email_id` varchar(140),
`phone_number` varchar(140),
`country` varchar(140),
`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`source` varchar(140),
`source_name` varchar(140),
`employee_referral` varchar(140),
`applicant_rating` decimal(3,2),
`notes` varchar(140),
`cover_letter` text,
`resume_attachment` text,
`resume_link` varchar(140),
`currency` varchar(140),
`lower_range` decimal(21,9) NOT NULL DEFAULT 0.0,
`upper_range` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `job_title`(`job_title`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,117 WARNING database DDL Query made to DB:
create table `tabSkill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`skill_name` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,281 WARNING database DDL Query made to DB:
create table `tabAttendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`working_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Present',
`leave_type` varchar(140),
`leave_application` varchar(140),
`attendance_date` date,
`company` varchar(140),
`department` varchar(140),
`attendance_request` varchar(140),
`half_day_status` varchar(140),
`shift` varchar(140),
`in_time` datetime(6),
`out_time` datetime(6),
`late_entry` tinyint NOT NULL DEFAULT 0,
`early_exit` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `status`(`status`),
index `attendance_date`(`attendance_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,391 WARNING database DDL Query made to DB:
create table `tabEmployee Property History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`property` varchar(140),
`current` varchar(140),
`new` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,461 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,547 WARNING database DDL Query made to DB:
create table `tabShift Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`start_time` time(6),
`end_time` time(6),
`holiday_list` varchar(140),
`color` varchar(140) DEFAULT 'Blue',
`enable_auto_attendance` tinyint NOT NULL DEFAULT 0,
`determine_check_in_and_check_out` varchar(140),
`working_hours_calculation_based_on` varchar(140),
`begin_check_in_before_shift_start_time` int NOT NULL DEFAULT 60,
`allow_check_out_after_shift_end_time` int NOT NULL DEFAULT 60,
`mark_auto_attendance_on_holidays` tinyint NOT NULL DEFAULT 0,
`working_hours_threshold_for_half_day` decimal(21,9) NOT NULL DEFAULT 0.0,
`working_hours_threshold_for_absent` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_attendance_after` date,
`last_sync_of_checkin` datetime(6),
`auto_update_last_sync` tinyint NOT NULL DEFAULT 0,
`enable_late_entry_marking` tinyint NOT NULL DEFAULT 0,
`late_entry_grace_period` int NOT NULL DEFAULT 0,
`enable_early_exit_marking` tinyint NOT NULL DEFAULT 0,
`early_exit_grace_period` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,638 WARNING database DDL Query made to DB:
create table `tabTraining Event Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`attendance` varchar(140),
`is_mandatory` tinyint NOT NULL DEFAULT 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,803 WARNING database DDL Query made to DB:
create table `tabLeave Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`transaction_type` varchar(140),
`transaction_name` varchar(140),
`company` varchar(140),
`leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`from_date` date,
`to_date` date,
`holiday_list` varchar(140),
`is_carry_forward` tinyint NOT NULL DEFAULT 0,
`is_expired` tinyint NOT NULL DEFAULT 0,
`is_lwp` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `transaction_type`(`transaction_type`),
index `transaction_name`(`transaction_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:01,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry`
				ADD INDEX IF NOT EXISTS `transaction_type_transaction_name_index`(transaction_type, transaction_name)
2025-04-22 15:40:02,015 WARNING database DDL Query made to DB:
create table `tabTraining Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`training_event` varchar(140) UNIQUE,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,094 WARNING database DDL Query made to DB:
create table `tabEmployee Skill Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140) UNIQUE,
`employee_name` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,168 WARNING database DDL Query made to DB:
create table `tabEmployee Grade` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`default_salary_structure` varchar(140),
`currency` varchar(140),
`default_base_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,264 WARNING database DDL Query made to DB:
create table `tabLeave Policy Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`leave_policy` varchar(140),
`carry_forward` tinyint NOT NULL DEFAULT 0,
`assignment_based_on` varchar(140),
`leave_period` varchar(140),
`effective_from` date,
`effective_to` date,
`leaves_allocated` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,414 WARNING database DDL Query made to DB:
create table `tabLeave Policy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,502 WARNING database DDL Query made to DB:
create table `tabTraining Result Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`grade` varchar(140),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,573 WARNING database DDL Query made to DB:
create table `tabInterviewer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,677 WARNING database DDL Query made to DB:
create table `tabCompensatory Leave Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`work_from_date` date,
`work_end_date` date,
`half_day` tinyint NOT NULL DEFAULT 0,
`half_day_date` date,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,764 WARNING database DDL Query made to DB:
create table `tabStaffing Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`designation` varchar(140),
`vacancies` int NOT NULL DEFAULT 0,
`estimated_cost_per_position` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_estimated_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`current_count` int NOT NULL DEFAULT 0,
`current_openings` int NOT NULL DEFAULT 0,
`number_of_positions` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,841 WARNING database DDL Query made to DB:
create table `tabExpense Claim Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`deferred_expense_account` tinyint NOT NULL DEFAULT 0,
`expense_type` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,920 WARNING database DDL Query made to DB:
create table `tabPurpose of Travel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`purpose_of_travel` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:02,991 WARNING database DDL Query made to DB:
create table `tabExpense Claim Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`default_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,081 WARNING database DDL Query made to DB:
create table `tabEmployee Promotion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`salary_currency` varchar(140),
`promotion_date` date,
`company` varchar(140),
`current_ctc` decimal(21,9) NOT NULL DEFAULT 0.0,
`revised_ctc` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,204 WARNING database DDL Query made to DB:
create table `tabAppraisal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_image` text,
`company` varchar(140),
`appraisal_cycle` varchar(140),
`start_date` date,
`end_date` date,
`final_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`appraisal_template` varchar(140),
`rate_goals_manually` tinyint NOT NULL DEFAULT 0,
`goal_score_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`remarks` text,
`total_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`avg_feedback_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`self_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`reflections` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,313 WARNING database DDL Query made to DB:
create table `tabLeave Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`leave_type_name` varchar(140) UNIQUE,
`max_leaves_allowed` decimal(21,9) NOT NULL DEFAULT 0.0,
`applicable_after` int NOT NULL DEFAULT 0,
`max_continuous_days_allowed` int NOT NULL DEFAULT 0,
`is_carry_forward` tinyint NOT NULL DEFAULT 0,
`is_lwp` tinyint NOT NULL DEFAULT 0,
`is_ppl` tinyint NOT NULL DEFAULT 0,
`fraction_of_daily_salary_per_leave` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_optional_leave` tinyint NOT NULL DEFAULT 0,
`allow_negative` tinyint NOT NULL DEFAULT 0,
`allow_over_allocation` tinyint NOT NULL DEFAULT 0,
`include_holiday` tinyint NOT NULL DEFAULT 0,
`is_compensatory` tinyint NOT NULL DEFAULT 0,
`maximum_carry_forwarded_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`expire_carry_forwarded_leaves_after_days` int NOT NULL DEFAULT 0,
`allow_encashment` tinyint NOT NULL DEFAULT 0,
`max_encashable_leaves` int NOT NULL DEFAULT 0,
`non_encashable_leaves` int NOT NULL DEFAULT 0,
`earning_component` varchar(140),
`is_earned_leave` tinyint NOT NULL DEFAULT 0,
`earned_leave_frequency` varchar(140),
`allocate_on_day` varchar(140) DEFAULT 'Last Day',
`rounding` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,418 WARNING database DDL Query made to DB:
create table `tabExpense Claim Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee_advance` varchar(140),
`posting_date` date,
`advance_paid` decimal(21,9) NOT NULL DEFAULT 0.0,
`unclaimed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`return_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`advance_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,570 WARNING database DDL Query made to DB:
create table `tabEmployee Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transfer_date` date,
`company` varchar(140),
`new_company` varchar(140),
`department` varchar(140),
`reallocate_leaves` tinyint NOT NULL DEFAULT 0,
`create_new_employee_id` tinyint NOT NULL DEFAULT 0,
`new_employee_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,694 WARNING database DDL Query made to DB:
create table `tabEmployee Separation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) DEFAULT 'Pending',
`resignation_letter_date` date,
`boarding_begins_on` date,
`project` varchar(140),
`employee_separation_template` varchar(140),
`notify_users_by_email` tinyint NOT NULL DEFAULT 0,
`exit_interview` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,775 WARNING database DDL Query made to DB:
create table `tabAppraisal Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`template_title` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,875 WARNING database DDL Query made to DB:
create table `tabDesignation Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`skill` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:03,972 WARNING database DDL Query made to DB:
create table `tabFull and Final Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transaction_date` date,
`company` varchar(140),
`status` varchar(140) DEFAULT 'Unpaid',
`amended_from` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`designation` varchar(140),
`department` varchar(140),
`total_asset_recovery_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_payable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_receivable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,065 WARNING database DDL Query made to DB:
create table `tabVehicle Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`service_item` varchar(140),
`type` varchar(140),
`frequency` varchar(140),
`expense_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,185 WARNING database DDL Query made to DB:
create table `tabEmployee Performance Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`company` varchar(140),
`reviewer` varchar(140),
`reviewer_name` varchar(140),
`reviewer_designation` varchar(140),
`user` varchar(140),
`added_on` datetime(6),
`appraisal_cycle` varchar(140),
`appraisal` varchar(140),
`total_score` decimal(21,9) NOT NULL DEFAULT 0.0,
`feedback` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,342 WARNING database DDL Query made to DB:
create table `tabTravel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`travel_type` varchar(140),
`travel_funding` varchar(140),
`travel_proof` text,
`purpose_of_travel` varchar(140),
`details_of_sponsor` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`cell_number` varchar(140),
`prefered_email` varchar(140),
`company` varchar(140),
`date_of_birth` date,
`personal_id_type` varchar(140),
`personal_id_number` varchar(140),
`passport_number` varchar(140),
`description` text,
`cost_center` varchar(140),
`name_of_organizer` varchar(140),
`address_of_organizer` varchar(140),
`other_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,477 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`send_emails_at` varchar(140),
`holiday_list` varchar(140),
`subject` varchar(140) DEFAULT 'What did you work on today?',
`message` longtext DEFAULT '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,555 WARNING database DDL Query made to DB:
create table `tabEmployee Health Insurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`health_insurance_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,628 WARNING database DDL Query made to DB:
create table `tabInterview Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`interviewer` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,706 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Rating` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`criteria` varchar(140),
`per_weightage` decimal(21,9) NOT NULL DEFAULT 0.0,
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,797 WARNING database DDL Query made to DB:
create table `tabTraining Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`training_program` varchar(140) UNIQUE,
`status` varchar(140) DEFAULT 'Scheduled',
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`description` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:04,879 WARNING database DDL Query made to DB:
create table `tabAppraisal Template Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key_result_area` varchar(140),
`per_weightage` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:05,517 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:05,613 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` tinyint NOT NULL DEFAULT 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:05,739 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_active` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:05,859 WARNING database DDL Query made to DB:
create table `tabGratuity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`current_work_experience` decimal(21,9) NOT NULL DEFAULT 0.0,
`posting_date` date,
`gratuity_rule` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`pay_via_salary_slip` tinyint NOT NULL DEFAULT 1,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`payroll_date` date,
`salary_component` varchar(140),
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`expense_account` varchar(140),
`payable_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:05,968 WARNING database DDL Query made to DB:
create table `tabEmployee Other Income` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`source` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `company`(`company`),
index `payroll_period`(`payroll_period`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,074 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`submission_date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`total_actual_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`exemption_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,168 WARNING database DDL Query made to DB:
create table `tabPayroll Period Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,243 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`earning_component` varchar(140),
`pay_against_benefit_claim` tinyint NOT NULL DEFAULT 0,
`max_benefit_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,324 WARNING database DDL Query made to DB:
create table `tabPayroll Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,420 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`type_of_proof` varchar(140),
`attach_proof` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,527 WARNING database DDL Query made to DB:
create table `tabSalary Structure Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`salary_structure` varchar(140),
`from_date` date,
`income_tax_slab` varchar(140),
`company` varchar(140),
`payroll_payable_account` varchar(140),
`currency` varchar(140),
`base` decimal(21,9) NOT NULL DEFAULT 0.0,
`variable` decimal(21,9) NOT NULL DEFAULT 0.0,
`amended_from` varchar(140),
`taxable_earnings_till_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_deducted_till_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `salary_structure`(`salary_structure`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,626 WARNING database DDL Query made to DB:
create table `tabRetention Bonus` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`date_of_joining` varchar(140),
`salary_component` varchar(140),
`bonus_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`bonus_payment_date` date,
`currency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
