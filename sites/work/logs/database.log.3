2025-04-22 15:36:32,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `dont_create_loyalty_points` tinyint NOT NULL DEFAULT 0, <PERSON><PERSON><PERSON>Y `update_outstanding_for_self` tinyint NOT NULL DEFAULT 1, M<PERSON>IFY `is_debit_note` tinyint NOT NULL DEFAULT 0, <PERSON><PERSON><PERSON><PERSON> `amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0, <PERSON><PERSON><PERSON>Y `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_internal_customer` tinyint NOT NULL DEFAULT 0, M<PERSON><PERSON>Y `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0, M<PERSON><PERSON>Y `update_billed_amount_in_delivery_note` tinyint NOT NULL DEFAULT 1, MOD<PERSON>Y `is_return` tinyint NOT NULL DEFAULT 0, <PERSON><PERSON><PERSON><PERSON> `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, <PERSON><PERSON><PERSON>Y `is_cash_or_non_trade_discount` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `loyalty_points` int NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocate_advances_automatically` tinyint NOT NULL DEFAULT 0, MODIFY `update_billed_amount_in_sales_order` tinyint NOT NULL DEFAULT 0, MODIFY `update_stock` tinyint NOT NULL DEFAULT 0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `redeem_loyalty_points` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `only_include_allocated_payments` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_default_payment_terms_template` tinyint NOT NULL DEFAULT 0, MODIFY `write_off_outstanding_amount_automatically` tinyint NOT NULL DEFAULT 0, MODIFY `is_pos` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_write_off_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_advance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_discounted` tinyint NOT NULL DEFAULT 0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_change_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_company_roundoff_cost_center` tinyint NOT NULL DEFAULT 0, MODIFY `loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_consolidated` tinyint NOT NULL DEFAULT 0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `change_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:32,773 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `debit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:32,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:33,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges Template` MODIFY `is_default` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:33,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `is_a_subscription` tinyint NOT NULL DEFAULT 0, MODIFY `outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `make_sales_invoice` tinyint NOT NULL DEFAULT 0, MODIFY `mute_email` tinyint NOT NULL DEFAULT 0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:33,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabClosed Document` MODIFY `closed` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:33,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Accounting Ledger` MODIFY `delete_cancelled_entries` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:34,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Point Entry Redemption` MODIFY `redeemed_points` int NOT NULL DEFAULT 0
2025-04-22 15:36:34,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Opening Entry Detail` MODIFY `opening_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:34,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Transfer` MODIFY `from_no` int NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `no_of_shares` int NOT NULL DEFAULT 0, MODIFY `to_no` int NOT NULL DEFAULT 0
2025-04-22 15:36:34,533 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension Detail` MODIFY `mandatory_for_pl` tinyint NOT NULL DEFAULT 0, MODIFY `mandatory_for_bs` tinyint NOT NULL DEFAULT 0, MODIFY `automatically_post_balancing_accounting_entry` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:37,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `blog_subscriber` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:37,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:37,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabCampaign Email Schedule` MODIFY `send_after_days` int NOT NULL DEFAULT 0
2025-04-22 15:36:37,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` MODIFY `requires_fulfilment` tinyint NOT NULL DEFAULT 0, MODIFY `is_signed` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:38,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Template` MODIFY `requires_fulfilment` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:38,270 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:38,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Fulfilment Checklist` MODIFY `fulfilled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:38,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect Opportunity` MODIFY `probability` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:39,193 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `first_response_time` decimal(21,9), MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `probability` decimal(21,9) NOT NULL DEFAULT 100.0, MODIFY `opportunity_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_opportunity_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:39,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item Supplied` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_supplied_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:39,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` MODIFY `send_document_print` tinyint NOT NULL DEFAULT 0, MODIFY `send_attached_files` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:39,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Standing` MODIFY `min_grade` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `prevent_pos` tinyint NOT NULL DEFAULT 0, MODIFY `max_grade` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `notify_employee` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `warn_pos` tinyint NOT NULL DEFAULT 0, MODIFY `warn_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `notify_supplier` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:40,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `last_purchase_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `subcontracted_quantity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 1, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `against_blanket_order` tinyint NOT NULL DEFAULT 0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 0, MODIFY `billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `fg_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:40,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:40,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Criteria` MODIFY `max_score` decimal(21,9) NOT NULL DEFAULT 100.0, MODIFY `weight` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:40,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Item` MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:40,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Variable` MODIFY `value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:40,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Criteria` MODIFY `weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `score` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `max_score` decimal(21,9) NOT NULL DEFAULT 100.0
2025-04-22 15:36:40,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Variable` MODIFY `is_custom` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:41,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard` MODIFY `warn_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_pos` tinyint NOT NULL DEFAULT 0, MODIFY `notify_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `notify_employee` tinyint NOT NULL DEFAULT 0, MODIFY `warn_pos` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:41,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Period` MODIFY `total_score` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:41,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Standing` MODIFY `notify_employee` tinyint NOT NULL DEFAULT 0, MODIFY `min_grade` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `warn_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `warn_pos` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_pos` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `max_grade` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `notify_supplier` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:41,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `lead_time_days` int NOT NULL DEFAULT 0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:41,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` MODIFY `allow_purchase_invoice_creation_without_purchase_order` tinyint NOT NULL DEFAULT 0, MODIFY `is_frozen` tinyint NOT NULL DEFAULT 0, MODIFY `is_internal_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `is_transporter` tinyint NOT NULL DEFAULT 0, MODIFY `warn_pos` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `warn_rfqs` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `prevent_pos` tinyint NOT NULL DEFAULT 0, MODIFY `on_hold` tinyint NOT NULL DEFAULT 0, MODIFY `allow_purchase_invoice_creation_without_purchase_receipt` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:41,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_received` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `is_internal_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `advance_paid` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `is_old_subcontracting_flow` tinyint NOT NULL DEFAULT 0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:41,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Supplier` MODIFY `send_email` tinyint NOT NULL DEFAULT 1, MODIFY `email_sent` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:42,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item Supplied` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:42,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Type` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:42,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet Detail` MODIFY `is_billable` tinyint NOT NULL DEFAULT 0, MODIFY `costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `expected_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `completed` tinyint NOT NULL DEFAULT 0, MODIFY `billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:42,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` MODIFY `hide_timesheets` tinyint NOT NULL DEFAULT 0, MODIFY `welcome_email_sent` tinyint NOT NULL DEFAULT 0, MODIFY `view_attachments` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:42,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `is_template` tinyint NOT NULL DEFAULT 0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `expected_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `task_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `duration` int NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `progress` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_milestone` tinyint NOT NULL DEFAULT 0, MODIFY `total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `start` int NOT NULL DEFAULT 0, MODIFY `actual_time` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:42,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Cost` MODIFY `costing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billing_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:43,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `total_billable_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billed_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_hours` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:43,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask Type` MODIFY `weight` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:43,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Template` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:43,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Update` MODIFY `sent` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:43,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `actual_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_consumed_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_sales_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `estimated_costing` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `percent_complete` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `collect_progress` tinyint NOT NULL DEFAULT 0, MODIFY `total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_purchase_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:44,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `loyalty_points` int NOT NULL DEFAULT 0, MODIFY `total_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_picked` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_delivered` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserve_stock` tinyint NOT NULL DEFAULT 0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `advance_paid` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `skip_delivery_note` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_internal_customer` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:44,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduct Bundle` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:45,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduct Bundle Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:45,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `so_required` tinyint NOT NULL DEFAULT 0, MODIFY `is_internal_customer` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_frozen` tinyint NOT NULL DEFAULT 0, MODIFY `dn_required` tinyint NOT NULL DEFAULT 0, MODIFY `default_commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:45,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Credit Limit` MODIFY `credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `bypass_credit_limit_check` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:45,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `against_blanket_order` tinyint NOT NULL DEFAULT 0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_alternative` tinyint NOT NULL DEFAULT 0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `gross_profit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:45,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:46,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `grant_commission` tinyint NOT NULL DEFAULT 0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `gross_profit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `against_blanket_order` tinyint NOT NULL DEFAULT 0, MODIFY `reserve_stock` tinyint NOT NULL DEFAULT 1, MODIFY `blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `work_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `production_plan_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ensure_delivery_based_on_produced_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_stock_item` tinyint NOT NULL DEFAULT 0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `picked_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:46,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:46,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Team` MODIFY `incentives` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allocated_percentage` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:47,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee External Work History` MODIFY `salary` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:47,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record` MODIFY `clear_notifications` tinyint NOT NULL DEFAULT 0, MODIFY `delete_bin_data` tinyint NOT NULL DEFAULT 0, MODIFY `delete_transactions` tinyint NOT NULL DEFAULT 0, MODIFY `reset_company_default_values` tinyint NOT NULL DEFAULT 0, MODIFY `initialize_doctypes_table` tinyint NOT NULL DEFAULT 0, MODIFY `process_in_single_transaction` tinyint NOT NULL DEFAULT 0, MODIFY `delete_leads_and_addresses` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:47,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` MODIFY `buying` tinyint NOT NULL DEFAULT 1, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `selling` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:48,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM` MODIFY `must_be_whole_number` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:48,181 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Group` MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:48,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabHoliday List` MODIFY `total_holidays` int NOT NULL DEFAULT 0
2025-04-22 15:36:48,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `wheels` int NOT NULL DEFAULT 0, MODIFY `doors` int NOT NULL DEFAULT 0, MODIFY `last_odometer` int NOT NULL DEFAULT 0
2025-04-22 15:36:48,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Person` MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:48,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Group` MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0
2025-04-22 15:36:49,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Education` MODIFY `year_of_passing` int NOT NULL DEFAULT 0
2025-04-22 15:36:49,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `notice_number_of_days` int NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `ctc` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `create_user_permission` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:49,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Group` MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:49,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency Exchange` MODIFY `for_buying` tinyint NOT NULL DEFAULT 1, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `for_selling` tinyint NOT NULL DEFAULT 1
2025-04-22 15:36:49,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:36:49,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Partner` MODIFY `show_in_website` tinyint NOT NULL DEFAULT 0, MODIFY `commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:50,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Detail` MODIFY `target_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `target_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:50,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabHoliday` MODIFY `weekly_off` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:50,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `auto_exchange_rate_revaluation` tinyint NOT NULL DEFAULT 0, MODIFY `monthly_sales_target` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `enable_perpetual_inventory` tinyint NOT NULL DEFAULT 1, MODIFY `book_advance_payments_in_separate_party_account` tinyint NOT NULL DEFAULT 0, MODIFY `allow_account_creation_against_child_company` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `total_monthly_sales` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `submit_err_jv` tinyint NOT NULL DEFAULT 0, MODIFY `reconcile_on_advance_payment_date` tinyint NOT NULL DEFAULT 0, MODIFY `credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `enable_provisional_accounting_for_non_stock_items` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:50,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Digest` MODIFY `purchase_orders_items_overdue` tinyint NOT NULL DEFAULT 0, MODIFY `issue` tinyint NOT NULL DEFAULT 0, MODIFY `notifications` tinyint NOT NULL DEFAULT 0, MODIFY `expenses_booked` tinyint NOT NULL DEFAULT 0, MODIFY `calendar_events` tinyint NOT NULL DEFAULT 0, MODIFY `sales_order` tinyint NOT NULL DEFAULT 0, MODIFY `purchase_order` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 0, MODIFY `income_year_to_date` tinyint NOT NULL DEFAULT 0, MODIFY `sales_orders_to_deliver` tinyint NOT NULL DEFAULT 0, MODIFY `payables` tinyint NOT NULL DEFAULT 0, MODIFY `sales_invoice` tinyint NOT NULL DEFAULT 0, MODIFY `invoiced_amount` tinyint NOT NULL DEFAULT 0, MODIFY `purchase_orders_to_bill` tinyint NOT NULL DEFAULT 0, MODIFY `project` tinyint NOT NULL DEFAULT 0, MODIFY `add_quote` tinyint NOT NULL DEFAULT 0, MODIFY `expense_year_to_date` tinyint NOT NULL DEFAULT 0, MODIFY `purchase_invoice` tinyint NOT NULL DEFAULT 0, MODIFY `purchase_orders_to_receive` tinyint NOT NULL DEFAULT 0, MODIFY `new_quotations` tinyint NOT NULL DEFAULT 0, MODIFY `sales_orders_to_bill` tinyint NOT NULL DEFAULT 0, MODIFY `income` tinyint NOT NULL DEFAULT 0, MODIFY `pending_quotations` tinyint NOT NULL DEFAULT 0, MODIFY `credit_balance` tinyint NOT NULL DEFAULT 0, MODIFY `todo_list` tinyint NOT NULL DEFAULT 0, MODIFY `bank_balance` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:50,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuthorization Rule` MODIFY `value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:50,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM Conversion Factor` MODIFY `value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:51,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerritory` MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0
2025-04-22 15:36:51,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `hour_rate_consumable` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate_labour` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `production_capacity` int NOT NULL DEFAULT 1, MODIFY `hour_rate_electricity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_working_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate_rent` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:51,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Operation` MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:51,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Explosion Item` MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sourced_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0, MODIFY `qty_consumed_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:51,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 1, MODIFY `planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `pending_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:51,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` MODIFY `base_cost_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `batch_size` int NOT NULL DEFAULT 0, MODIFY `is_final_finished_good` tinyint NOT NULL DEFAULT 0, MODIFY `cost_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `finished_good_qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `skip_material_transfer` tinyint NOT NULL DEFAULT 0, MODIFY `fixed_time` tinyint NOT NULL DEFAULT 0, MODIFY `set_cost_based_on_bom_qty` tinyint NOT NULL DEFAULT 0, MODIFY `sequence_id` int NOT NULL DEFAULT 0, MODIFY `base_hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Plan Item` MODIFY `requested_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_qty_for_production` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `safety_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `quantity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_bom_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `operation_row_id` int NOT NULL DEFAULT 0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0, MODIFY `available_qty_at_wip_warehouse` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `available_qty_at_source_warehouse` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Working Hour` MODIFY `enabled` tinyint NOT NULL DEFAULT 1, MODIFY `hours` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Operation` MODIFY `completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Item` MODIFY `operation_row_id` int NOT NULL DEFAULT 0, MODIFY `sourced_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `do_not_explode` tinyint NOT NULL DEFAULT 0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_stock_item` tinyint NOT NULL DEFAULT 0, MODIFY `qty_consumed_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_variants` tinyint NOT NULL DEFAULT 0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabSub Operation` MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:52,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `track_semi_finished_goods` tinyint NOT NULL DEFAULT 0, MODIFY `total_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_rate_of_sub_assembly_item_based_on_bom` tinyint NOT NULL DEFAULT 1, MODIFY `base_total_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `show_items` tinyint NOT NULL DEFAULT 0, MODIFY `is_active` tinyint NOT NULL DEFAULT 1, MODIFY `scrap_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_scrap_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `show_operations` tinyint NOT NULL DEFAULT 0, MODIFY `is_default` tinyint NOT NULL DEFAULT 1, MODIFY `fg_based_operating_cost` tinyint NOT NULL DEFAULT 0, MODIFY `show_in_website` tinyint NOT NULL DEFAULT 0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `inspection_required` tinyint NOT NULL DEFAULT 0, MODIFY `raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `quantity` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_variants` tinyint NOT NULL DEFAULT 0, MODIFY `with_operations` tinyint NOT NULL DEFAULT 0, MODIFY `process_loss_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `base_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `skip_material_transfer` tinyint NOT NULL DEFAULT 0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `total_time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `manufactured_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `for_quantity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_corrective_job_card` tinyint NOT NULL DEFAULT 0, MODIFY `process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `sequence_id` int NOT NULL DEFAULT 0, MODIFY `hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `operation_row_id` int NOT NULL DEFAULT 0, MODIFY `is_paused` tinyint NOT NULL DEFAULT 0, MODIFY `time_required` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `track_semi_finished_goods` tinyint NOT NULL DEFAULT 0, MODIFY `current_time` int NOT NULL DEFAULT 0, MODIFY `requested_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabRouting` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:53,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Update Log` MODIFY `current_level` int NOT NULL DEFAULT 0
2025-04-22 15:36:53,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Scrap Item` MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabDowntime Entry` MODIFY `downtime` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlanket Order Item` MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:53,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Item` MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `sequence_id` int NOT NULL DEFAULT 0, MODIFY `batch_size` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `skip_material_transfer` tinyint NOT NULL DEFAULT 0, MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_operation_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `planned_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scheduled Time` MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `wo_produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `indent` int NOT NULL DEFAULT 0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `bom_level` int NOT NULL DEFAULT 0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan` MODIFY `combine_items` tinyint NOT NULL DEFAULT 0, MODIFY `include_non_stock_items` tinyint NOT NULL DEFAULT 1, MODIFY `skip_available_sub_assembly_item` tinyint NOT NULL DEFAULT 1, MODIFY `combine_sub_items` tinyint NOT NULL DEFAULT 0, MODIFY `consider_minimum_order_qty` tinyint NOT NULL DEFAULT 0, MODIFY `total_planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_subcontracted_items` tinyint NOT NULL DEFAULT 1, MODIFY `include_safety_stock` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_existing_ordered_qty` tinyint NOT NULL DEFAULT 1, MODIFY `total_produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `material_transferred_for_manufacturing` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `update_consumed_material_cost_in_project` tinyint NOT NULL DEFAULT 1, MODIFY `total_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `track_semi_finished_goods` tinyint NOT NULL DEFAULT 0, MODIFY `reserve_stock` tinyint NOT NULL DEFAULT 0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `lead_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `from_wip_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `corrective_operation_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `batch_size` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_multi_level_bom` tinyint NOT NULL DEFAULT 1, MODIFY `planned_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `skip_transfer` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:54,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` MODIFY `time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:54,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Type` MODIFY `hour_rate_rent` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate_consumable` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate_electricity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `hour_rate_labour` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:55,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Update Batch` MODIFY `level` int NOT NULL DEFAULT 0, MODIFY `batch_no` int NOT NULL DEFAULT 0
2025-04-22 15:36:55,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `batch_size` int NOT NULL DEFAULT 1, MODIFY `total_operation_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_corrective_operation` tinyint NOT NULL DEFAULT 0, MODIFY `create_job_card_based_on_batch_size` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:55,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator` MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `set_rate_based_on_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:55,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `do_not_explode` tinyint NOT NULL DEFAULT 1, MODIFY `is_expandable` tinyint NOT NULL DEFAULT 0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `bom_created` tinyint NOT NULL DEFAULT 0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sourced_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:55,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sales Order` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:57,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Price` MODIFY `buying` tinyint NOT NULL DEFAULT 0, MODIFY `selling` tinyint NOT NULL DEFAULT 0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `lead_time_days` int NOT NULL DEFAULT 0, MODIFY `packing_unit` int NOT NULL DEFAULT 0
2025-04-22 15:36:57,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Delivery Note` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:58,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `has_item_scanned` tinyint NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `basic_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_scrap_item` tinyint NOT NULL DEFAULT 0, MODIFY `is_finished_item` tinyint NOT NULL DEFAULT 0, MODIFY `transfer_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `basic_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sample_quantity` int NOT NULL DEFAULT 0, MODIFY `set_basic_rate_manually` tinyint NOT NULL DEFAULT 0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `retain_sample` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:58,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` MODIFY `value_of_goods` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `pickup_to` time(6) DEFAULT '17:00', MODIFY `pickup_from` time(6) DEFAULT '09:00', MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `shipment_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:58,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `prompt_qty` tinyint NOT NULL DEFAULT 0, MODIFY `pick_manually` tinyint NOT NULL DEFAULT 0, MODIFY `consider_rejected_warehouses` tinyint NOT NULL DEFAULT 0, MODIFY `for_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `scan_mode` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:58,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `packed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grant_commission` tinyint NOT NULL DEFAULT 0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_item_scanned` tinyint NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `installed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:58,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabInventory Dimension` MODIFY `apply_to_all_doctypes` tinyint NOT NULL DEFAULT 1, MODIFY `istable` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `reqd` tinyint NOT NULL DEFAULT 0, MODIFY `validate_negative_stock` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:58,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `current_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reconcile_all_serial_batch` tinyint NOT NULL DEFAULT 0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount_difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:59,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `from_bom` tinyint NOT NULL DEFAULT 0, MODIFY `add_to_transit` tinyint NOT NULL DEFAULT 0, MODIFY `fg_completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `total_incoming_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `value_difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `inspection_required` tinyint NOT NULL DEFAULT 0, MODIFY `apply_putaway_rule` tinyint NOT NULL DEFAULT 0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_outgoing_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_transferred` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_additional_costs` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_multi_level_bom` tinyint NOT NULL DEFAULT 1, MODIFY `process_loss_percentage` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:59,414 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `numeric_values` tinyint NOT NULL DEFAULT 0, MODIFY `from_range` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `increment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `to_range` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:59,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` MODIFY `recreate_stock_ledgers` tinyint NOT NULL DEFAULT 0, MODIFY `current_index` int NOT NULL DEFAULT 0, MODIFY `total_reposting_count` int NOT NULL DEFAULT 0, MODIFY `allow_zero_rate` tinyint NOT NULL DEFAULT 0, MODIFY `allow_negative_stock` tinyint NOT NULL DEFAULT 1, MODIFY `gl_reposting_index` int NOT NULL DEFAULT 0, MODIFY `via_landed_cost_voucher` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:59,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `enable_deferred_revenue` tinyint NOT NULL DEFAULT 0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `inspection_required_before_purchase` tinyint NOT NULL DEFAULT 0, MODIFY `last_purchase_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grant_commission` tinyint NOT NULL DEFAULT 1, MODIFY `has_variants` tinyint NOT NULL DEFAULT 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_sales_item` tinyint NOT NULL DEFAULT 1, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `safety_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_customer_provided_item` tinyint NOT NULL DEFAULT 0, MODIFY `create_new_batch` tinyint NOT NULL DEFAULT 0, MODIFY `shelf_life_in_days` int NOT NULL DEFAULT 0, MODIFY `standard_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_purchase_item` tinyint NOT NULL DEFAULT 1, MODIFY `lead_time_days` int NOT NULL DEFAULT 0, MODIFY `allow_negative_stock` tinyint NOT NULL DEFAULT 0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `no_of_months_exp` int NOT NULL DEFAULT 0, MODIFY `retain_sample` tinyint NOT NULL DEFAULT 0, MODIFY `no_of_months` int NOT NULL DEFAULT 0, MODIFY `inspection_required_before_delivery` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `enable_deferred_expense` tinyint NOT NULL DEFAULT 0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `has_expiry_date` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `include_item_in_manufacturing` tinyint NOT NULL DEFAULT 1, MODIFY `over_billing_allowance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sample_quantity` int NOT NULL DEFAULT 0, MODIFY `is_sub_contracted_item` tinyint NOT NULL DEFAULT 0, MODIFY `is_grouped_asset` tinyint NOT NULL DEFAULT 0, MODIFY `auto_create_assets` tinyint NOT NULL DEFAULT 0, MODIFY `max_discount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_stock_item` tinyint NOT NULL DEFAULT 1, MODIFY `total_projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:36:59,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabBatch` MODIFY `qty_to_produce` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_batchwise_valuation` tinyint NOT NULL DEFAULT 0
2025-04-22 15:36:59,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` MODIFY `numeric_values` tinyint NOT NULL DEFAULT 0, MODIFY `from_range` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `increment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `to_range` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:00,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking Slip Item` MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:00,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `recalculate_rate` tinyint NOT NULL DEFAULT 0, MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `to_rename` tinyint NOT NULL DEFAULT 1, MODIFY `is_cancelled` tinyint NOT NULL DEFAULT 0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_value_difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `outgoing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `qty_after_transaction` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_adjustment_entry` tinyint NOT NULL DEFAULT 0, MODIFY `auto_created_serial_and_batch_bundle` tinyint NOT NULL DEFAULT 0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:00,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `packed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_batch_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `picked_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:00,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Alternative` MODIFY `two_way` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:00,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Manufacturer` MODIFY `is_default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:00,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabWarehouse` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_rejected_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:37:01,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking Slip` MODIFY `from_case_no` int NOT NULL DEFAULT 0, MODIFY `to_case_no` int NOT NULL DEFAULT 0, MODIFY `gross_weight_pkg` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_weight_pkg` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:01,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection Reading` MODIFY `min_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `formula_based_criteria` tinyint NOT NULL DEFAULT 0, MODIFY `max_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `numeric` tinyint NOT NULL DEFAULT 1, MODIFY `manual_inspection` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:01,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `manual_inspection` tinyint NOT NULL DEFAULT 0, MODIFY `sample_size` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:01,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rm_supp_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `sample_quantity` int NOT NULL DEFAULT 0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `item_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `landed_cost_voucher_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `retain_sample` tinyint NOT NULL DEFAULT 0, MODIFY `is_free_item` tinyint NOT NULL DEFAULT 0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_tds` tinyint NOT NULL DEFAULT 1, MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0, MODIFY `base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `return_qty_from_rejected_warehouse` tinyint NOT NULL DEFAULT 0, MODIFY `sales_incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rejected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount_difference_with_purchase_invoice` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_item_scanned` tinyint NOT NULL DEFAULT 0, MODIFY `received_stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:02,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `print_without_amount` tinyint NOT NULL DEFAULT 0, MODIFY `total_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_internal_customer` tinyint NOT NULL DEFAULT 0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `issue_credit_note` tinyint NOT NULL DEFAULT 0, MODIFY `per_installed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `per_returned` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:02,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Type` MODIFY `is_standard` tinyint NOT NULL DEFAULT 0, MODIFY `add_to_transit` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:02,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Reorder` MODIFY `warehouse_reorder_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `warehouse_reorder_level` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:02,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Entry` MODIFY `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_outward` tinyint NOT NULL DEFAULT 0, MODIFY `outgoing_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_value_difference` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:02,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabPutaway Rule` MODIFY `priority` int NOT NULL DEFAULT 1, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `capacity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_capacity` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:02,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:02,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Parcel Template` MODIFY `width` int NOT NULL DEFAULT 0, MODIFY `height` int NOT NULL DEFAULT 0, MODIFY `length` int NOT NULL DEFAULT 0, MODIFY `weight` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:03,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `warranty_period` int NOT NULL DEFAULT 0
2025-04-22 15:37:03,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `base_taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_returned` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `disable_rounded_total` tinyint NOT NULL DEFAULT 0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `group_same_items` tinyint NOT NULL DEFAULT 0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ignore_pricing_rule` tinyint NOT NULL DEFAULT 0, MODIFY `additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_subcontracted` tinyint NOT NULL DEFAULT 0, MODIFY `base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_internal_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `taxes_and_charges_added` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_old_subcontracting_flow` tinyint NOT NULL DEFAULT 0, MODIFY `base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `apply_putaway_rule` tinyint NOT NULL DEFAULT 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:03,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` MODIFY `total_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_rejected` tinyint NOT NULL DEFAULT 0, MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_cancelled` tinyint NOT NULL DEFAULT 0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `avg_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:03,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin` MODIFY `stock_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `indented_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_qty_for_production_plan` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_qty_for_sub_contract` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reserved_qty_for_production` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:03,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reservation Entry` MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `voucher_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `available_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:03,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` MODIFY `has_corrective_cost` tinyint NOT NULL DEFAULT 0, MODIFY `base_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:04,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `scan_mode` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:04,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `min_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:04,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Stop` MODIFY `grand_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `visited` tinyint NOT NULL DEFAULT 0, MODIFY `locked` tinyint NOT NULL DEFAULT 0, MODIFY `lat` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `lng` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `distance` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:04,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Parcel` MODIFY `width` int NOT NULL DEFAULT 0, MODIFY `weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `length` int NOT NULL DEFAULT 0, MODIFY `count` int NOT NULL DEFAULT 1, MODIFY `height` int NOT NULL DEFAULT 0
2025-04-22 15:37:04,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Quality Inspection Parameter` MODIFY `max_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `formula_based_criteria` tinyint NOT NULL DEFAULT 0, MODIFY `numeric` tinyint NOT NULL DEFAULT 1, MODIFY `min_value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:04,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax` MODIFY `minimum_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `maximum_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:05,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM Conversion Detail` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:05,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice List` MODIFY `price_not_uom_dependent` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 1, MODIFY `buying` tinyint NOT NULL DEFAULT 0, MODIFY `selling` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:05,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:05,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `applicable_charges` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:05,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `picked_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:05,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Closing Balance` MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_value_difference` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:05,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_ordered` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:06,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Trip` MODIFY `email_notification_sent` tinyint NOT NULL DEFAULT 0, MODIFY `total_distance` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:07,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `via_customer_portal` tinyint NOT NULL DEFAULT 0, MODIFY `first_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-04-22 15:37:07,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Level Agreement` MODIFY `apply_sla_for_resolution` tinyint NOT NULL DEFAULT 1, MODIFY `default_service_level_agreement` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 1
2025-04-22 15:37:08,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Level Priority` MODIFY `default_priority` tinyint NOT NULL DEFAULT 0, MODIFY `resolution_time` decimal(21,9), MODIFY `response_time` decimal(21,9)
2025-04-22 15:37:08,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabVideo` MODIFY `like_count` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `duration` decimal(21,9), MODIFY `dislike_count` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `view_count` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `comment_count` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:09,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabLocation` MODIFY `area` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `latitude` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `is_container` tinyint NOT NULL DEFAULT 0, MODIFY `longitude` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:37:09,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Task` MODIFY `certificate_required` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:09,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `stock_consumption` tinyint NOT NULL DEFAULT 0, MODIFY `total_repair_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `increase_in_asset_life` int NOT NULL DEFAULT 0, MODIFY `repair_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `capitalize_repair_cost` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:09,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization` MODIFY `target_has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `target_has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `service_items_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `stock_items_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `target_qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `target_is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `asset_items_total` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `target_incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:09,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` MODIFY `enable_cwip_accounting` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:10,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `difference_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `new_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:10,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:10,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `total_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_number_of_booked_depreciations` int NOT NULL DEFAULT 0, MODIFY `is_fully_depreciated` tinyint NOT NULL DEFAULT 0, MODIFY `total_number_of_depreciations` int NOT NULL DEFAULT 0, MODIFY `is_composite_asset` tinyint NOT NULL DEFAULT 0, MODIFY `value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `booked_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `maintenance_required` tinyint NOT NULL DEFAULT 0, MODIFY `asset_quantity` int NOT NULL DEFAULT 1, MODIFY `is_existing_asset` tinyint NOT NULL DEFAULT 0, MODIFY `frequency_of_depreciation` int NOT NULL DEFAULT 0, MODIFY `gross_purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `calculate_depreciation` tinyint NOT NULL DEFAULT 0, MODIFY `additional_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:10,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:10,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` MODIFY `total_number_of_booked_depreciations` int NOT NULL DEFAULT 0, MODIFY `expected_value_after_useful_life` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `frequency_of_depreciation` int NOT NULL DEFAULT 0, MODIFY `shift_based` tinyint NOT NULL DEFAULT 0, MODIFY `rate_of_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `salvage_value_percentage` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_number_of_depreciations` int NOT NULL DEFAULT 0, MODIFY `daily_prorata_based` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:10,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepreciation Schedule` MODIFY `depreciation_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `accumulated_depreciation_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:11,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Log` MODIFY `has_certificate` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:11,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Purchase Invoice` MODIFY `repair_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:11,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Service Item` MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0
2025-04-22 15:37:11,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Depreciation Schedule` MODIFY `daily_prorata_based` tinyint NOT NULL DEFAULT 0, MODIFY `frequency_of_depreciation` int NOT NULL DEFAULT 0, MODIFY `shift_based` tinyint NOT NULL DEFAULT 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate_of_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_number_of_depreciations` int NOT NULL DEFAULT 0, MODIFY `gross_purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `finance_book_id` int NOT NULL DEFAULT 0, MODIFY `expected_value_after_useful_life` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_number_of_booked_depreciations` int NOT NULL DEFAULT 0
2025-04-22 15:37:11,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Asset Item` MODIFY `asset_value` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_asset_value` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:11,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Shift Factor` MODIFY `shift_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `default` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:12,100 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Item` MODIFY `no_of_visits` int NOT NULL DEFAULT 0
2025-04-22 15:37:12,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabLower Deduction Certificate` MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `certificate_limit` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:14,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Procedure` MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:37:14,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Medium` MODIFY `disabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:37:14,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncoming Call Handling Schedule` MODIFY `from_time` time(6) DEFAULT '9:00:00', MODIFY `to_time` time(6) DEFAULT '17:00:00'
2025-04-22 15:37:14,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabCall Log` MODIFY `duration` decimal(21,9)
2025-04-22 15:37:15,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabBulk Transaction Log Detail` MODIFY `retried` int NOT NULL DEFAULT 0
2025-04-22 15:37:15,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `rm_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `service_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `subcontracting_conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:15,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `fg_item_qty` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:15,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `available_qty_for_consumption` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `current_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:15,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD INDEX `modified`(`modified`)
2025-04-22 15:37:15,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` MODIFY `page_break` tinyint NOT NULL DEFAULT 0, MODIFY `conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `rejected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `rm_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `received_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `scrap_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_scrap_item` tinyint NOT NULL DEFAULT 0, MODIFY `service_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `use_serial_batch_fields` tinyint NOT NULL DEFAULT 0, MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `additional_cost_per_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `include_exploded_items` tinyint NOT NULL DEFAULT 0, MODIFY `rm_supp_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:37:15,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD INDEX `modified`(`modified`)
2025-04-22 15:37:15,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt` MODIFY `total_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_additional_costs` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_return` tinyint NOT NULL DEFAULT 0, MODIFY `per_returned` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `set_posting_time` tinyint NOT NULL DEFAULT 0, MODIFY `total` decimal(21,9) NOT NULL DEFAULT 0.0
