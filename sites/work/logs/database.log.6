2025-04-22 15:34:54,023 WARNING database DDL Query made to DB:
create table `tabProject Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`project` varchar(140),
`sent` tinyint NOT NULL DEFAULT 0,
`date` date,
`time` time(6),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `date`(`date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,166 WARNING database DDL Query made to DB:
create table `tabProject` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`project_name` varchar(140) UNIQUE,
`status` varchar(140) DEFAULT 'Open',
`project_type` varchar(140),
`is_active` varchar(140),
`percent_complete_method` varchar(140) DEFAULT 'Task Completion',
`percent_complete` decimal(21,9) NOT NULL DEFAULT 0.0,
`project_template` varchar(140),
`expected_start_date` date,
`expected_end_date` date,
`priority` varchar(140),
`department` varchar(140),
`customer` varchar(140),
`sales_order` varchar(140),
`copied_from` varchar(140),
`notes` longtext,
`actual_start_date` date,
`actual_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_end_date` date,
`estimated_costing` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_purchase_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`company` varchar(140),
`total_sales_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_consumed_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`per_gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`collect_progress` tinyint NOT NULL DEFAULT 0,
`holiday_list` varchar(140),
`frequency` varchar(140),
`from_time` time(6),
`to_time` time(6),
`first_email` time(6),
`second_email` time(6),
`daily_time_to_send` time(6),
`day_to_send` varchar(140),
`weekly_time_to_send` time(6),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `customer`(`customer`),
index `collect_progress`(`collect_progress`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,238 WARNING database DDL Query made to DB:
create table `tabTask Depends On` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`task` varchar(140),
`subject` text,
`project` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,397 WARNING database DDL Query made to DB:
create table `tabParty Specific Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`party_type` varchar(140),
`party` varchar(140),
`restrict_based_on` varchar(140),
`based_on_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,652 WARNING database DDL Query made to DB:
create table `tabSales Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`tax_id` varchar(140),
`order_type` varchar(140) DEFAULT 'Sales',
`transaction_date` date,
`delivery_date` date,
`po_no` varchar(140),
`po_date` date,
`company` varchar(140),
`skip_delivery_note` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`set_warehouse` varchar(140),
`reserve_stock` tinyint NOT NULL DEFAULT 0,
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`in_words` varchar(240),
`advance_paid` decimal(21,9) NOT NULL DEFAULT 0.0,
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`coupon_code` varchar(140),
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`other_charges_calculation` longtext,
`customer_address` varchar(140),
`address_display` longtext,
`customer_group` varchar(140),
`territory` varchar(140),
`contact_person` varchar(140),
`contact_display` text,
`contact_phone` varchar(140),
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` longtext,
`dispatch_address_name` varchar(140),
`dispatch_address` longtext,
`company_address` varchar(140),
`company_address_display` longtext,
`company_contact_person` varchar(140),
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) DEFAULT 'Draft',
`delivery_status` varchar(140),
`per_delivered` decimal(21,9) NOT NULL DEFAULT 0.0,
`per_billed` decimal(21,9) NOT NULL DEFAULT 0.0,
`per_picked` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_status` varchar(140),
`advance_payment_status` varchar(140),
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_commission` decimal(21,9) NOT NULL DEFAULT 0.0,
`loyalty_points` int NOT NULL DEFAULT 0,
`loyalty_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`from_date` date,
`to_date` date,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`is_internal_customer` tinyint NOT NULL DEFAULT 0,
`represents_company` varchar(140),
`utm_source` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`utm_content` varchar(140),
`inter_company_order_reference` varchar(140),
`party_account_currency` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `transaction_date`(`transaction_date`),
index `project`(`project`),
index `status`(`status`),
index `inter_company_order_reference`(`inter_company_order_reference`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,845 WARNING database DDL Query made to DB:
create table `tabInstallation Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_address` varchar(140),
`contact_person` varchar(140),
`customer_name` varchar(140),
`address_display` longtext,
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`inst_date` date,
`inst_time` time(6),
`status` varchar(140) DEFAULT 'Draft',
`company` varchar(140),
`project` varchar(140),
`amended_from` varchar(140),
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index `territory`(`territory`),
index `inst_date`(`inst_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:54,973 WARNING database DDL Query made to DB:
create table `tabProduct Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`new_item_code` varchar(140),
`description` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,052 WARNING database DDL Query made to DB:
create table `tabProduct Bundle Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` longtext,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,190 WARNING database DDL Query made to DB:
create table `tabCustomer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`salutation` varchar(140),
`customer_name` varchar(140),
`customer_type` varchar(140) DEFAULT 'Company',
`customer_group` varchar(140),
`territory` varchar(140),
`gender` varchar(140),
`lead_name` varchar(140),
`opportunity_name` varchar(140),
`prospect_name` varchar(140),
`account_manager` varchar(140),
`image` text,
`default_currency` varchar(140),
`default_bank_account` varchar(140),
`default_price_list` varchar(140),
`is_internal_customer` tinyint NOT NULL DEFAULT 0,
`represents_company` varchar(140) UNIQUE,
`market_segment` varchar(140),
`industry` varchar(140),
`customer_pos_id` varchar(140),
`website` varchar(140),
`language` varchar(140),
`customer_details` text,
`customer_primary_address` varchar(140),
`primary_address` text,
`customer_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`tax_id` varchar(140),
`tax_category` varchar(140),
`tax_withholding_category` varchar(140),
`payment_terms` varchar(140),
`loyalty_program` varchar(140),
`loyalty_program_tier` varchar(140),
`default_sales_partner` varchar(140),
`default_commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`so_required` tinyint NOT NULL DEFAULT 0,
`dn_required` tinyint NOT NULL DEFAULT 0,
`is_frozen` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer_name`(`customer_name`),
index `customer_group`(`customer_group`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,273 WARNING database DDL Query made to DB:
create table `tabCustomer Credit Limit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0,
`bypass_credit_limit_check` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,385 WARNING database DDL Query made to DB:
create table `tabQuotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`customer_item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`is_alternative` tinyint NOT NULL DEFAULT 0,
`has_alternative_item` tinyint NOT NULL DEFAULT 0,
`valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`gross_profit` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`against_blanket_order` tinyint NOT NULL DEFAULT 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_rate` longtext,
`additional_notes` text,
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,556 WARNING database DDL Query made to DB:
create table `tabQuotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`quotation_to` varchar(140) DEFAULT 'Customer',
`party_name` varchar(140),
`customer_name` varchar(140),
`transaction_date` date,
`valid_till` date,
`order_type` varchar(140) DEFAULT 'Sales',
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ignore_pricing_rule` tinyint NOT NULL DEFAULT 0,
`scan_barcode` varchar(140),
`total_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_net_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounding_adjustment` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`disable_rounded_total` tinyint NOT NULL DEFAULT 0,
`in_words` varchar(240),
`apply_discount_on` varchar(140) DEFAULT 'Grand Total',
`base_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`coupon_code` varchar(140),
`additional_discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`referral_sales_partner` varchar(140),
`other_charges_calculation` longtext,
`customer_address` varchar(140),
`address_display` longtext,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` longtext,
`company_address` varchar(140),
`company_address_display` longtext,
`company_contact_person` varchar(140),
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`group_same_items` tinyint NOT NULL DEFAULT 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`order_lost_reason` text,
`status` varchar(140) DEFAULT 'Draft',
`customer_group` varchar(140),
`territory` varchar(140),
`utm_source` varchar(140),
`utm_campaign` varchar(140),
`utm_medium` varchar(140),
`utm_content` varchar(140),
`opportunity` varchar(140),
`supplier_quotation` varchar(140),
`enq_det` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `party_name`(`party_name`),
index `transaction_date`(`transaction_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,741 WARNING database DDL Query made to DB:
create table `tabSales Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`customer_item_code` varchar(140),
`ensure_delivery_based_on_produced_serial_no` tinyint NOT NULL DEFAULT 0,
`is_stock_item` tinyint NOT NULL DEFAULT 0,
`reserve_stock` tinyint NOT NULL DEFAULT 1,
`delivery_date` date,
`item_name` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`grant_commission` tinyint NOT NULL DEFAULT 0,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`billed_amt` decimal(21,9) NOT NULL DEFAULT 0.0,
`valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`gross_profit` decimal(21,9) NOT NULL DEFAULT 0.0,
`delivered_by_supplier` tinyint NOT NULL DEFAULT 0,
`supplier` varchar(140),
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`prevdoc_docname` varchar(140),
`quotation_item` varchar(140),
`against_blanket_order` tinyint NOT NULL DEFAULT 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`company_total_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
`bom_no` varchar(140),
`projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`production_plan_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`work_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`picked_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_notes` text,
`page_break` tinyint NOT NULL DEFAULT 0,
`item_tax_rate` longtext,
`transaction_date` date,
`material_request` varchar(140),
`purchase_order` varchar(140),
`material_request_item` varchar(140),
`purchase_order_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
index `prevdoc_docname`(`prevdoc_docname`),
index `purchase_order`(`purchase_order`),
index `project`(`project`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-04-22 15:34:55,851 WARNING database DDL Query made to DB:
create table `tabInstallation Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`serial_and_batch_bundle` varchar(140),
`serial_no` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` longtext,
`prevdoc_detail_docname` varchar(140),
`prevdoc_docname` varchar(140),
`prevdoc_doctype` varchar(140),
index `prevdoc_docname`(`prevdoc_docname`),
index `prevdoc_doctype`(`prevdoc_doctype`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,924 WARNING database DDL Query made to DB:
create table `tabIndustry Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`industry` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:55,993 WARNING database DDL Query made to DB:
create table `tabSales Partner Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_partner_type` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,060 WARNING database DDL Query made to DB:
create table `tabSales Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_person` varchar(140),
`contact_no` varchar(140),
`allocated_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`allocated_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`commission_rate` varchar(140),
`incentives` decimal(21,9) NOT NULL DEFAULT 0.0,
index `sales_person`(`sales_person`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,512 WARNING database DDL Query made to DB:
create table `tabEmployee Internal Work History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`from_date` date,
`to_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,642 WARNING database DDL Query made to DB:
create table `tabEmployee Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee_group_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,709 WARNING database DDL Query made to DB:
create table `tabEmployee External Work History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company_name` varchar(140),
`designation` varchar(140),
`salary` decimal(21,9) NOT NULL DEFAULT 0.0,
`address` text,
`contact` varchar(140),
`total_experience` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,787 WARNING database DDL Query made to DB:
create table `tabTransaction Deletion Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`status` varchar(140),
`error_log` longtext,
`delete_bin_data` tinyint NOT NULL DEFAULT 0,
`delete_leads_and_addresses` tinyint NOT NULL DEFAULT 0,
`reset_company_default_values` tinyint NOT NULL DEFAULT 0,
`clear_notifications` tinyint NOT NULL DEFAULT 0,
`initialize_doctypes_table` tinyint NOT NULL DEFAULT 0,
`delete_transactions` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`process_in_single_transaction` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,861 WARNING database DDL Query made to DB:
create table `tabBrand` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`brand` varchar(140) UNIQUE,
`image` text,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:56,940 WARNING database DDL Query made to DB:
create table `tabDriving License Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`class` varchar(140),
`description` varchar(140),
`issuing_date` date,
`expiry_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,008 WARNING database DDL Query made to DB:
create table `tabTerms and Conditions` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`selling` tinyint NOT NULL DEFAULT 1,
`buying` tinyint NOT NULL DEFAULT 1,
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,088 WARNING database DDL Query made to DB:
create table `tabWebsite Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,144 WARNING database DDL Query made to DB:
create table `tabEmail Digest Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`recipient` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,216 WARNING database DDL Query made to DB:
create table `tabUOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`uom_name` varchar(140) UNIQUE,
`symbol` varchar(140),
`common_code` varchar(3),
`description` text,
`enabled` tinyint NOT NULL DEFAULT 1,
`must_be_whole_number` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,286 WARNING database DDL Query made to DB:
create table `tabEmployee Group Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`user_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,376 WARNING database DDL Query made to DB:
create table `tabSupplier Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier_group_name` varchar(140) UNIQUE,
`parent_supplier_group` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`payment_terms` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,463 WARNING database DDL Query made to DB:
create table `tabHoliday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`holiday_list_name` varchar(140) UNIQUE,
`from_date` date,
`to_date` date,
`total_holidays` int NOT NULL DEFAULT 0,
`weekly_off` varchar(140),
`country` varchar(140),
`subdivision` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,544 WARNING database DDL Query made to DB:
create table `tabIncoterm` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`code` varchar(3) UNIQUE,
`title` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,674 WARNING database DDL Query made to DB:
create table `tabVehicle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`license_plate` varchar(140) UNIQUE,
`make` varchar(140),
`model` varchar(140),
`company` varchar(140),
`last_odometer` int NOT NULL DEFAULT 0,
`acquisition_date` date,
`location` varchar(140),
`chassis_no` varchar(140),
`vehicle_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`employee` varchar(140),
`insurance_company` varchar(140),
`policy_no` varchar(140),
`start_date` date,
`end_date` date,
`fuel_type` varchar(140),
`uom` varchar(140),
`carbon_check_date` date,
`color` varchar(140),
`wheels` int NOT NULL DEFAULT 0,
`doors` int NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,778 WARNING database DDL Query made to DB:
create table `tabSales Person` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_person_name` varchar(140) UNIQUE,
`parent_sales_person` varchar(140),
`commission_rate` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`enabled` tinyint NOT NULL DEFAULT 1,
`employee` varchar(140),
`department` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Person`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:57,886 WARNING database DDL Query made to DB:
create table `tabQuotation Lost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:57,981 WARNING database DDL Query made to DB:
create table `tabItem Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_group_name` varchar(140) UNIQUE,
`parent_item_group` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`image` text,
`lft` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`rgt` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,057 WARNING database DDL Query made to DB:
create table `tabEmployee Education` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`school_univ` text,
`qualification` varchar(140),
`level` varchar(140),
`year_of_passing` int NOT NULL DEFAULT 0,
`class_per` varchar(140),
`maj_opt_subj` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,121 WARNING database DDL Query made to DB:
create table `tabParty Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`party_type` varchar(140) UNIQUE,
`account_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,296 WARNING database DDL Query made to DB:
create table `tabEmployee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`employee_name` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
`salutation` varchar(140),
`date_of_joining` date,
`image` text,
`status` varchar(140) DEFAULT 'Active',
`user_id` varchar(140),
`create_user_permission` tinyint NOT NULL DEFAULT 1,
`company` varchar(140),
`department` varchar(140),
`employee_number` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`branch` varchar(140),
`scheduled_confirmation_date` date,
`final_confirmation_date` date,
`contract_end_date` date,
`notice_number_of_days` int NOT NULL DEFAULT 0,
`date_of_retirement` date,
`cell_number` varchar(140),
`personal_email` varchar(140),
`company_email` varchar(140),
`prefered_contact_email` varchar(140),
`prefered_email` varchar(140),
`unsubscribed` tinyint NOT NULL DEFAULT 0,
`current_address` text,
`current_accommodation_type` varchar(140),
`permanent_address` text,
`permanent_accommodation_type` varchar(140),
`person_to_be_contacted` varchar(140),
`emergency_phone_number` varchar(140),
`relation` varchar(140),
`attendance_device_id` varchar(140) UNIQUE,
`holiday_list` varchar(140),
`ctc` decimal(21,9) NOT NULL DEFAULT 0.0,
`salary_currency` varchar(140),
`salary_mode` varchar(140),
`bank_name` varchar(140),
`bank_ac_no` varchar(140),
`iban` varchar(140),
`marital_status` varchar(140),
`family_background` text,
`blood_group` varchar(140),
`health_details` text,
`passport_number` varchar(140),
`valid_upto` date,
`date_of_issue` date,
`place_of_issue` varchar(140),
`bio` longtext,
`resignation_letter_date` date,
`relieving_date` date,
`held_on` date,
`new_workplace` varchar(140),
`leave_encashed` varchar(140),
`encashment_date` date,
`reason_for_leaving` text,
`feedback` text,
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `designation`(`designation`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,354 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:58,400 WARNING database DDL Query made to DB:
create table `tabBranch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`branch` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,537 WARNING database DDL Query made to DB:
create table `tabCustomer Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`customer_group_name` varchar(140) UNIQUE,
`parent_customer_group` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`default_price_list` varchar(140),
`payment_terms` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Group`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:58,784 WARNING database DDL Query made to DB:
create table `tabCurrency Exchange` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`date` date,
`from_currency` varchar(140),
`to_currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_buying` tinyint NOT NULL DEFAULT 1,
`for_selling` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,897 WARNING database DDL Query made to DB:
create table `tabDepartment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`department_name` varchar(140),
`parent_department` varchar(140),
`company` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:58,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:34:59,051 WARNING database DDL Query made to DB:
create table `tabSales Partner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`partner_name` varchar(140) UNIQUE,
`partner_type` varchar(140),
`territory` varchar(140),
`commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`show_in_website` tinyint NOT NULL DEFAULT 0,
`referral_code` varchar(8) UNIQUE,
`route` varchar(140) UNIQUE,
`logo` text,
`partner_website` varchar(140),
`introduction` text,
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,171 WARNING database DDL Query made to DB:
create table `tabTarget Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_group` varchar(140),
`fiscal_year` varchar(140),
`target_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`target_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distribution_id` varchar(140),
index `item_group`(`item_group`),
index `fiscal_year`(`fiscal_year`),
index `target_amount`(`target_amount`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,236 WARNING database DDL Query made to DB:
create table `tabTransaction Deletion Record Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`doctype_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,301 WARNING database DDL Query made to DB:
create table `tabHoliday` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`holiday_date` date,
`weekly_off` tinyint NOT NULL DEFAULT 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,496 WARNING database DDL Query made to DB:
create table `tabCompany` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company_name` varchar(140) UNIQUE,
`abbr` varchar(140),
`default_currency` varchar(140),
`country` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`default_holiday_list` varchar(140),
`default_letter_head` varchar(140),
`tax_id` varchar(140),
`domain` varchar(140),
`date_of_establishment` date,
`parent_company` varchar(140),
`company_logo` text,
`date_of_incorporation` date,
`phone_no` varchar(140),
`email` varchar(140),
`company_description` longtext,
`date_of_commencement` date,
`fax` varchar(140),
`website` varchar(140),
`registration_details` longtext,
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`create_chart_of_accounts_based_on` varchar(140),
`existing_company` varchar(140),
`chart_of_accounts` varchar(140),
`default_bank_account` varchar(140),
`default_cash_account` varchar(140),
`default_receivable_account` varchar(140),
`default_payable_account` varchar(140),
`write_off_account` varchar(140),
`unrealized_profit_loss_account` varchar(140),
`allow_account_creation_against_child_company` tinyint NOT NULL DEFAULT 0,
`default_expense_account` varchar(140),
`default_income_account` varchar(140),
`default_discount_account` varchar(140),
`payment_terms` varchar(140),
`cost_center` varchar(140),
`default_finance_book` varchar(140),
`exchange_gain_loss_account` varchar(140),
`unrealized_exchange_gain_loss_account` varchar(140),
`round_off_account` varchar(140),
`round_off_cost_center` varchar(140),
`round_off_for_opening` varchar(140),
`default_deferred_revenue_account` varchar(140),
`default_deferred_expense_account` varchar(140),
`book_advance_payments_in_separate_party_account` tinyint NOT NULL DEFAULT 0,
`reconcile_on_advance_payment_date` tinyint NOT NULL DEFAULT 0,
`reconciliation_takes_effect_on` varchar(140) DEFAULT 'Oldest Of Invoice Or Advance',
`default_advance_received_account` varchar(140),
`default_advance_paid_account` varchar(140),
`auto_exchange_rate_revaluation` tinyint NOT NULL DEFAULT 0,
`auto_err_frequency` varchar(140),
`submit_err_jv` tinyint NOT NULL DEFAULT 0,
`exception_budget_approver_role` varchar(140),
`accumulated_depreciation_account` varchar(140),
`depreciation_expense_account` varchar(140),
`series_for_depreciation_entry` varchar(140),
`disposal_account` varchar(140),
`depreciation_cost_center` varchar(140),
`capital_work_in_progress_account` varchar(140),
`asset_received_but_not_billed` varchar(140),
`default_buying_terms` varchar(140),
`sales_monthly_history` text,
`monthly_sales_target` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_monthly_sales` decimal(21,9) NOT NULL DEFAULT 0.0,
`default_selling_terms` varchar(140),
`default_warehouse_for_sales_return` varchar(140),
`credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0,
`transactions_annual_history` longtext,
`enable_perpetual_inventory` tinyint NOT NULL DEFAULT 1,
`enable_provisional_accounting_for_non_stock_items` tinyint NOT NULL DEFAULT 0,
`default_inventory_account` varchar(140),
`stock_adjustment_account` varchar(140),
`stock_received_but_not_billed` varchar(140),
`default_provisional_account` varchar(140),
`default_in_transit_warehouse` varchar(140),
`default_operating_cost_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,626 WARNING database DDL Query made to DB:
create table `tabEmail Digest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 0,
`company` varchar(140),
`frequency` varchar(140),
`next_send` varchar(140),
`income` tinyint NOT NULL DEFAULT 0,
`expenses_booked` tinyint NOT NULL DEFAULT 0,
`income_year_to_date` tinyint NOT NULL DEFAULT 0,
`expense_year_to_date` tinyint NOT NULL DEFAULT 0,
`bank_balance` tinyint NOT NULL DEFAULT 0,
`credit_balance` tinyint NOT NULL DEFAULT 0,
`invoiced_amount` tinyint NOT NULL DEFAULT 0,
`payables` tinyint NOT NULL DEFAULT 0,
`sales_orders_to_bill` tinyint NOT NULL DEFAULT 0,
`purchase_orders_to_bill` tinyint NOT NULL DEFAULT 0,
`sales_order` tinyint NOT NULL DEFAULT 0,
`purchase_order` tinyint NOT NULL DEFAULT 0,
`sales_orders_to_deliver` tinyint NOT NULL DEFAULT 0,
`purchase_orders_to_receive` tinyint NOT NULL DEFAULT 0,
`sales_invoice` tinyint NOT NULL DEFAULT 0,
`purchase_invoice` tinyint NOT NULL DEFAULT 0,
`new_quotations` tinyint NOT NULL DEFAULT 0,
`pending_quotations` tinyint NOT NULL DEFAULT 0,
`issue` tinyint NOT NULL DEFAULT 0,
`project` tinyint NOT NULL DEFAULT 0,
`purchase_orders_items_overdue` tinyint NOT NULL DEFAULT 0,
`calendar_events` tinyint NOT NULL DEFAULT 0,
`todo_list` tinyint NOT NULL DEFAULT 0,
`notifications` tinyint NOT NULL DEFAULT 0,
`add_quote` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,729 WARNING database DDL Query made to DB:
create table `tabAuthorization Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`transaction` varchar(140),
`based_on` varchar(140),
`customer_or_item` varchar(140),
`master_name` varchar(140),
`company` varchar(140),
`value` decimal(21,9) NOT NULL DEFAULT 0.0,
`system_role` varchar(140),
`to_emp` varchar(140),
`system_user` varchar(140),
`to_designation` varchar(140),
`approving_role` varchar(140),
`approving_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,809 WARNING database DDL Query made to DB:
create table `tabQuotation Lost Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`order_lost_reason` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,889 WARNING database DDL Query made to DB:
create table `tabDesignation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`designation_name` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:59,961 WARNING database DDL Query made to DB:
create table `tabUOM Conversion Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`category` varchar(140),
`from_uom` varchar(140),
`to_uom` varchar(140),
`value` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,044 WARNING database DDL Query made to DB:
create table `tabDriver` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`full_name` varchar(140),
`status` varchar(140),
`transporter` varchar(140),
`employee` varchar(140),
`cell_number` varchar(140),
`address` varchar(140),
`user` varchar(140),
`license_number` varchar(140),
`issuing_date` date,
`expiry_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,168 WARNING database DDL Query made to DB:
create table `tabTerritory` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`territory_name` varchar(140) UNIQUE,
`parent_territory` varchar(140),
`is_group` tinyint NOT NULL DEFAULT 0,
`territory_manager` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `territory_manager`(`territory_manager`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerritory`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-04-22 15:35:00,339 WARNING database DDL Query made to DB:
create table `tabPlant Floor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`floor_name` varchar(140) UNIQUE,
`company` varchar(140),
`warehouse` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,436 WARNING database DDL Query made to DB:
create table `tabWorkstation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workstation_name` varchar(140) UNIQUE,
`workstation_type` varchar(140),
`plant_floor` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`production_capacity` int NOT NULL DEFAULT 1,
`warehouse` varchar(140),
`status` varchar(140),
`on_status_image` text,
`off_status_image` text,
`hour_rate_electricity` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_consumable` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_rent` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_labour` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`holiday_list` varchar(140),
`total_working_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,519 WARNING database DDL Query made to DB:
create table `tabBOM Website Operation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`operation` varchar(140),
`workstation` varchar(140),
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`website_image` text,
`thumbnail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,595 WARNING database DDL Query made to DB:
create table `tabBOM Explosion Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`source_warehouse` varchar(140),
`operation` varchar(140),
`description` longtext,
`image` text,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`qty_consumed_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0,
`sourced_by_supplier` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,707 WARNING database DDL Query made to DB:
create table `tabProduction Plan Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`include_exploded_items` tinyint NOT NULL DEFAULT 1,
`item_code` varchar(140),
`bom_no` varchar(140),
`planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`warehouse` varchar(140),
`planned_start_date` datetime(6),
`pending_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` longtext,
`produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`product_bundle_item` varchar(140),
`item_reference` varchar(140),
`temporary_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,788 WARNING database DDL Query made to DB:
create table `tabBOM Operation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`operation` varchar(140),
`sequence_id` int NOT NULL DEFAULT 0,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`bom_no` varchar(140),
`workstation_type` varchar(140),
`workstation` varchar(140),
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`fixed_time` tinyint NOT NULL DEFAULT 0,
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`is_final_finished_good` tinyint NOT NULL DEFAULT 0,
`set_cost_based_on_bom_qty` tinyint NOT NULL DEFAULT 0,
`skip_material_transfer` tinyint NOT NULL DEFAULT 0,
`backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0,
`source_warehouse` varchar(140),
`wip_warehouse` varchar(140),
`fg_warehouse` varchar(140),
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`batch_size` int NOT NULL DEFAULT 0,
`cost_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_cost_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` longtext,
`image` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,878 WARNING database DDL Query made to DB:
create table `tabMaterial Request Plan Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`item_name` varchar(140),
`material_request_type` varchar(140),
`quantity` decimal(21,9) NOT NULL DEFAULT 0.0,
`required_bom_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`schedule_date` date,
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` longtext,
`min_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`sales_order` varchar(140),
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`requested_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`reserved_qty_for_production` decimal(21,9) NOT NULL DEFAULT 0.0,
`ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`safety_stock` decimal(21,9) NOT NULL DEFAULT 0.0,
index `item_code`(`item_code`),
index `from_warehouse`(`from_warehouse`),
index `warehouse`(`warehouse`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:00,968 WARNING database DDL Query made to DB:
create table `tabWork Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`operation` varchar(140),
`item_code` varchar(140),
`source_warehouse` varchar(140),
`operation_row_id` int NOT NULL DEFAULT 0,
`item_name` varchar(140),
`description` text,
`allow_alternative_item` tinyint NOT NULL DEFAULT 0,
`include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`consumed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`returned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`available_qty_at_source_warehouse` decimal(21,9) NOT NULL DEFAULT 0.0,
`available_qty_at_wip_warehouse` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item`
				ADD INDEX IF NOT EXISTS `item_code_source_warehouse_index`(item_code, source_warehouse)
2025-04-22 15:35:01,061 WARNING database DDL Query made to DB:
create table `tabProduction Plan Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`material_request` varchar(140),
`material_request_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,126 WARNING database DDL Query made to DB:
create table `tabWorkstation Working Hour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`start_time` time(6),
`hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`end_time` time(6),
`enabled` tinyint NOT NULL DEFAULT 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,190 WARNING database DDL Query made to DB:
create table `tabJob Card Operation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sub_operation` varchar(140),
`completed_time` varchar(140),
`status` varchar(140) DEFAULT 'Pending',
`completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,288 WARNING database DDL Query made to DB:
create table `tabBOM Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`operation` varchar(140),
`operation_row_id` int NOT NULL DEFAULT 0,
`do_not_explode` tinyint NOT NULL DEFAULT 0,
`bom_no` varchar(140),
`source_warehouse` varchar(140),
`allow_alternative_item` tinyint NOT NULL DEFAULT 0,
`is_stock_item` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`image` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`uom` varchar(140),
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`qty_consumed_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`has_variants` tinyint NOT NULL DEFAULT 0,
`include_item_in_manufacturing` tinyint NOT NULL DEFAULT 0,
`original_item` varchar(140),
`sourced_by_supplier` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `bom_no`(`bom_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,351 WARNING database DDL Query made to DB:
create table `tabSub Operation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`operation` varchar(140),
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,541 WARNING database DDL Query made to DB:
create table `tabBOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item` varchar(140),
`company` varchar(140),
`uom` varchar(140),
`quantity` decimal(21,9) NOT NULL DEFAULT 1.0,
`is_active` tinyint NOT NULL DEFAULT 1,
`is_default` tinyint NOT NULL DEFAULT 1,
`allow_alternative_item` tinyint NOT NULL DEFAULT 0,
`set_rate_of_sub_assembly_item_based_on_bom` tinyint NOT NULL DEFAULT 1,
`project` varchar(140),
`image` text,
`rm_cost_as_per` varchar(140) DEFAULT 'Valuation Rate',
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 1.0,
`with_operations` tinyint NOT NULL DEFAULT 0,
`track_semi_finished_goods` tinyint NOT NULL DEFAULT 0,
`transfer_material_against` varchar(140) DEFAULT 'Work Order',
`routing` varchar(140),
`fg_based_operating_cost` tinyint NOT NULL DEFAULT 0,
`default_source_warehouse` varchar(140),
`default_target_warehouse` varchar(140),
`operating_cost_per_bom_quantity` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_loss_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`scrap_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_scrap_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_name` varchar(140),
`description` text,
`has_variants` tinyint NOT NULL DEFAULT 0,
`inspection_required` tinyint NOT NULL DEFAULT 0,
`quality_inspection_template` varchar(140),
`show_in_website` tinyint NOT NULL DEFAULT 0,
`route` text,
`website_image` text,
`thumbnail` varchar(140),
`show_items` tinyint NOT NULL DEFAULT 0,
`show_operations` tinyint NOT NULL DEFAULT 0,
`web_long_description` longtext,
`bom_creator` varchar(140),
`bom_creator_item` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item`(`item`),
index `bom_creator`(`bom_creator`),
index `bom_creator_item`(`bom_creator_item`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,621 WARNING database DDL Query made to DB:
create table `tabProduction Plan Material Request Warehouse` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,698 WARNING database DDL Query made to DB:
create table `tabJob Card Scrap Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,873 WARNING database DDL Query made to DB:
create table `tabJob Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'PO-JOB.#####',
`work_order` varchar(140),
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`posting_date` date,
`company` varchar(140),
`project` varchar(140),
`bom_no` varchar(140),
`finished_good` varchar(140),
`production_item` varchar(140),
`semi_fg_bom` varchar(140),
`total_completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_quantity` decimal(21,9) NOT NULL DEFAULT 0.0,
`transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`manufactured_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`operation` varchar(140),
`source_warehouse` varchar(140),
`wip_warehouse` varchar(140),
`skip_material_transfer` tinyint NOT NULL DEFAULT 0,
`backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0,
`workstation_type` varchar(140),
`workstation` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection_template` varchar(140),
`quality_inspection` varchar(140),
`expected_start_date` datetime(6),
`time_required` decimal(21,9) NOT NULL DEFAULT 0.0,
`expected_end_date` datetime(6),
`actual_start_date` datetime(6),
`total_time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_end_date` datetime(6),
`for_job_card` varchar(140),
`is_corrective_job_card` tinyint NOT NULL DEFAULT 0,
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`for_operation` varchar(140),
`item_name` varchar(140),
`requested_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Open',
`operation_row_id` int NOT NULL DEFAULT 0,
`is_paused` tinyint NOT NULL DEFAULT 0,
`track_semi_finished_goods` tinyint NOT NULL DEFAULT 0,
`operation_row_number` varchar(140),
`operation_id` varchar(140),
`sequence_id` int NOT NULL DEFAULT 0,
`remarks` text,
`serial_and_batch_bundle` varchar(140),
`batch_no` varchar(140),
`serial_no` text,
`barcode` longtext,
`started_time` datetime(6),
`current_time` int NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `work_order`(`work_order`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:01,995 WARNING database DDL Query made to DB:
create table `tabBlanket Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`blanket_order_type` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`order_no` varchar(140),
`order_date` date,
`from_date` date,
`to_date` date,
`company` varchar(140),
`amended_from` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,069 WARNING database DDL Query made to DB:
create table `tabRouting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`routing_name` varchar(140) UNIQUE,
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,163 WARNING database DDL Query made to DB:
create table `tabBOM Update Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`update_type` varchar(140),
`status` varchar(140),
`current_bom` varchar(140),
`new_bom` varchar(140),
`error_log` varchar(140),
`current_level` int NOT NULL DEFAULT 0,
`processed_boms` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,242 WARNING database DDL Query made to DB:
create table `tabBOM Scrap Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,313 WARNING database DDL Query made to DB:
create table `tabDowntime Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`workstation` varchar(140),
`operator` varchar(140),
`from_time` datetime(6),
`to_time` datetime(6),
`downtime` decimal(21,9) NOT NULL DEFAULT 0.0,
`stop_reason` varchar(140),
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,391 WARNING database DDL Query made to DB:
create table `tabBlanket Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`party_item_code` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`terms_and_conditions` text,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,460 WARNING database DDL Query made to DB:
create table `tabJob Card Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`source_warehouse` varchar(140),
`uom` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`item_name` varchar(140),
`description` text,
`required_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`transferred_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`allow_alternative_item` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,567 WARNING database DDL Query made to DB:
create table `tabWork Order Operation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`operation` varchar(140),
`status` varchar(140) DEFAULT 'Pending',
`completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`bom` varchar(140),
`workstation_type` varchar(140),
`workstation` varchar(140),
`sequence_id` int NOT NULL DEFAULT 0,
`bom_no` varchar(140),
`finished_good` varchar(140),
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`skip_material_transfer` tinyint NOT NULL DEFAULT 0,
`backflush_from_wip_warehouse` tinyint NOT NULL DEFAULT 0,
`source_warehouse` varchar(140),
`wip_warehouse` varchar(140),
`fg_warehouse` varchar(140),
`description` longtext,
`planned_start_time` datetime(6),
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`planned_end_time` datetime(6),
`batch_size` decimal(21,9) NOT NULL DEFAULT 0.0,
`planned_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_start_time` datetime(6),
`actual_operation_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_end_time` datetime(6),
`actual_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,630 WARNING database DDL Query made to DB:
create table `tabBOM Website Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`website_image` text,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,693 WARNING database DDL Query made to DB:
create table `tabJob Card Scheduled Time` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_time` datetime(6),
`to_time` datetime(6),
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,778 WARNING database DDL Query made to DB:
create table `tabProduction Plan Sub Assembly Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`production_item` varchar(140),
`item_name` varchar(140),
`fg_warehouse` varchar(140),
`parent_item_code` varchar(140),
`schedule_date` datetime(6),
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`bom_no` varchar(140),
`bom_level` int NOT NULL DEFAULT 0,
`type_of_manufacturing` varchar(140) DEFAULT 'In House',
`supplier` varchar(140),
`wo_produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`purchase_order` varchar(140),
`production_plan_item` varchar(140),
`received_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`indent` int NOT NULL DEFAULT 0,
`uom` varchar(140),
`stock_uom` varchar(140),
`description` text,
`actual_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:02,889 WARNING database DDL Query made to DB:
create table `tabProduction Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`get_items_from` varchar(140),
`posting_date` date,
`item_code` varchar(140),
`customer` varchar(140),
`warehouse` varchar(140),
`project` varchar(140),
`sales_order_status` varchar(140),
`from_date` date,
`to_date` date,
`from_delivery_date` date,
`to_delivery_date` date,
`combine_items` tinyint NOT NULL DEFAULT 0,
`combine_sub_items` tinyint NOT NULL DEFAULT 0,
`sub_assembly_warehouse` varchar(140),
`skip_available_sub_assembly_item` tinyint NOT NULL DEFAULT 1,
`include_non_stock_items` tinyint NOT NULL DEFAULT 1,
`include_subcontracted_items` tinyint NOT NULL DEFAULT 1,
`consider_minimum_order_qty` tinyint NOT NULL DEFAULT 0,
`include_safety_stock` tinyint NOT NULL DEFAULT 0,
`ignore_existing_ordered_qty` tinyint NOT NULL DEFAULT 1,
`for_warehouse` varchar(140),
`total_planned_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,028 WARNING database DDL Query made to DB:
create table `tabWork Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`production_item` varchar(140),
`item_name` varchar(140),
`image` text,
`bom_no` varchar(140),
`sales_order` varchar(140),
`company` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 1.0,
`material_transferred_for_manufacturing` decimal(21,9) NOT NULL DEFAULT 0.0,
`produced_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`process_loss_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`project` varchar(140),
`track_semi_finished_goods` tinyint NOT NULL DEFAULT 0,
`reserve_stock` tinyint NOT NULL DEFAULT 0,
`source_warehouse` varchar(140),
`wip_warehouse` varchar(140),
`fg_warehouse` varchar(140),
`scrap_warehouse` varchar(140),
`transfer_material_against` varchar(140),
`allow_alternative_item` tinyint NOT NULL DEFAULT 0,
`use_multi_level_bom` tinyint NOT NULL DEFAULT 1,
`skip_transfer` tinyint NOT NULL DEFAULT 0,
`from_wip_warehouse` tinyint NOT NULL DEFAULT 0,
`update_consumed_material_cost_in_project` tinyint NOT NULL DEFAULT 1,
`has_serial_no` tinyint NOT NULL DEFAULT 0,
`has_batch_no` tinyint NOT NULL DEFAULT 0,
`batch_size` decimal(21,9) NOT NULL DEFAULT 0.0,
`planned_start_date` datetime(6),
`planned_end_date` datetime(6),
`expected_delivery_date` date,
`actual_start_date` datetime(6),
`actual_end_date` datetime(6),
`lead_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`planned_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`actual_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`corrective_operation_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_operating_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`stock_uom` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`sales_order_item` varchar(140),
`production_plan` varchar(140),
`production_plan_item` varchar(140),
`production_plan_sub_assembly_item` varchar(140),
`product_bundle_item` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `production_plan`(`production_plan`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,092 WARNING database DDL Query made to DB:
create table `tabJob Card Time Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`from_time` datetime(6),
`to_time` datetime(6),
`time_in_mins` decimal(21,9) NOT NULL DEFAULT 0.0,
`completed_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`operation` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,171 WARNING database DDL Query made to DB:
create table `tabWorkstation Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workstation_type` varchar(140) UNIQUE,
`hour_rate_electricity` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_consumable` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_rent` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate_labour` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,237 WARNING database DDL Query made to DB:
create table `tabBOM Update Batch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`level` int NOT NULL DEFAULT 0,
`batch_no` int NOT NULL DEFAULT 0,
`boms_updated` longtext,
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,311 WARNING database DDL Query made to DB:
create table `tabOperation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workstation` varchar(140),
`is_corrective_operation` tinyint NOT NULL DEFAULT 0,
`create_job_card_based_on_batch_size` tinyint NOT NULL DEFAULT 0,
`quality_inspection_template` varchar(140),
`batch_size` int NOT NULL DEFAULT 1,
`total_operation_time` decimal(21,9) NOT NULL DEFAULT 0.0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,420 WARNING database DDL Query made to DB:
create table `tabBOM Creator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`project` varchar(140),
`uom` varchar(140),
`routing` varchar(140),
`rm_cost_as_per` varchar(140) DEFAULT 'Valuation Rate',
`set_rate_based_on_warehouse` tinyint NOT NULL DEFAULT 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140),
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 1.0,
`default_warehouse` varchar(140),
`company` varchar(140),
`raw_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`remarks` longtext,
`status` varchar(140) DEFAULT 'Draft',
`error_log` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,522 WARNING database DDL Query made to DB:
create table `tabBOM Creator Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`fg_item` varchar(140),
`is_expandable` tinyint NOT NULL DEFAULT 0,
`sourced_by_supplier` tinyint NOT NULL DEFAULT 0,
`bom_created` tinyint NOT NULL DEFAULT 0,
`is_subcontracted` tinyint NOT NULL DEFAULT 0,
`operation` varchar(140),
`description` text,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`uom` varchar(140),
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 0.0,
`stock_uom` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`do_not_explode` tinyint NOT NULL DEFAULT 1,
`parent_row_no` varchar(140),
`fg_reference_id` varchar(140),
`instruction` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,584 WARNING database DDL Query made to DB:
create table `tabProduction Plan Item Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_reference` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`qty` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:03,645 WARNING database DDL Query made to DB:
create table `tabProduction Plan Sales Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_order` varchar(140),
`sales_order_date` date,
`customer` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:04,073 WARNING database DDL Query made to DB:
create table `tabItem Price` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`uom` varchar(140),
`packing_unit` int NOT NULL DEFAULT 0,
`item_name` varchar(140),
`brand` varchar(140),
`item_description` text,
`price_list` varchar(140),
`customer` varchar(140),
`supplier` varchar(140),
`batch_no` varchar(140),
`buying` tinyint NOT NULL DEFAULT 0,
`selling` tinyint NOT NULL DEFAULT 0,
`currency` varchar(140),
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`valid_from` date,
`lead_time_days` int NOT NULL DEFAULT 0,
`valid_upto` date,
`note` text,
`reference` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `price_list`(`price_list`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:35:04,142 WARNING database DDL Query made to DB:
create table `tabShipment Delivery Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`delivery_note` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
