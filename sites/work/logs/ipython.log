2025-07-01 15:07:09,079 INFO ipython === bench console session ===
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SELECT 1 as test")
2025-07-01 15:07:09,079 INFO ipython frappe.get_all("User", limit=3)
2025-07-01 15:07:09,079 INFO ipython frappe.get_installed_apps()
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Product%'")
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Shopping%'")
2025-07-01 15:07:09,080 INFO ipython === session end ===
2025-07-01 15:26:49,301 INFO ipython === bench console session ===
2025-07-01 15:26:49,302 INFO ipython frappe.get_installed_apps()
2025-07-01 15:26:49,302 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:26:49,302 INFO ipython frappe.db.sql("SELECT 1 as test")
2025-07-01 15:26:49,302 INFO ipython === session end ===
