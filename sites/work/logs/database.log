2025-07-01 14:59:16,842 WARNING database DDL Query made to DB:
create table `tabProduct Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`product` varchar(140),
`customer` varchar(140),
`order_reference` varchar(140),
`overall_rating` decimal(3,2),
`product_quality_rating` decimal(3,2),
`delivery_rating` decimal(3,2),
`service_rating` decimal(3,2),
`review_title` varchar(140),
`review_text` text,
`pros` text,
`cons` text,
`is_verified_purchase` tinyint(4) NOT NULL DEFAULT 0,
`is_approved` tinyint(4) NOT NULL DEFAULT 0,
`is_featured` tinyint(4) NOT NULL DEFAULT 0,
`helpful_count` int(11) NOT NULL DEFAULT 0,
`review_date` datetime(6),
`approved_by` varchar(140),
`approved_date` datetime(6),
`seller_response` text,
`response_date` datetime(6),
`responded_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,009 WARNING database DDL Query made to DB:
create table `tabEcommerce Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`order_date` datetime(6),
`status` varchar(140) DEFAULT 'Pending',
`payment_status` varchar(140) DEFAULT 'Pending',
`payment_method` varchar(140),
`delivery_status` varchar(140) DEFAULT 'Pending',
`subtotal` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140) DEFAULT 'TZS',
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 1.0,
`paid_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`outstanding_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_address` text,
`shipping_address` text,
`contact_phone` varchar(140),
`contact_email` varchar(140),
`special_instructions` text,
`coupon_code` varchar(140),
`discount_reason` varchar(140),
`loyalty_points_used` int(11) NOT NULL DEFAULT 0,
`loyalty_points_earned` int(11) NOT NULL DEFAULT 0,
`estimated_delivery_date` date,
`actual_delivery_date` date,
`tracking_number` varchar(140),
`notes` text,
`internal_notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,150 WARNING database DDL Query made to DB:
create table `tabBusiness Dashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dashboard_name` varchar(140) UNIQUE,
`user` varchar(140),
`user_type` varchar(140),
`date_range` varchar(140) DEFAULT 'This Month',
`from_date` date,
`to_date` date,
`total_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_orders` int(11) NOT NULL DEFAULT 0,
`total_customers` int(11) NOT NULL DEFAULT 0,
`average_order_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`conversion_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`customer_acquisition_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`customer_lifetime_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`return_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`top_products` json,
`top_categories` json,
`sales_by_region` json,
`revenue_trend` json,
`order_trend` json,
`customer_trend` json,
`last_updated` datetime(6),
`auto_refresh` tinyint(4) NOT NULL DEFAULT 1,
`refresh_interval` int(11) NOT NULL DEFAULT 15,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,286 WARNING database DDL Query made to DB:
create table `tabProduct Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`category_name` varchar(140) UNIQUE,
`parent_category` varchar(140),
`category_code` varchar(140) UNIQUE,
`description` longtext,
`category_image` text,
`is_active` tinyint(4) NOT NULL DEFAULT 1,
`show_in_website` tinyint(4) NOT NULL DEFAULT 1,
`featured_category` tinyint(4) NOT NULL DEFAULT 0,
`meta_title` varchar(140),
`meta_description` text,
`meta_keywords` varchar(140),
`sort_order` int(11) NOT NULL DEFAULT 0,
`commission_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,367 WARNING database DDL Query made to DB:
create table `tabProduct Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`attribute_name` varchar(140),
`attribute_value` varchar(140),
`attribute_type` varchar(140) DEFAULT 'Text',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,577 WARNING database DDL Query made to DB:
create table `tabProduct` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`product_name` varchar(140),
`product_code` varchar(140) UNIQUE,
`category` varchar(140),
`brand` varchar(140),
`short_description` text,
`description` longtext,
`featured_image` text,
`price` decimal(21,9) NOT NULL DEFAULT 0.0,
`compare_price` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_price` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140) DEFAULT 'TZS',
`tax_category` varchar(140),
`tax_inclusive` tinyint(4) NOT NULL DEFAULT 0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`sku` varchar(140) UNIQUE,
`barcode` varchar(140),
`weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`dimensions` varchar(140),
`track_inventory` tinyint(4) NOT NULL DEFAULT 1,
`stock_quantity` int(11) NOT NULL DEFAULT 0,
`min_stock_level` int(11) NOT NULL DEFAULT 0,
`max_stock_level` int(11) NOT NULL DEFAULT 0,
`supplier` varchar(140),
`manufacturer` varchar(140),
`country_of_origin` varchar(140),
`warranty_period` varchar(140),
`return_policy` text,
`shipping_class` varchar(140),
`is_active` tinyint(4) NOT NULL DEFAULT 1,
`is_featured` tinyint(4) NOT NULL DEFAULT 0,
`show_in_website` tinyint(4) NOT NULL DEFAULT 1,
`requires_shipping` tinyint(4) NOT NULL DEFAULT 1,
`digital_product` tinyint(4) NOT NULL DEFAULT 0,
`downloadable` tinyint(4) NOT NULL DEFAULT 0,
`virtual_product` tinyint(4) NOT NULL DEFAULT 0,
`meta_title` varchar(140),
`meta_description` text,
`meta_keywords` varchar(140),
`tags` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,671 WARNING database DDL Query made to DB:
create table `tabProduct Image` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`image` text,
`image_title` varchar(140),
`alt_text` varchar(140),
`sort_order` int(11) NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:17,776 WARNING database DDL Query made to DB:
create table `tabOrder Tracking` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`order` varchar(140),
`status` varchar(140),
`status_date` datetime(6),
`location` varchar(140),
`estimated_time` datetime(6),
`actual_time` datetime(6),
`notes` text,
`updated_by` varchar(140),
`gps_coordinates` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:18,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-07-01 14:59:18,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-07-01 15:22:47,154 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabRecommended Items`
2025-07-01 15:22:47,201 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHomepage Featured Product`
2025-07-01 15:22:47,236 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWebsite Item Tabbed Section`
2025-07-01 15:22:47,271 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWishlist`
2025-07-01 15:22:47,324 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWebsite Offer`
2025-07-01 15:22:47,362 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWishlist Item`
2025-07-01 15:23:02,166 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Page Patch`
2025-07-01 15:23:02,206 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Page Revision Item`
2025-07-01 15:23:02,245 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki App Switcher List Table`
2025-07-01 15:23:02,280 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Sidebar`
2025-07-01 15:23:02,317 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Feedback`
2025-07-01 15:23:02,355 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Group Item`
2025-07-01 15:23:02,391 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabWiki Page Revision`
