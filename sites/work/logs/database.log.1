2025-04-22 15:40:06,848 WARNING database DDL Query made to DB:
create table `tabSalary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
`posting_date` date,
`letter_head` varchar(140),
`status` varchar(140),
`salary_withholding` varchar(140),
`salary_withholding_cycle` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 1.0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`salary_structure` varchar(140),
`payroll_entry` varchar(140),
`mode_of_payment` varchar(140),
`salary_slip_based_on_timesheet` tinyint NOT NULL DEFAULT 0,
`deduct_tax_for_unclaimed_employee_benefits` tinyint NOT NULL DEFAULT 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` tinyint NOT NULL DEFAULT 0,
`total_working_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`unmarked_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`leave_without_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`absent_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`payment_days` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_working_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`gross_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_gross_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`gross_year_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_gross_year_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_deduction` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_total_deduction` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rounded_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`year_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_year_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`month_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_month_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_in_words` varchar(240),
`base_total_in_words` varchar(240),
`ctc` decimal(21,9) NOT NULL DEFAULT 0.0,
`income_from_other_sources` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_earnings` decimal(21,9) NOT NULL DEFAULT 0.0,
`non_taxable_earnings` decimal(21,9) NOT NULL DEFAULT 0.0,
`standard_tax_exemption_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_exemption_declaration` decimal(21,9) NOT NULL DEFAULT 0.0,
`deductions_before_tax_calculation` decimal(21,9) NOT NULL DEFAULT 0.0,
`annual_taxable_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`income_tax_deducted_till_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`current_month_income_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`future_income_tax_deductions` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_income_tax` decimal(21,9) NOT NULL DEFAULT 0.0,
`journal_entry` varchar(140),
`amended_from` varchar(140),
`bank_name` varchar(140),
`bank_account_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `salary_structure`(`salary_structure`),
index `payroll_entry`(`payroll_entry`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:06,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-04-22 15:40:07,050 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`claim_date` date,
`currency` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`earning_component` varchar(140),
`max_amount_eligible` decimal(21,9) NOT NULL DEFAULT 0.0,
`pay_against_benefit_claim` tinyint NOT NULL DEFAULT 0,
`claimed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`salary_slip` varchar(140),
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,175 WARNING database DDL Query made to DB:
create table `tabEmployee Incentive` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`department` varchar(140),
`salary_component` varchar(140),
`currency` varchar(140),
`payroll_date` date,
`incentive_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,263 WARNING database DDL Query made to DB:
create table `tabEmployee Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`cost_center` varchar(140),
`percentage` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,395 WARNING database DDL Query made to DB:
create table `tabSalary Component Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,466 WARNING database DDL Query made to DB:
create table `tabPayroll Employee Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`is_salary_withheld` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,583 WARNING database DDL Query made to DB:
create table `tabAdditional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`is_recurring` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`from_date` date,
`to_date` date,
`payroll_date` date,
`amended_from` varchar(140),
`salary_component` varchar(140),
`type` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`deduct_full_tax_on_selected_payroll_date` tinyint NOT NULL DEFAULT 0,
`overwrite_salary_structure_amount` tinyint NOT NULL DEFAULT 1,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `payroll_date`(`payroll_date`),
index `salary_component`(`salary_component`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,743 WARNING database DDL Query made to DB:
create table `tabPayroll Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`company` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`payroll_payable_account` varchar(140),
`status` varchar(140),
`salary_slip_based_on_timesheet` tinyint NOT NULL DEFAULT 0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`deduct_tax_for_unclaimed_employee_benefits` tinyint NOT NULL DEFAULT 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` tinyint NOT NULL DEFAULT 0,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`number_of_employees` int NOT NULL DEFAULT 0,
`validate_attendance` tinyint NOT NULL DEFAULT 0,
`cost_center` varchar(140),
`project` varchar(140),
`payment_account` varchar(140),
`bank_account` varchar(140),
`salary_slips_created` tinyint NOT NULL DEFAULT 0,
`salary_slips_submitted` tinyint NOT NULL DEFAULT 0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,847 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`total_declared_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_exemption_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:07,939 WARNING database DDL Query made to DB:
create table `tabTaxable Salary Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`to_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`percent_deduction` decimal(21,9) NOT NULL DEFAULT 0.0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,021 WARNING database DDL Query made to DB:
create table `tabSalary Slip Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`time_sheet` varchar(140),
`working_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,116 WARNING database DDL Query made to DB:
create table `tabSalary Slip Loan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`principal_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`interest_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_payment` decimal(21,9) NOT NULL DEFAULT 0.0,
`loan_repayment_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,206 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab Other Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`description` varchar(140),
`percent` decimal(21,9) NOT NULL DEFAULT 0.0,
`min_taxable_income` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_taxable_income` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,319 WARNING database DDL Query made to DB:
create table `tabSalary Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`salary_component` varchar(140),
`abbr` varchar(140),
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`year_to_date` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_salary` varchar(140),
`is_recurring_additional_salary` tinyint NOT NULL DEFAULT 0,
`statistical_component` tinyint NOT NULL DEFAULT 0,
`depends_on_payment_days` tinyint NOT NULL DEFAULT 0,
`exempted_from_income_tax` tinyint NOT NULL DEFAULT 0,
`is_tax_applicable` tinyint NOT NULL DEFAULT 0,
`is_flexible_benefit` tinyint NOT NULL DEFAULT 0,
`variable_based_on_taxable_salary` tinyint NOT NULL DEFAULT 0,
`do_not_include_in_total` tinyint NOT NULL DEFAULT 0,
`deduct_full_tax_on_selected_payroll_date` tinyint NOT NULL DEFAULT 0,
`condition` longtext,
`amount_based_on_formula` tinyint NOT NULL DEFAULT 0,
`formula` longtext,
`default_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`additional_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_on_flexible_benefit` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_on_additional_salary` decimal(21,9) NOT NULL DEFAULT 0.0,
index `salary_component`(`salary_component`),
index `exempted_from_income_tax`(`exempted_from_income_tax`),
index `is_tax_applicable`(`is_tax_applicable`),
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,400 WARNING database DDL Query made to DB:
create table `tabGratuity Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disable` tinyint NOT NULL DEFAULT 0,
`calculate_gratuity_amount_based_on` varchar(140),
`total_working_days_per_year` int NOT NULL DEFAULT 365,
`work_experience_calculation_function` varchar(140) DEFAULT 'Round off Work Experience',
`minimum_year_for_gratuity` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,472 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,582 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`expired_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`used_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`pending_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
`available_leaves` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,826 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) NOT NULL DEFAULT 0.0,
`remaining_benefit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pro_rata_dispensed_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:08,952 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`salary_component` varchar(140) UNIQUE,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` tinyint NOT NULL DEFAULT 1,
`is_tax_applicable` tinyint NOT NULL DEFAULT 1,
`deduct_full_tax_on_selected_payroll_date` tinyint NOT NULL DEFAULT 0,
`variable_based_on_taxable_salary` tinyint NOT NULL DEFAULT 0,
`is_income_tax_component` tinyint NOT NULL DEFAULT 0,
`exempted_from_income_tax` tinyint NOT NULL DEFAULT 0,
`round_to_the_nearest_integer` tinyint NOT NULL DEFAULT 0,
`statistical_component` tinyint NOT NULL DEFAULT 0,
`do_not_include_in_total` tinyint NOT NULL DEFAULT 0,
`remove_if_zero_valued` tinyint NOT NULL DEFAULT 1,
`disabled` tinyint NOT NULL DEFAULT 0,
`condition` longtext,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount_based_on_formula` tinyint NOT NULL DEFAULT 0,
`formula` longtext,
`is_flexible_benefit` tinyint NOT NULL DEFAULT 0,
`max_benefit_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pay_against_benefit_claim` tinyint NOT NULL DEFAULT 0,
`only_tax_impact` tinyint NOT NULL DEFAULT 0,
`create_separate_payment_entry_against_benefit_claim` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:09,098 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int NOT NULL DEFAULT 0,
`status` varchar(140) DEFAULT 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:09,196 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disabled` tinyint NOT NULL DEFAULT 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`allow_tax_exemption` tinyint NOT NULL DEFAULT 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:09,287 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_active` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:09,367 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_year` int NOT NULL DEFAULT 0,
`to_year` int NOT NULL DEFAULT 0,
`fraction_of_applicable_earnings` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:09,496 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) DEFAULT 'Yes',
`is_default` varchar(140) DEFAULT 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) NOT NULL DEFAULT 0.0,
`max_benefits` decimal(21,9) NOT NULL DEFAULT 0.0,
`salary_slip_based_on_timesheet` tinyint NOT NULL DEFAULT 0,
`payroll_frequency` varchar(140) DEFAULT 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_earning` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_deduction` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_pay` decimal(21,9) NOT NULL DEFAULT 0.0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:40:10,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-04-22 15:40:10,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `base_total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `exchange_rate` decimal(21,9) NOT NULL DEFAULT 1.0, MODIFY `per_billed` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billable_hours` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `base_total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billed_hours` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:40:10,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` tinyint NOT NULL DEFAULT 1
2025-04-22 15:40:10,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` MODIFY `selling` tinyint NOT NULL DEFAULT 1, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `buying` tinyint NOT NULL DEFAULT 1
2025-04-22 15:40:10,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-04-22 15:40:10,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-04-22 15:40:10,999 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `enable_provisional_accounting_for_non_stock_items` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `book_advance_payments_in_separate_party_account` tinyint NOT NULL DEFAULT 0, MODIFY `enable_perpetual_inventory` tinyint NOT NULL DEFAULT 1, MODIFY `allow_account_creation_against_child_company` tinyint NOT NULL DEFAULT 0, MODIFY `auto_exchange_rate_revaluation` tinyint NOT NULL DEFAULT 0, MODIFY `monthly_sales_target` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `reconcile_on_advance_payment_date` tinyint NOT NULL DEFAULT 0, MODIFY `credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `submit_err_jv` tinyint NOT NULL DEFAULT 0, MODIFY `total_monthly_sales` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:40:11,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-04-22 15:40:11,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `create_user_permission` tinyint NOT NULL DEFAULT 1, MODIFY `notice_number_of_days` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0
2025-04-22 15:40:11,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-04-22 15:40:11,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0
2025-04-22 15:40:11,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:40:11,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `expected_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_template` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `start` int NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `task_weight` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_time` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `duration` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `is_milestone` tinyint NOT NULL DEFAULT 0, MODIFY `total_billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `progress` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:40:11,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:40:11,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_billed_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `estimated_costing` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_consumed_material_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `percent_complete` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_purchase_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_billable_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_costing_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `collect_progress` tinyint NOT NULL DEFAULT 0, MODIFY `total_sales_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `per_gross_margin` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `actual_time` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:41:57,476 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-22 15:42:00,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0
2025-04-22 15:42:01,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0
2025-04-22 15:44:29,594 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-22 15:44:33,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0
2025-04-22 15:44:33,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0
2025-04-22 15:50:45,824 WARNING database DDL Query made to DB:
create table `tabCRM Territory` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`territory_name` varchar(140) UNIQUE,
`territory_manager` varchar(140),
`old_parent` varchar(140),
`parent_crm_territory` varchar(140),
`lft` int NOT NULL DEFAULT 0,
`rgt` int NOT NULL DEFAULT 0,
`is_group` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:45,942 WARNING database DDL Query made to DB:
create table `tabCRM Form Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dt` varchar(140),
`view` varchar(140) DEFAULT 'Form',
`enabled` tinyint NOT NULL DEFAULT 0,
`is_standard` tinyint NOT NULL DEFAULT 0,
`script` longtext DEFAULT 'function setupForm({ doc }) {\n    return {\n        actions: [],\n    }\n}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,119 WARNING database DDL Query made to DB:
create table `tabCRM Global Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dt` varchar(140) DEFAULT 'DocType',
`type` varchar(140),
`json` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,204 WARNING database DDL Query made to DB:
create table `tabCRM Deal Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`deal_status` varchar(140) UNIQUE,
`color` varchar(140) DEFAULT 'gray',
`position` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,282 WARNING database DDL Query made to DB:
create table `tabCRM Service Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,418 WARNING database DDL Query made to DB:
create sequence if not exists crm_view_settings_id_seq nocache nocycle
2025-04-22 15:50:46,441 WARNING database DDL Query made to DB:
create table `tabCRM View Settings` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`icon` varchar(140),
`user` varchar(140),
`is_standard` tinyint NOT NULL DEFAULT 0,
`is_default` tinyint NOT NULL DEFAULT 0,
`type` varchar(140) DEFAULT 'list',
`dt` varchar(140),
`route_name` varchar(140),
`pinned` tinyint NOT NULL DEFAULT 0,
`public` tinyint NOT NULL DEFAULT 0,
`filters` longtext,
`order_by` longtext,
`load_default_columns` tinyint NOT NULL DEFAULT 0,
`columns` longtext,
`rows` longtext,
`group_by_field` varchar(140),
`column_field` varchar(140),
`title_field` varchar(140),
`kanban_columns` longtext,
`kanban_fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,529 WARNING database DDL Query made to DB:
create table `tabCRM Lead Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`lead_status` varchar(140) UNIQUE,
`color` varchar(140) DEFAULT 'gray',
`position` int NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,712 WARNING database DDL Query made to DB:
create table `tabCRM Deal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'CRM-DEAL-.YYYY.-',
`organization` varchar(140),
`next_step` varchar(140),
`probability` decimal(21,9) NOT NULL DEFAULT 0.0,
`status` varchar(140) DEFAULT 'Qualification',
`close_date` date,
`deal_owner` varchar(140),
`contact` varchar(140),
`lead` varchar(140),
`source` varchar(140),
`lead_name` varchar(140),
`organization_name` varchar(140),
`website` varchar(140),
`no_of_employees` varchar(140),
`job_title` varchar(140),
`territory` varchar(140),
`currency` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`industry` varchar(140),
`salutation` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`email` varchar(140),
`mobile_no` varchar(140),
`phone` varchar(140),
`gender` varchar(140),
`sla` varchar(140),
`sla_creation` datetime(6),
`sla_status` varchar(140),
`communication_status` varchar(140) DEFAULT 'Open',
`response_by` datetime(6),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,891 WARNING database DDL Query made to DB:
create table `tabCRM Status Change Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from` varchar(140),
`to` varchar(140),
`from_date` datetime(6),
`to_date` datetime(6),
`duration` decimal(21,9),
`last_status_change_log` varchar(140),
`log_owner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:46,977 WARNING database DDL Query made to DB:
create table `tabCRM Telephony Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
`user_name` varchar(140),
`mobile_no` varchar(140),
`default_medium` varchar(140),
`twilio` tinyint NOT NULL DEFAULT 0,
`twilio_number` varchar(140),
`call_receiving_device` varchar(140) DEFAULT 'Computer',
`exotel` tinyint NOT NULL DEFAULT 0,
`exotel_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,072 WARNING database DDL Query made to DB:
create table `tabCRM Service Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`apply_on` varchar(140),
`sla_name` varchar(140) UNIQUE,
`enabled` tinyint NOT NULL DEFAULT 0,
`default` tinyint NOT NULL DEFAULT 0,
`start_date` date,
`end_date` date,
`condition` longtext,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,167 WARNING database DDL Query made to DB:
create table `tabCRM Telephony Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`number` varchar(140),
`is_primary` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,241 WARNING database DDL Query made to DB:
create table `tabCRM Lead Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`source_name` varchar(140) UNIQUE,
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,358 WARNING database DDL Query made to DB:
create table `tabCRM Holiday` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`date` date,
`weekly_off` tinyint NOT NULL DEFAULT 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,492 WARNING database DDL Query made to DB:
create table `tabCRM Communication Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,607 WARNING database DDL Query made to DB:
create table `tabCRM Dropdown Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`name1` varchar(140) UNIQUE,
`label` varchar(140),
`type` varchar(140),
`route` varchar(140),
`open_in_new_window` tinyint NOT NULL DEFAULT 1,
`hidden` tinyint NOT NULL DEFAULT 0,
`is_standard` tinyint NOT NULL DEFAULT 0,
`icon` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,701 WARNING database DDL Query made to DB:
create table `tabCRM Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`notification_text` text,
`from_user` varchar(140),
`type` varchar(140),
`to_user` varchar(140),
`read` tinyint NOT NULL DEFAULT 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`notification_type_doctype` varchar(140),
`notification_type_doc` varchar(140),
`comment` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,802 WARNING database DDL Query made to DB:
create table `tabCRM Holiday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`holiday_list_name` varchar(140) UNIQUE,
`from_date` date,
`to_date` date,
`total_holidays` int NOT NULL DEFAULT 0,
`weekly_off` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:47,937 WARNING database DDL Query made to DB:
create table `tabCRM Industry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`industry` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,034 WARNING database DDL Query made to DB:
create table `tabCRM Contacts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`contact` varchar(140),
`full_name` varchar(140),
`email` varchar(140),
`gender` varchar(140),
`mobile_no` varchar(140),
`phone` varchar(140),
`is_primary` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,221 WARNING database DDL Query made to DB:
create table `tabFCRM Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`content` longtext,
`reference_doctype` varchar(140) DEFAULT 'CRM Lead',
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,367 WARNING database DDL Query made to DB:
create table `tabCRM Call Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`telephony_medium` varchar(140),
`id` varchar(140) UNIQUE,
`from` varchar(140),
`status` varchar(140),
`duration` decimal(21,9),
`medium` varchar(140),
`start_time` datetime(6),
`reference_doctype` varchar(140) DEFAULT 'CRM Lead',
`reference_docname` varchar(140),
`to` varchar(140),
`type` varchar(140),
`receiver` varchar(140),
`caller` varchar(140),
`recording_url` varchar(140),
`end_time` datetime(6),
`note` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,494 WARNING database DDL Query made to DB:
create table `tabCRM Fields Layout` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`dt` varchar(140),
`type` varchar(140),
`layout` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,574 WARNING database DDL Query made to DB:
create sequence if not exists crm_task_id_seq nocache nocycle
2025-04-22 15:50:48,600 WARNING database DDL Query made to DB:
create table `tabCRM Task` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`priority` varchar(140),
`start_date` date,
`reference_doctype` varchar(140) DEFAULT 'CRM Lead',
`reference_docname` varchar(140),
`assigned_to` varchar(140),
`status` varchar(140),
`due_date` datetime(6),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,823 WARNING database DDL Query made to DB:
create table `tabCRM Invitation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email` varchar(140),
`role` varchar(140),
`key` varchar(140),
`invited_by` varchar(140),
`status` varchar(140),
`email_sent_at` datetime(6),
`accepted_at` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:48,996 WARNING database DDL Query made to DB:
create table `tabCRM Lead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`organization` varchar(140),
`website` varchar(140),
`territory` varchar(140),
`industry` varchar(140),
`job_title` varchar(140),
`source` varchar(140),
`lead_owner` varchar(140),
`salutation` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`email` varchar(140),
`mobile_no` varchar(140),
`naming_series` varchar(140) DEFAULT 'CRM-LEAD-.YYYY.-',
`lead_name` varchar(140),
`middle_name` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`status` varchar(140) DEFAULT 'New',
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`image` text,
`converted` tinyint NOT NULL DEFAULT 0,
`sla` varchar(140),
`sla_creation` datetime(6),
`sla_status` varchar(140),
`communication_status` varchar(140) DEFAULT 'Open',
`response_by` datetime(6),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email`(`email`),
index `lead_name`(`lead_name`),
index `status`(`status`),
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:49,135 WARNING database DDL Query made to DB:
create table `tabCRM Organization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`organization_name` varchar(140) UNIQUE,
`no_of_employees` varchar(140),
`currency` varchar(140),
`annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0,
`organization_logo` text,
`website` varchar(140),
`territory` varchar(140),
`industry` varchar(140),
`address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:49,217 WARNING database DDL Query made to DB:
create table `tabCRM Service Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`default_priority` tinyint NOT NULL DEFAULT 0,
`priority` varchar(140),
`first_response_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:50:50,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Template` ADD COLUMN `enabled` tinyint NOT NULL DEFAULT 0, ADD COLUMN `reference_doctype` varchar(140)
2025-04-22 15:50:50,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Template` MODIFY `use_html` tinyint NOT NULL DEFAULT 0
2025-04-22 15:51:24,787 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-22 15:51:27,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0
2025-04-22 15:51:27,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:06,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Territory` MODIFY `lft` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `rgt` int NOT NULL DEFAULT 0
2025-04-22 15:53:06,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Form Script` MODIFY `is_standard` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 0, MODIFY `script` longtext DEFAULT 'function setupForm({ doc }) {\n    return {\n        actions: [],\n    }\n}'
2025-04-22 15:53:06,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Global Settings` MODIFY `json` json
2025-04-22 15:53:06,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Deal Status` MODIFY `position` int NOT NULL DEFAULT 0
2025-04-22 15:53:06,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Service Day` ADD INDEX `modified`(`modified`)
2025-04-22 15:53:06,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM View Settings` MODIFY `pinned` tinyint NOT NULL DEFAULT 0, MODIFY `is_standard` tinyint NOT NULL DEFAULT 0, MODIFY `load_default_columns` tinyint NOT NULL DEFAULT 0, MODIFY `is_default` tinyint NOT NULL DEFAULT 0, MODIFY `public` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:07,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Lead Status` MODIFY `position` int NOT NULL DEFAULT 1
2025-04-22 15:53:07,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Deal` MODIFY `probability` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `first_response_time` decimal(21,9)
2025-04-22 15:53:07,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Status Change Log` MODIFY `duration` decimal(21,9)
2025-04-22 15:53:07,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Status Change Log` ADD INDEX `modified`(`modified`)
2025-04-22 15:53:07,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Telephony Agent` MODIFY `exotel` tinyint NOT NULL DEFAULT 0, MODIFY `twilio` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:07,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Service Level Agreement` MODIFY `default` tinyint NOT NULL DEFAULT 0, MODIFY `enabled` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:07,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Telephony Phone` MODIFY `is_primary` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:07,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Holiday` MODIFY `weekly_off` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:08,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Holiday` ADD INDEX `modified`(`modified`)
2025-04-22 15:53:08,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Dropdown Item` MODIFY `is_standard` tinyint NOT NULL DEFAULT 0, MODIFY `hidden` tinyint NOT NULL DEFAULT 0, MODIFY `open_in_new_window` tinyint NOT NULL DEFAULT 1
2025-04-22 15:53:08,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Notification` MODIFY `read` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:08,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Holiday List` MODIFY `total_holidays` int NOT NULL DEFAULT 0
2025-04-22 15:53:08,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Contacts` MODIFY `is_primary` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:08,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Contacts` ADD INDEX `modified`(`modified`)
2025-04-22 15:53:09,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Call Log` MODIFY `duration` decimal(21,9)
2025-04-22 15:53:09,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Lead` MODIFY `first_response_time` decimal(21,9), MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `converted` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:09,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Organization` MODIFY `annual_revenue` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-22 15:53:09,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Service Level Priority` MODIFY `first_response_time` decimal(21,9), MODIFY `default_priority` tinyint NOT NULL DEFAULT 0
2025-04-22 15:53:09,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Service Level Priority` ADD INDEX `modified`(`modified`)
2025-04-25 08:50:13,428 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-25 09:31:12,177 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-25 09:31:12,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `show_preview_popup` tinyint NOT NULL DEFAULT 0, MODIFY `show_name_in_global_search` tinyint NOT NULL DEFAULT 0, MODIFY `allow_rename` tinyint NOT NULL DEFAULT 1, MODIFY `istable` tinyint NOT NULL DEFAULT 0, MODIFY `email_append_to` tinyint NOT NULL DEFAULT 0, MODIFY `issingle` tinyint NOT NULL DEFAULT 0, MODIFY `is_tree` tinyint NOT NULL DEFAULT 0, MODIFY `is_submittable` tinyint NOT NULL DEFAULT 0, MODIFY `has_web_view` tinyint NOT NULL DEFAULT 0, MODIFY `allow_guest_to_view` tinyint NOT NULL DEFAULT 0, MODIFY `is_calendar_and_gantt` tinyint NOT NULL DEFAULT 0, MODIFY `index_web_pages_for_search` tinyint NOT NULL DEFAULT 1, MODIFY `editable_grid` tinyint NOT NULL DEFAULT 1, MODIFY `quick_entry` tinyint NOT NULL DEFAULT 0, MODIFY `grid_page_length` int NOT NULL DEFAULT 50, MODIFY `track_changes` tinyint NOT NULL DEFAULT 0, MODIFY `track_seen` tinyint NOT NULL DEFAULT 0, MODIFY `track_views` tinyint NOT NULL DEFAULT 0, MODIFY `custom` tinyint NOT NULL DEFAULT 0, MODIFY `beta` tinyint NOT NULL DEFAULT 0, MODIFY `is_virtual` tinyint NOT NULL DEFAULT 0, MODIFY `queue_in_background` tinyint NOT NULL DEFAULT 0, MODIFY `max_attachments` int NOT NULL DEFAULT 0, MODIFY `hide_toolbar` tinyint NOT NULL DEFAULT 0, MODIFY `allow_copy` tinyint NOT NULL DEFAULT 0, MODIFY `allow_import` tinyint NOT NULL DEFAULT 0, MODIFY `allow_events_in_timeline` tinyint NOT NULL DEFAULT 0, MODIFY `protect_attached_files` tinyint NOT NULL DEFAULT 0, MODIFY `in_create` tinyint NOT NULL DEFAULT 0, MODIFY `allow_auto_repeat` tinyint NOT NULL DEFAULT 0, MODIFY `make_attachments_public` tinyint NOT NULL DEFAULT 0, MODIFY `show_title_field_in_link` tinyint NOT NULL DEFAULT 0, MODIFY `translated_doctype` tinyint NOT NULL DEFAULT 0, MODIFY `read_only` tinyint NOT NULL DEFAULT 0, MODIFY `force_re_route_to_default_view` tinyint NOT NULL DEFAULT 0
2025-04-25 09:31:13,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` MODIFY `view_switcher` tinyint NOT NULL DEFAULT 1, MODIFY `enabled` tinyint NOT NULL DEFAULT 1, MODIFY `thread_notify` tinyint NOT NULL DEFAULT 1, MODIFY `send_me_a_copy` tinyint NOT NULL DEFAULT 0, MODIFY `mute_sounds` tinyint NOT NULL DEFAULT 0, MODIFY `logout_all_sessions` tinyint NOT NULL DEFAULT 1, MODIFY `document_follow_notify` tinyint NOT NULL DEFAULT 0, MODIFY `send_welcome_email` tinyint NOT NULL DEFAULT 1, MODIFY `notifications` tinyint NOT NULL DEFAULT 1, MODIFY `dashboard` tinyint NOT NULL DEFAULT 1, MODIFY `login_before` int NOT NULL DEFAULT 0, MODIFY `login_after` int NOT NULL DEFAULT 0, MODIFY `timeline` tinyint NOT NULL DEFAULT 1, MODIFY `follow_shared_documents` tinyint NOT NULL DEFAULT 0, MODIFY `bulk_actions` tinyint NOT NULL DEFAULT 1, MODIFY `follow_commented_documents` tinyint NOT NULL DEFAULT 0, MODIFY `allowed_in_mentions` tinyint NOT NULL DEFAULT 1, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `follow_assigned_documents` tinyint NOT NULL DEFAULT 0, MODIFY `list_sidebar` tinyint NOT NULL DEFAULT 1, MODIFY `follow_liked_documents` tinyint NOT NULL DEFAULT 0, MODIFY `simultaneous_sessions` int NOT NULL DEFAULT 2, MODIFY `follow_created_documents` tinyint NOT NULL DEFAULT 0, MODIFY `bypass_restrict_ip_check_if_2fa_enabled` tinyint NOT NULL DEFAULT 0, MODIFY `form_sidebar` tinyint NOT NULL DEFAULT 1, MODIFY `search_bar` tinyint NOT NULL DEFAULT 1
2025-04-25 09:31:13,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Settings` MODIFY `enable_email_event_reminders` tinyint NOT NULL DEFAULT 1, MODIFY `enable_email_mention` tinyint NOT NULL DEFAULT 1, MODIFY `enable_email_assignment` tinyint NOT NULL DEFAULT 1, MODIFY `enabled` tinyint NOT NULL DEFAULT 1, MODIFY `seen` tinyint NOT NULL DEFAULT 0, MODIFY `enable_email_threads_on_assigned_document` tinyint NOT NULL DEFAULT 1, MODIFY `enable_email_share` tinyint NOT NULL DEFAULT 1, MODIFY `enable_email_notifications` tinyint NOT NULL DEFAULT 1
2025-04-25 09:31:15,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `is_existing_asset` tinyint NOT NULL DEFAULT 0, MODIFY `booked_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `opening_number_of_booked_depreciations` int NOT NULL DEFAULT 0, MODIFY `value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `total_number_of_depreciations` int NOT NULL DEFAULT 0, MODIFY `maintenance_required` tinyint NOT NULL DEFAULT 0, MODIFY `additional_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_fully_depreciated` tinyint NOT NULL DEFAULT 0, MODIFY `total_asset_cost` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `asset_quantity` int NOT NULL DEFAULT 1, MODIFY `is_composite_asset` tinyint NOT NULL DEFAULT 0, MODIFY `calculate_depreciation` tinyint NOT NULL DEFAULT 0, MODIFY `frequency_of_depreciation` int NOT NULL DEFAULT 0, MODIFY `gross_purchase_amount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_accumulated_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0
2025-04-25 09:31:19,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0
2025-04-25 09:31:19,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0
2025-04-25 12:33:26,293 WARNING database DDL Query made to DB:
create table `tabWishlist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:26,561 WARNING database DDL Query made to DB:
create table `tabWishlist Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`website_item` varchar(140),
`web_item_name` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` longtext,
`route` text,
`image` text,
`warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:26,635 WARNING database DDL Query made to DB:
create table `tabHomepage Featured Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`image` text,
`thumbnail` text,
`route` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:26,804 WARNING database DDL Query made to DB:
create table `tabRecommended Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`website_item` varchar(140),
`website_item_name` varchar(140),
`item_code` varchar(140),
`route` text,
`website_item_image` text,
`website_item_thumbnail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:26,959 WARNING database DDL Query made to DB:
create table `tabWebsite Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140) DEFAULT 'WEB-ITM-.####',
`web_item_name` varchar(140),
`route` text,
`has_variants` tinyint NOT NULL DEFAULT 0,
`variant_of` varchar(140),
`published` tinyint NOT NULL DEFAULT 1,
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`description` longtext,
`brand` varchar(140),
`website_image` text,
`website_image_alt` varchar(140),
`slideshow` varchar(140),
`thumbnail` varchar(140),
`website_warehouse` varchar(140),
`on_backorder` tinyint NOT NULL DEFAULT 0,
`short_description` text,
`web_long_description` longtext,
`show_tabbed_section` tinyint NOT NULL DEFAULT 0,
`ranking` int NOT NULL DEFAULT 0,
`website_content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variant_of`(`variant_of`),
index `item_group`(`item_group`),
index `brand`(`brand`),
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:27,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Item`
				ADD INDEX IF NOT EXISTS `route_index`(route(500))
2025-04-25 12:33:27,249 WARNING database DDL Query made to DB:
create table `tabItem Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`website_item` varchar(140),
`user` varchar(140),
`customer` varchar(140),
`item` varchar(140),
`published_on` varchar(140),
`review_title` varchar(140),
`rating` decimal(3,2),
`comment` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:27,398 WARNING database DDL Query made to DB:
create table `tabWebsite Offer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`offer_title` varchar(140),
`offer_subtitle` varchar(140),
`offer_details` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:27,474 WARNING database DDL Query made to DB:
create table `tabWebsite Item Tabbed Section` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`content` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-25 12:33:28,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `published_in_website` tinyint NOT NULL DEFAULT 0
2025-04-25 12:33:28,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `is_fixed_asset` tinyint NOT NULL DEFAULT 0, MODIFY `safety_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `over_billing_allowance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `no_of_months` int NOT NULL DEFAULT 0, MODIFY `sample_quantity` int NOT NULL DEFAULT 0, MODIFY `max_discount` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `has_serial_no` tinyint NOT NULL DEFAULT 0, MODIFY `auto_create_assets` tinyint NOT NULL DEFAULT 0, MODIFY `valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `opening_stock` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_customer_provided_item` tinyint NOT NULL DEFAULT 0, MODIFY `has_variants` tinyint NOT NULL DEFAULT 0, MODIFY `is_stock_item` tinyint NOT NULL DEFAULT 1, MODIFY `lead_time_days` int NOT NULL DEFAULT 0, MODIFY `inspection_required_before_purchase` tinyint NOT NULL DEFAULT 0, MODIFY `last_purchase_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `retain_sample` tinyint NOT NULL DEFAULT 0, MODIFY `delivered_by_supplier` tinyint NOT NULL DEFAULT 0, MODIFY `min_order_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `standard_rate` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `allow_alternative_item` tinyint NOT NULL DEFAULT 0, MODIFY `is_purchase_item` tinyint NOT NULL DEFAULT 1, MODIFY `has_batch_no` tinyint NOT NULL DEFAULT 0, MODIFY `total_projected_qty` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `is_grouped_asset` tinyint NOT NULL DEFAULT 0, MODIFY `shelf_life_in_days` int NOT NULL DEFAULT 0, MODIFY `allow_negative_stock` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `enable_deferred_expense` tinyint NOT NULL DEFAULT 0, MODIFY `create_new_batch` tinyint NOT NULL DEFAULT 0, MODIFY `include_item_in_manufacturing` tinyint NOT NULL DEFAULT 1, MODIFY `inspection_required_before_delivery` tinyint NOT NULL DEFAULT 0, MODIFY `is_sub_contracted_item` tinyint NOT NULL DEFAULT 0, MODIFY `has_expiry_date` tinyint NOT NULL DEFAULT 0, MODIFY `enable_deferred_revenue` tinyint NOT NULL DEFAULT 0, MODIFY `is_sales_item` tinyint NOT NULL DEFAULT 1, MODIFY `over_delivery_receipt_allowance` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0, MODIFY `grant_commission` tinyint NOT NULL DEFAULT 1, MODIFY `no_of_months_exp` int NOT NULL DEFAULT 0
2025-04-25 12:33:29,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Group` ADD COLUMN `show_in_website` tinyint NOT NULL DEFAULT 0, ADD COLUMN `route` varchar(140) UNIQUE, ADD COLUMN `website_title` varchar(140), ADD COLUMN `description` longtext, ADD COLUMN `include_descendants` tinyint NOT NULL DEFAULT 0, ADD COLUMN `weightage` int NOT NULL DEFAULT 0, ADD COLUMN `slideshow` varchar(140)
2025-04-25 12:33:29,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Group` MODIFY `rgt` int NOT NULL DEFAULT 0, MODIFY `is_group` tinyint NOT NULL DEFAULT 0, MODIFY `lft` int NOT NULL DEFAULT 0, ADD UNIQUE INDEX IF NOT EXISTS route (`route`)
2025-04-25 12:33:39,883 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-25 12:33:43,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0
2025-04-25 12:33:43,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0
2025-04-28 14:26:27,235 WARNING database DDL Query made to DB:
create table `tabWiki App Switcher List Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`app_title` varchar(140),
`wiki_space` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:27,397 WARNING database DDL Query made to DB:
create table `tabWiki Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`wiki_page` varchar(140) UNIQUE,
`parent_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:27,514 WARNING database DDL Query made to DB:
create table `tabWiki Space` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`space_name` varchar(140),
`route` varchar(140) UNIQUE,
`app_switcher_logo` text,
`light_mode_logo` text,
`dark_mode_logo` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:27,782 WARNING database DDL Query made to DB:
create table `tabWiki Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`parent_label` varchar(140),
`wiki_page` varchar(140),
`hide_on_sidebar` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:27,903 WARNING database DDL Query made to DB:
create table `tabWiki Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`route` varchar(140) UNIQUE,
`published` tinyint NOT NULL DEFAULT 0,
`allow_guest` tinyint NOT NULL DEFAULT 1,
`content` longtext DEFAULT 'No Content',
`meta_description` text,
`meta_image` text,
`meta_keywords` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:28,149 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`wiki_page` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:28,287 WARNING database DDL Query made to DB:
create table `tabWiki Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`wiki_page` varchar(140),
`status` varchar(140) DEFAULT 'Open',
`rating` decimal(3,2),
`feedback` text,
`email_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:28,501 WARNING database DDL Query made to DB:
create table `tabWiki Page Patch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`wiki_page` varchar(140),
`new_title` varchar(140),
`new_sidebar_group` varchar(140),
`message` text,
`raised_by` varchar(140),
`status` varchar(140) DEFAULT 'Under Review',
`approved_by` varchar(140),
`new` tinyint NOT NULL DEFAULT 0,
`orignal_code` longtext,
`new_code` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:28,622 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`content` longtext,
`raised_by` varchar(140),
`raised_by_username` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-28 14:26:39,354 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-28 14:26:46,533 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` MODIFY `is_primary_address` tinyint NOT NULL DEFAULT 0, MODIFY `disabled` tinyint NOT NULL DEFAULT 0, MODIFY `is_your_company_address` tinyint NOT NULL DEFAULT 0, MODIFY `is_shipping_address` tinyint NOT NULL DEFAULT 0
2025-04-28 14:26:46,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` MODIFY `is_billing_contact` tinyint NOT NULL DEFAULT 0, MODIFY `unsubscribed` tinyint NOT NULL DEFAULT 0, MODIFY `sync_with_google_contacts` tinyint NOT NULL DEFAULT 0, MODIFY `is_primary_contact` tinyint NOT NULL DEFAULT 0, MODIFY `pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0
2025-07-01 14:58:59,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `is_setup_complete` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:00,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `rows_threshold_for_grid_search` int(11) NOT NULL DEFAULT 0
2025-07-01 14:59:00,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `show_full_number` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `background_color` varchar(140)
2025-07-01 14:59:00,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `show_absolute_datetime_in_timeline` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:01,098 WARNING database DDL Query made to DB:
create table `tabAPI Request Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`path` varchar(140),
`method` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:01,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` ADD COLUMN `email_header` varchar(140)
2025-07-01 14:59:01,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-07-01 14:59:02,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry` ADD COLUMN `total_taxes_and_charges` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:02,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) DEFAULT 'Categorize by Voucher (Consolidated)'
2025-07-01 14:59:03,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Statement Import` ADD COLUMN `import_mt940_fromat` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `use_csv_sniffer` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:04,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `for_all_stock_asset_accounts` tinyint(4) NOT NULL DEFAULT 1, ADD COLUMN `stock_asset_account` varchar(140), ADD COLUMN `periodic_entry_difference_account` varchar(140)
2025-07-01 14:59:04,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabBudget` ADD COLUMN `applicable_on_cumulative_expense` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `action_if_annual_exceeded_on_cumulative_expense` varchar(140), ADD COLUMN `action_if_accumulated_monthly_exceeded_on_cumulative_expense` varchar(140)
2025-07-01 14:59:04,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-07-01 14:59:04,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `action_on_new_invoice` varchar(140) DEFAULT 'Always Ask', ADD COLUMN `set_grand_total_to_default_mop` tinyint(4) NOT NULL DEFAULT 1, ADD COLUMN `allow_partial_payment` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:05,082 WARNING database DDL Query made to DB:
create table `tabSales Invoice Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sales_invoice` varchar(140),
`posting_date` date,
`customer` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_return` tinyint(4) NOT NULL DEFAULT 0,
`return_against` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:05,264 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:05,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `is_created_using_pos` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `pos_closing_entry` varchar(140)
2025-07-01 14:59:06,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-07-01 14:59:06,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:07,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:07,436 WARNING database DDL Query made to DB:
create table `tabCustomer Number At Supplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`customer_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:07,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` ADD COLUMN `margin_type` varchar(140), ADD COLUMN `margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0, ADD COLUMN `rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:08,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-07-01 14:59:08,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:08,897 WARNING database DDL Query made to DB:
create table `tabSupplier Number At Customer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`supplier_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:09,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:09,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Plan Item` ADD COLUMN `stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:09,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` ADD COLUMN `is_additional_item` tinyint(4) NOT NULL DEFAULT 0, ADD COLUMN `voucher_detail_reference` varchar(140)
2025-07-01 14:59:09,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `required_qty` decimal(21,9) NOT NULL DEFAULT 0.0, ADD COLUMN `stock_reserved_qty` decimal(21,9) NOT NULL DEFAULT 0.0, ADD COLUMN `ordered_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:10,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan` ADD COLUMN `reserve_stock` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:10,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:10,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item Reference` MODIFY `qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:10,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `landed_cost_voucher_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:10,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:11,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-01 14:59:11,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-01 14:59:11,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `stock_uom` varchar(140)
2025-07-01 14:59:11,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `warehouse`, DROP INDEX `posting_date`, DROP INDEX `posting_datetime`
2025-07-01 14:59:11,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-07-01 14:59:12,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-07-01 14:59:12,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` ADD COLUMN `is_packed` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:13,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `stock_entry_item` varchar(140)
2025-07-01 14:59:13,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:13,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` ADD COLUMN `consumed_items_cost` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:13,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:14,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` ADD COLUMN `is_composite_component` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:14,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` ADD COLUMN `increase_in_asset_life` int(11) NOT NULL DEFAULT 0
2025-07-01 14:59:14,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Depreciation Schedule` ADD COLUMN `value_after_depreciation` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:15,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-07-01 14:59:15,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD COLUMN `landed_cost_voucher_amount` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:15,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `modify_half_day_status` tinyint(4) NOT NULL DEFAULT 0
2025-07-01 14:59:15,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD COLUMN `tax_relief_limit` decimal(21,9) NOT NULL DEFAULT 0.0
2025-07-01 14:59:16,132 WARNING database DDL Query made to DB:
create table `tabChat Message` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`naming_series` varchar(140),
`chat_room` varchar(140),
`sender` varchar(140),
`recipient` varchar(140),
`message_type` varchar(140) DEFAULT 'Text',
`message_text` text,
`attachment` text,
`product_reference` varchar(140),
`order_reference` varchar(140),
`timestamp` datetime(6),
`is_read` tinyint(4) NOT NULL DEFAULT 0,
`read_timestamp` datetime(6),
`is_deleted` tinyint(4) NOT NULL DEFAULT 0,
`deleted_by` varchar(140),
`deleted_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:16,268 WARNING database DDL Query made to DB:
create table `tabShopping Cart Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`product` varchar(140),
`product_name` varchar(140),
`quantity` int(11) NOT NULL DEFAULT 1,
`price` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:16,371 WARNING database DDL Query made to DB:
create table `tabShopping Cart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
`session_id` varchar(140),
`total_items` int(11) NOT NULL DEFAULT 0,
`subtotal` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`currency` varchar(140) DEFAULT 'TZS',
`last_updated` datetime(6),
`expires_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:16,503 WARNING database DDL Query made to DB:
create table `tabEcommerce User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140) UNIQUE,
`user_type` varchar(140),
`account_tier` varchar(140) DEFAULT 'Basic',
`verification_status` varchar(140) DEFAULT 'Pending',
`nida_number` varchar(140),
`business_license` text,
`business_license_number` varchar(140),
`phone_number` varchar(140),
`location_details` json,
`address_line_1` varchar(140),
`address_line_2` varchar(140),
`city` varchar(140),
`state` varchar(140),
`postal_code` varchar(140),
`country` varchar(140),
`company_name` varchar(140),
`business_type` varchar(140),
`tax_id` varchar(140),
`website` varchar(140),
`social_media_links` json,
`preferred_language` varchar(140) DEFAULT 'English',
`account_balance` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_limit` decimal(21,9) NOT NULL DEFAULT 0.0,
`loyalty_points` int(11) NOT NULL DEFAULT 0,
`total_orders` int(11) NOT NULL DEFAULT 0,
`total_spent` decimal(21,9) NOT NULL DEFAULT 0.0,
`last_order_date` date,
`verification_documents` json,
`notes` text,
`is_active` tinyint(4) NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:16,614 WARNING database DDL Query made to DB:
create table `tabEcommerce Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`product` varchar(140),
`product_name` varchar(140),
`quantity` int(11) NOT NULL DEFAULT 0,
`price` decimal(21,9) NOT NULL DEFAULT 0.0,
`tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`total` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-01 14:59:16,718 WARNING database DDL Query made to DB:
create table `tabReview Image` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`image` text,
`caption` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
