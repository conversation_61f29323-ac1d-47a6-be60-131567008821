2025-04-22 15:32:51,886 WARNING database DDL Query made to DB:
create table `tabWebsite Theme Ignore App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`app` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,056 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,129 WARNING database DDL Query made to DB:
create table `tabColor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,212 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`slideshow_name` varchar(140) UNIQUE,
`header` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,289 WARNING database DDL Query made to DB:
create table `tabUTM Medium` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`slug` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,370 WARNING database DDL Query made to DB:
create table `tabTop Bar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`url` varchar(140),
`open_in_new_tab` tinyint NOT NULL DEFAULT 0,
`right` tinyint NOT NULL DEFAULT 1,
`parent_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,463 WARNING database DDL Query made to DB:
create table `tabUTM Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`slug` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,551 WARNING database DDL Query made to DB:
create table `tabWeb Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`fieldtype` varchar(140) DEFAULT 'Data',
`reqd` tinyint NOT NULL DEFAULT 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,632 WARNING database DDL Query made to DB:
create table `tabBlogger` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disabled` tinyint NOT NULL DEFAULT 0,
`short_name` varchar(140) UNIQUE,
`full_name` varchar(140),
`user` varchar(140),
`bio` text,
`avatar` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,760 WARNING database DDL Query made to DB:
create table `tabWeb Page Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`web_template` varchar(140),
`web_template_values` longtext,
`css_class` text,
`section_id` varchar(140),
`add_container` tinyint NOT NULL DEFAULT 1,
`add_top_padding` tinyint NOT NULL DEFAULT 1,
`add_bottom_padding` tinyint NOT NULL DEFAULT 1,
`add_border_at_top` tinyint NOT NULL DEFAULT 0,
`add_border_at_bottom` tinyint NOT NULL DEFAULT 0,
`add_shade` tinyint NOT NULL DEFAULT 0,
`hide_block` tinyint NOT NULL DEFAULT 0,
`add_background_image` tinyint NOT NULL DEFAULT 0,
`background_image` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,900 WARNING database DDL Query made to DB:
create table `tabBlog Post` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`blog_category` varchar(140),
`blogger` varchar(140),
`route` varchar(140) UNIQUE,
`read_time` int NOT NULL DEFAULT 0,
`published_on` date,
`published` tinyint NOT NULL DEFAULT 0,
`featured` tinyint NOT NULL DEFAULT 0,
`hide_cta` tinyint NOT NULL DEFAULT 0,
`enable_email_notification` tinyint NOT NULL DEFAULT 1,
`disable_comments` tinyint NOT NULL DEFAULT 0,
`disable_likes` tinyint NOT NULL DEFAULT 0,
`blog_intro` text,
`content_type` varchar(140) DEFAULT 'Markdown',
`content` longtext,
`content_md` longtext,
`content_html` longtext,
`email_sent` tinyint NOT NULL DEFAULT 0,
`meta_title` varchar(60),
`meta_description` text,
`meta_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:52,969 WARNING database DDL Query made to DB:
create sequence if not exists web_form_list_column_id_seq nocache nocycle
2025-04-22 15:32:52,989 WARNING database DDL Query made to DB:
create table `tabWeb Form List Column` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:53,675 WARNING database DDL Query made to DB:
create table `tabWorkflow Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140),
`reference_name` varchar(140),
`reference_doctype` varchar(140),
`workflow_state` varchar(140),
`completed_by_role` varchar(140),
`completed_by` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:53,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Action`
				ADD INDEX IF NOT EXISTS `reference_name_reference_doctype_status_index`(reference_name, reference_doctype, status)
2025-04-22 15:32:53,954 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Permitted Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,038 WARNING database DDL Query made to DB:
create table `tabWorkflow Document State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`state` varchar(140),
`doc_status` varchar(140) DEFAULT '0',
`update_field` varchar(140),
`update_value` varchar(140),
`is_optional_state` tinyint NOT NULL DEFAULT 0,
`avoid_status_override` tinyint NOT NULL DEFAULT 0,
`next_action_email_template` varchar(140),
`allow_edit` varchar(140),
`send_email` tinyint NOT NULL DEFAULT 1,
`message` text,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,124 WARNING database DDL Query made to DB:
create table `tabWorkflow State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workflow_state_name` varchar(140) UNIQUE,
`icon` varchar(140),
`style` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,218 WARNING database DDL Query made to DB:
create table `tabWorkflow Transition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`state` varchar(140),
`action` varchar(140),
`next_state` varchar(140),
`allowed` varchar(140),
`allow_self_approval` tinyint NOT NULL DEFAULT 1,
`send_email_to_creator` tinyint NOT NULL DEFAULT 0,
`condition` longtext,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,279 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Master` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workflow_action_name` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,365 WARNING database DDL Query made to DB:
create table `tabWorkflow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`workflow_name` varchar(140) UNIQUE,
`document_type` varchar(140),
`is_active` tinyint NOT NULL DEFAULT 0,
`override_status` tinyint NOT NULL DEFAULT 0,
`send_email_alert` tinyint NOT NULL DEFAULT 0,
`workflow_state_field` varchar(140) DEFAULT 'workflow_state',
`workflow_data` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,505 WARNING database DDL Query made to DB:
create table `tabEmail Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` varchar(140),
`use_html` tinyint NOT NULL DEFAULT 0,
`response_html` longtext,
`response` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,702 WARNING database DDL Query made to DB:
create table `tabEmail Unsubscribe` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`global_unsubscribe` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email`(`email`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,786 WARNING database DDL Query made to DB:
create table `tabIMAP Folder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`folder_name` varchar(140),
`append_to` varchar(140),
`uidvalidity` varchar(140),
`uidnext` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,898 WARNING database DDL Query made to DB:
create table `tabEmail Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`domain_name` varchar(140) UNIQUE,
`email_server` varchar(140),
`use_imap` tinyint NOT NULL DEFAULT 0,
`use_ssl` tinyint NOT NULL DEFAULT 0,
`validate_ssl_certificate` tinyint NOT NULL DEFAULT 1,
`use_starttls` tinyint NOT NULL DEFAULT 0,
`incoming_port` varchar(140),
`attachment_limit` int NOT NULL DEFAULT 0,
`smtp_server` varchar(140),
`use_tls` tinyint NOT NULL DEFAULT 0,
`use_ssl_for_outgoing` tinyint NOT NULL DEFAULT 0,
`validate_ssl_certificate_for_outgoing` tinyint NOT NULL DEFAULT 1,
`smtp_port` varchar(140),
`append_emails_to_sent_folder` tinyint NOT NULL DEFAULT 0,
`sent_folder_name` varchar(140) DEFAULT 'Sent',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:54,981 WARNING database DDL Query made to DB:
create table `tabUnhandled Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_account` varchar(140),
`uid` varchar(140),
`reason` longtext,
`message_id` longtext,
`raw` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,092 WARNING database DDL Query made to DB:
create table `tabEmail Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`sender` varchar(140),
`show_as_cc` text,
`message` longtext,
`status` varchar(140) DEFAULT 'Not Sent',
`error` longtext,
`message_id` text,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`communication` varchar(140),
`send_after` datetime(6),
`priority` int NOT NULL DEFAULT 1,
`add_unsubscribe_link` tinyint NOT NULL DEFAULT 1,
`unsubscribe_params` longtext,
`unsubscribe_method` varchar(140),
`expose_recipients` varchar(140),
`attachments` longtext,
`retry` int NOT NULL DEFAULT 0,
`email_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index `communication`(`communication`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,166 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `index_bulk_flush`(status, send_after, priority, creation)
2025-04-22 15:32:55,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-04-22 15:32:55,401 WARNING database DDL Query made to DB:
create table `tabEmail Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_id` varchar(140),
`email_account_name` varchar(140) UNIQUE,
`enable_incoming` tinyint NOT NULL DEFAULT 0,
`enable_outgoing` tinyint NOT NULL DEFAULT 0,
`service` varchar(140),
`domain` varchar(140),
`frappe_mail_site` varchar(140) DEFAULT 'https://frappemail.com',
`auth_method` varchar(140) DEFAULT 'Basic',
`backend_app_flow` tinyint NOT NULL DEFAULT 0,
`password` text,
`awaiting_password` tinyint NOT NULL DEFAULT 0,
`ascii_encode_password` tinyint NOT NULL DEFAULT 0,
`api_key` varchar(140),
`api_secret` text,
`connected_app` varchar(140),
`connected_user` varchar(140),
`login_id_is_different` tinyint NOT NULL DEFAULT 0,
`login_id` varchar(140),
`default_incoming` tinyint NOT NULL DEFAULT 0,
`attachment_limit` int NOT NULL DEFAULT 0,
`last_synced_at` datetime(6),
`use_imap` tinyint NOT NULL DEFAULT 0,
`use_ssl` tinyint NOT NULL DEFAULT 0,
`validate_ssl_certificate` tinyint NOT NULL DEFAULT 1,
`use_starttls` tinyint NOT NULL DEFAULT 0,
`email_server` varchar(140),
`incoming_port` varchar(140),
`email_sync_option` varchar(140) DEFAULT 'UNSEEN',
`initial_sync_count` varchar(140) DEFAULT '250',
`append_emails_to_sent_folder` tinyint NOT NULL DEFAULT 0,
`sent_folder_name` varchar(140),
`append_to` varchar(140),
`create_contact` tinyint NOT NULL DEFAULT 1,
`enable_automatic_linking` tinyint NOT NULL DEFAULT 0,
`notify_if_unreplied` tinyint NOT NULL DEFAULT 0,
`unreplied_for_mins` int NOT NULL DEFAULT 30,
`send_notification_to` text,
`default_outgoing` tinyint NOT NULL DEFAULT 0,
`always_use_account_email_id_as_sender` tinyint NOT NULL DEFAULT 0,
`always_use_account_name_as_sender_name` tinyint NOT NULL DEFAULT 0,
`send_unsubscribe_message` tinyint NOT NULL DEFAULT 1,
`track_email_status` tinyint NOT NULL DEFAULT 1,
`use_tls` tinyint NOT NULL DEFAULT 0,
`use_ssl_for_outgoing` tinyint NOT NULL DEFAULT 0,
`smtp_server` varchar(140),
`smtp_port` varchar(140),
`no_smtp_authentication` tinyint NOT NULL DEFAULT 0,
`always_bcc` varchar(140),
`add_signature` tinyint NOT NULL DEFAULT 0,
`signature` longtext,
`enable_auto_reply` tinyint NOT NULL DEFAULT 0,
`auto_reply_message` longtext,
`footer` longtext,
`brand_logo` text,
`uidvalidity` varchar(140),
`uidnext` int NOT NULL DEFAULT 0,
`no_failed` int NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,453 WARNING database DDL Query made to DB:
alter table `tabEmail Account`
					add unique `unique_email_account_type`(email_id, enable_incoming, enable_outgoing)
2025-04-22 15:32:55,499 WARNING database DDL Query made to DB:
create table `tabNewsletter Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,567 WARNING database DDL Query made to DB:
create table `tabEmail Queue Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`recipient` varchar(140),
`status` varchar(140) DEFAULT 'Not Sent',
`error` longtext,
index `status`(`status`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient`
				ADD INDEX IF NOT EXISTS `modified_index`(modified)
2025-04-22 15:32:55,698 WARNING database DDL Query made to DB:
create table `tabDocument Follow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `user`(`user`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,769 WARNING database DDL Query made to DB:
create table `tabEmail Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_group` varchar(140),
`email` varchar(140),
`unsubscribed` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_group`(`email_group`),
index `unsubscribed`(`unsubscribed`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,813 WARNING database DDL Query made to DB:
alter table `tabEmail Group Member`
					add unique `unique_email_group_email`(email_group, email)
2025-04-22 15:32:55,859 WARNING database DDL Query made to DB:
create table `tabNewsletter Email Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_group` varchar(140),
`total_subscribers` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:55,965 WARNING database DDL Query made to DB:
create table `tabNewsletter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_sent_at` datetime(6),
`total_recipients` int NOT NULL DEFAULT 0,
`total_views` int NOT NULL DEFAULT 0,
`email_sent` tinyint NOT NULL DEFAULT 0,
`sender_name` varchar(140),
`sender_email` varchar(140),
`send_from` varchar(140),
`subject` text,
`content_type` varchar(140),
`message` longtext,
`message_md` longtext,
`message_html` longtext,
`campaign` varchar(140),
`send_unsubscribe_link` tinyint NOT NULL DEFAULT 1,
`send_webview_link` tinyint NOT NULL DEFAULT 0,
`scheduled_to_send` int NOT NULL DEFAULT 0,
`schedule_sending` tinyint NOT NULL DEFAULT 0,
`schedule_send` datetime(6),
`published` tinyint NOT NULL DEFAULT 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,030 WARNING database DDL Query made to DB:
create table `tabEmail Flag Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_completed` tinyint NOT NULL DEFAULT 0,
`communication` varchar(140),
`action` varchar(140),
`email_account` varchar(140),
`uid` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,104 WARNING database DDL Query made to DB:
create table `tabEmail Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_id` varchar(140) UNIQUE,
`is_spam` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,223 WARNING database DDL Query made to DB:
create table `tabNotification Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`receiver_by_document_field` varchar(140),
`receiver_by_role` varchar(140),
`cc` longtext,
`bcc` longtext,
`condition` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,362 WARNING database DDL Query made to DB:
create table `tabAuto Email Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`report` varchar(140),
`user` varchar(140) DEFAULT 'User',
`enabled` tinyint NOT NULL DEFAULT 1,
`report_type` varchar(140),
`reference_report` varchar(140),
`send_if_data` tinyint NOT NULL DEFAULT 1,
`data_modified_till` int NOT NULL DEFAULT 0,
`no_of_rows` int NOT NULL DEFAULT 100,
`filters` text,
`filter_meta` text,
`from_date_field` varchar(140),
`to_date_field` varchar(140),
`dynamic_date_period` varchar(140),
`use_first_day_of_period` tinyint NOT NULL DEFAULT 0,
`email_to` text,
`day_of_week` varchar(140) DEFAULT 'Monday',
`sender` varchar(140),
`frequency` varchar(140),
`format` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,461 WARNING database DDL Query made to DB:
create table `tabEmail Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140) UNIQUE,
`total_subscribers` int NOT NULL DEFAULT 0,
`confirmation_email_template` varchar(140),
`welcome_email_template` varchar(140),
`welcome_url` varchar(140),
`add_query_parameters` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,531 WARNING database DDL Query made to DB:
create table `tabDocType Layout Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:56,730 WARNING database DDL Query made to DB:
create table `tabDocType Layout` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`route` varchar(140),
`client_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,058 WARNING database DDL Query made to DB:
create table `tabCustomize Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`is_system_generated` tinyint NOT NULL DEFAULT 0,
`label` varchar(140),
`fieldtype` varchar(140) DEFAULT 'Data',
`fieldname` varchar(140),
`non_negative` tinyint NOT NULL DEFAULT 0,
`reqd` tinyint NOT NULL DEFAULT 0,
`unique` tinyint NOT NULL DEFAULT 0,
`is_virtual` tinyint NOT NULL DEFAULT 0,
`in_list_view` tinyint NOT NULL DEFAULT 0,
`in_standard_filter` tinyint NOT NULL DEFAULT 0,
`in_global_search` tinyint NOT NULL DEFAULT 0,
`in_preview` tinyint NOT NULL DEFAULT 0,
`bold` tinyint NOT NULL DEFAULT 0,
`no_copy` tinyint NOT NULL DEFAULT 0,
`allow_in_quick_entry` tinyint NOT NULL DEFAULT 0,
`translatable` tinyint NOT NULL DEFAULT 1,
`link_filters` json,
`default` text,
`precision` varchar(140),
`length` int NOT NULL DEFAULT 0,
`options` text,
`sort_options` tinyint NOT NULL DEFAULT 0,
`fetch_from` text,
`fetch_if_empty` tinyint NOT NULL DEFAULT 0,
`show_dashboard` tinyint NOT NULL DEFAULT 0,
`depends_on` longtext,
`permlevel` int NOT NULL DEFAULT 0,
`hidden` tinyint NOT NULL DEFAULT 0,
`read_only` tinyint NOT NULL DEFAULT 0,
`collapsible` tinyint NOT NULL DEFAULT 0,
`allow_bulk_edit` tinyint NOT NULL DEFAULT 0,
`collapsible_depends_on` longtext,
`ignore_user_permissions` tinyint NOT NULL DEFAULT 0,
`allow_on_submit` tinyint NOT NULL DEFAULT 0,
`report_hide` tinyint NOT NULL DEFAULT 0,
`remember_last_selected_value` tinyint NOT NULL DEFAULT 0,
`hide_border` tinyint NOT NULL DEFAULT 0,
`ignore_xss_filter` tinyint NOT NULL DEFAULT 0,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`in_filter` tinyint NOT NULL DEFAULT 0,
`hide_seconds` tinyint NOT NULL DEFAULT 0,
`hide_days` tinyint NOT NULL DEFAULT 0,
`description` text,
`placeholder` varchar(140),
`print_hide` tinyint NOT NULL DEFAULT 0,
`print_hide_if_no_value` tinyint NOT NULL DEFAULT 0,
`print_width` varchar(140),
`columns` int NOT NULL DEFAULT 0,
`width` varchar(140),
`is_custom_field` tinyint NOT NULL DEFAULT 0,
index `label`(`label`),
index `fieldtype`(`fieldtype`),
index `fieldname`(`fieldname`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,235 WARNING database DDL Query made to DB:
create table `tabCurrency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`currency_name` varchar(140) UNIQUE,
`enabled` tinyint NOT NULL DEFAULT 0,
`fraction` varchar(140),
`fraction_units` int NOT NULL DEFAULT 0,
`smallest_currency_fraction_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`symbol` varchar(140),
`symbol_on_right` tinyint NOT NULL DEFAULT 0,
`number_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,544 WARNING database DDL Query made to DB:
create table `tabCountry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`country_name` varchar(140) UNIQUE,
`code` varchar(2),
`date_format` varchar(140),
`time_format` varchar(140) DEFAULT 'HH:mm:ss',
`time_zones` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,626 WARNING database DDL Query made to DB:
create table `tabChangelog Feed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`app_name` varchar(140),
`link` longtext,
`posting_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_timestamp`(`posting_timestamp`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,841 WARNING database DDL Query made to DB:
create table `tabTag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,898 WARNING database DDL Query made to DB:
create table `tabConsole Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`script` longtext,
`type` varchar(140),
`committed` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:57,976 WARNING database DDL Query made to DB:
create table `tabCustom HTML Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`private` tinyint NOT NULL DEFAULT 0,
`html` longtext,
`script` longtext,
`style` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,125 WARNING database DDL Query made to DB:
create table `tabNotification Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`enable_email_notifications` tinyint NOT NULL DEFAULT 1,
`enable_email_mention` tinyint NOT NULL DEFAULT 1,
`enable_email_assignment` tinyint NOT NULL DEFAULT 1,
`enable_email_threads_on_assigned_document` tinyint NOT NULL DEFAULT 1,
`enable_email_energy_point` tinyint NOT NULL DEFAULT 1,
`enable_email_share` tinyint NOT NULL DEFAULT 1,
`enable_email_event_reminders` tinyint NOT NULL DEFAULT 1,
`user` varchar(140),
`seen` tinyint NOT NULL DEFAULT 0,
`energy_points_system_notifications` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,202 WARNING database DDL Query made to DB:
create table `tabRoute History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`route` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,286 WARNING database DDL Query made to DB:
create table `tabNote` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`title` varchar(140),
`public` tinyint NOT NULL DEFAULT 0,
`notify_on_login` tinyint NOT NULL DEFAULT 0,
`notify_on_every_login` tinyint NOT NULL DEFAULT 0,
`expire_notification_on` datetime(6),
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `expire_notification_on`(`expire_notification_on`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,356 WARNING database DDL Query made to DB:
create table `tabDashboard Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`chart_config` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,490 WARNING database DDL Query made to DB:
create table `tabKanban Board` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`kanban_board_name` varchar(140) UNIQUE,
`reference_doctype` varchar(140),
`field_name` varchar(140),
`private` tinyint NOT NULL DEFAULT 0,
`show_labels` tinyint NOT NULL DEFAULT 0,
`filters` longtext,
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,589 WARNING database DDL Query made to DB:
create table `tabToDo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`status` varchar(140) DEFAULT 'Open',
`priority` varchar(140) DEFAULT 'Medium',
`color` varchar(140),
`date` date,
`allocated_to` varchar(140),
`description` longtext,
`reference_type` varchar(140),
`reference_name` varchar(140),
`role` varchar(140),
`assigned_by` varchar(140),
`assigned_by_full_name` varchar(140),
`sender` varchar(140),
`assignment_rule` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabToDo`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-04-22 15:32:58,776 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`y_field` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:58,874 WARNING database DDL Query made to DB:
create table `tabGlobal Search DocType` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,110 WARNING database DDL Query made to DB:
create table `tabNumber Card Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,202 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`chart` varchar(140),
`width` varchar(140) DEFAULT 'Half',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,281 WARNING database DDL Query made to DB:
create table `tabList Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`filter_name` varchar(140),
`reference_doctype` varchar(140),
`for_user` varchar(140),
`filters` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,351 WARNING database DDL Query made to DB:
create table `tabNote Seen By` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,542 WARNING database DDL Query made to DB:
create table `tabEvent Participants` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,746 WARNING database DDL Query made to DB:
create table `tabTag Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`tag` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-04-22 15:32:59,870 WARNING database DDL Query made to DB:
create table `tabNotification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` text,
`for_user` varchar(140),
`type` varchar(140),
`email_content` longtext,
`document_type` varchar(140),
`read` tinyint NOT NULL DEFAULT 0,
`document_name` varchar(140),
`attached_file` longtext,
`from_user` varchar(140),
`link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `for_user`(`for_user`),
index `document_name`(`document_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:32:59,941 WARNING database DDL Query made to DB:
create table `tabCalendar View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`subject_field` varchar(140),
`start_date_field` varchar(140),
`end_date_field` varchar(140),
`all_day` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,009 WARNING database DDL Query made to DB:
create table `tabKanban Board Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`column_name` varchar(140),
`status` varchar(140) DEFAULT 'Active',
`indicator` varchar(140) DEFAULT 'Gray',
`order` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,078 WARNING database DDL Query made to DB:
create table `tabNotification Subscribed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,216 WARNING database DDL Query made to DB:
create table `tabDesktop Icon` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`module_name` varchar(140),
`label` varchar(140),
`standard` tinyint NOT NULL DEFAULT 0,
`custom` tinyint NOT NULL DEFAULT 0,
`app` varchar(140),
`description` text,
`category` varchar(140),
`hidden` tinyint NOT NULL DEFAULT 0,
`blocked` tinyint NOT NULL DEFAULT 0,
`force_show` tinyint NOT NULL DEFAULT 0,
`type` varchar(140),
`_doctype` varchar(140),
`_report` varchar(140),
`link` text,
`color` varchar(140),
`icon` varchar(140),
`reverse` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,280 WARNING database DDL Query made to DB:
alter table `tabDesktop Icon`
					add unique `unique_module_name_owner_standard`(module_name, owner, standard)
2025-04-22 15:33:00,340 WARNING database DDL Query made to DB:
create table `tabList View Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`disable_count` tinyint NOT NULL DEFAULT 0,
`disable_comment_count` tinyint NOT NULL DEFAULT 0,
`disable_sidebar_stats` tinyint NOT NULL DEFAULT 0,
`disable_auto_refresh` tinyint NOT NULL DEFAULT 0,
`allow_edit` tinyint NOT NULL DEFAULT 0,
`disable_automatic_recency_filters` tinyint NOT NULL DEFAULT 0,
`total_fields` varchar(140),
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,459 WARNING database DDL Query made to DB:
create table `tabEvent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`subject` text,
`event_category` varchar(140),
`event_type` varchar(140),
`color` varchar(140),
`send_reminder` tinyint NOT NULL DEFAULT 1,
`repeat_this_event` tinyint NOT NULL DEFAULT 0,
`starts_on` datetime(6),
`ends_on` datetime(6),
`status` varchar(140) DEFAULT 'Open',
`sender` varchar(140),
`all_day` tinyint NOT NULL DEFAULT 0,
`sync_with_google_calendar` tinyint NOT NULL DEFAULT 0,
`add_video_conferencing` tinyint NOT NULL DEFAULT 0,
`google_calendar` varchar(140),
`google_calendar_id` varchar(140),
`google_calendar_event_id` varchar(320),
`google_meet_link` text,
`pulled_from_google_calendar` tinyint NOT NULL DEFAULT 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` tinyint NOT NULL DEFAULT 0,
`tuesday` tinyint NOT NULL DEFAULT 0,
`wednesday` tinyint NOT NULL DEFAULT 0,
`thursday` tinyint NOT NULL DEFAULT 0,
`friday` tinyint NOT NULL DEFAULT 0,
`saturday` tinyint NOT NULL DEFAULT 0,
`sunday` tinyint NOT NULL DEFAULT 0,
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `event_type`(`event_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,723 WARNING database DDL Query made to DB:
create table `tabWebhook Header` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key` text,
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:00,957 WARNING database DDL Query made to DB:
create table `tabWebhook` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`webhook_doctype` varchar(140),
`webhook_docevent` varchar(140),
`enabled` tinyint NOT NULL DEFAULT 1,
`condition` text,
`request_url` text,
`is_dynamic_url` tinyint NOT NULL DEFAULT 0,
`timeout` int NOT NULL DEFAULT 5,
`background_jobs_queue` varchar(140),
`request_method` varchar(140) DEFAULT 'POST',
`request_structure` varchar(140),
`enable_security` tinyint NOT NULL DEFAULT 0,
`webhook_secret` text,
`webhook_json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,095 WARNING database DDL Query made to DB:
create table `tabOAuth Client` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`client_id` varchar(140),
`app_name` varchar(140),
`user` varchar(140),
`client_secret` varchar(140),
`skip_authorization` tinyint NOT NULL DEFAULT 0,
`scopes` text DEFAULT 'all openid',
`redirect_uris` text,
`default_redirect_uri` varchar(140),
`grant_type` varchar(140),
`response_type` varchar(140) DEFAULT 'Code',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,420 WARNING database DDL Query made to DB:
create table `tabLDAP Group Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`ldap_group` varchar(140),
`erpnext_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,476 WARNING database DDL Query made to DB:
create table `tabSlack Webhook URL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`webhook_name` varchar(140) UNIQUE,
`webhook_url` varchar(140),
`show_document_link` tinyint NOT NULL DEFAULT 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,592 WARNING database DDL Query made to DB:
create table `tabOAuth Authorization Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`authorization_code` varchar(140) UNIQUE,
`expiration_time` datetime(6),
`redirect_uri_bound_to_authorization_code` varchar(140),
`validity` varchar(140),
`nonce` varchar(140),
`code_challenge` varchar(140),
`code_challenge_method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,804 WARNING database DDL Query made to DB:
create table `tabOAuth Bearer Token` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`client` varchar(140),
`user` varchar(140) NOT NULL DEFAULT '',
`scopes` text,
`access_token` varchar(140) UNIQUE,
`refresh_token` varchar(140),
`expiration_time` datetime(6),
`expires_in` int NOT NULL DEFAULT 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `expiration_time`(`expiration_time`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:01,878 WARNING database DDL Query made to DB:
create table `tabSocial Login Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enable_social_login` tinyint NOT NULL DEFAULT 0,
`social_login_provider` varchar(140) DEFAULT 'Custom',
`client_id` varchar(140),
`provider_name` varchar(140),
`client_secret` text,
`icon` varchar(140),
`base_url` varchar(140),
`sign_ups` varchar(140),
`authorize_url` varchar(140),
`access_token_url` varchar(140),
`redirect_url` varchar(140),
`api_endpoint` varchar(140),
`custom_base_url` tinyint NOT NULL DEFAULT 0,
`api_endpoint_args` longtext,
`auth_url_data` longtext,
`user_id_property` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,139 WARNING database DDL Query made to DB:
create table `tabGoogle Calendar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enable` tinyint NOT NULL DEFAULT 1,
`calendar_name` varchar(140) UNIQUE,
`user` varchar(140),
`pull_from_google_calendar` tinyint NOT NULL DEFAULT 1,
`sync_as_public` tinyint NOT NULL DEFAULT 0,
`push_to_google_calendar` tinyint NOT NULL DEFAULT 1,
`google_calendar_id` varchar(140),
`refresh_token` text,
`authorization_code` text,
`next_sync_token` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,393 WARNING database DDL Query made to DB:
create table `tabConnected App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`provider_name` varchar(140),
`openid_configuration` varchar(140),
`client_id` varchar(140),
`redirect_uri` varchar(140),
`client_secret` text,
`authorization_uri` text,
`token_uri` varchar(140),
`revocation_uri` varchar(140),
`userinfo_uri` varchar(140),
`introspection_uri` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,483 WARNING database DDL Query made to DB:
create table `tabQuery Parameters` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`key` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,550 WARNING database DDL Query made to DB:
create table `tabGoogle Contacts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enable` tinyint NOT NULL DEFAULT 0,
`email_id` varchar(140),
`last_sync_on` datetime(6),
`authorization_code` text,
`refresh_token` text,
`next_sync_token` text,
`pull_from_google_contacts` tinyint NOT NULL DEFAULT 0,
`push_to_google_contacts` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,628 WARNING database DDL Query made to DB:
create table `tabWebhook Request Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`webhook` varchar(140),
`reference_doctype` varchar(140),
`reference_document` varchar(140),
`headers` longtext,
`data` longtext,
`user` varchar(140),
`url` text,
`response` longtext,
`error` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `webhook`(`webhook`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,682 WARNING database DDL Query made to DB:
create table `tabOAuth Client Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,735 WARNING database DDL Query made to DB:
create table `tabOAuth Scope` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`scope` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,806 WARNING database DDL Query made to DB:
create table `tabIntegration Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`request_id` varchar(140),
`integration_request_service` varchar(140),
`is_remote_request` tinyint NOT NULL DEFAULT 0,
`request_description` varchar(140),
`status` varchar(140) DEFAULT 'Queued',
`url` text,
`request_headers` longtext,
`data` longtext,
`output` longtext,
`error` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,885 WARNING database DDL Query made to DB:
create table `tabToken Cache` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`connected_app` varchar(140),
`provider_name` varchar(140),
`access_token` text,
`refresh_token` text,
`expires_in` int NOT NULL DEFAULT 0,
`state` varchar(140),
`success_uri` varchar(140),
`token_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:02,944 WARNING database DDL Query made to DB:
create table `tabWebhook Data` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`fieldname` varchar(140),
`key` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,054 WARNING database DDL Query made to DB:
create table `tabPrint Heading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`print_heading` varchar(140) UNIQUE,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,217 WARNING database DDL Query made to DB:
create table `tabNetwork Printer Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`server_ip` varchar(140) DEFAULT 'localhost',
`port` int NOT NULL DEFAULT 631,
`printer_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,299 WARNING database DDL Query made to DB:
create table `tabLetter Head` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`letter_head_name` varchar(140) UNIQUE,
`source` varchar(140),
`footer_source` varchar(140) DEFAULT 'HTML',
`disabled` tinyint NOT NULL DEFAULT 0,
`is_default` tinyint NOT NULL DEFAULT 0,
`image` text,
`image_height` decimal(21,9) NOT NULL DEFAULT 0.0,
`image_width` decimal(21,9) NOT NULL DEFAULT 0.0,
`align` varchar(140) DEFAULT 'Left',
`content` longtext,
`footer` longtext,
`footer_image` text,
`footer_image_height` decimal(21,9) NOT NULL DEFAULT 0.0,
`footer_image_width` decimal(21,9) NOT NULL DEFAULT 0.0,
`footer_align` varchar(140),
`header_script` longtext,
`footer_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_default`(`is_default`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,373 WARNING database DDL Query made to DB:
create table `tabPrint Format Field Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`field` varchar(140),
`template_file` varchar(140),
`module` varchar(140),
`standard` tinyint NOT NULL DEFAULT 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,644 WARNING database DDL Query made to DB:
create table `tabAddress Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`country` varchar(140) UNIQUE,
`is_default` tinyint NOT NULL DEFAULT 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,842 WARNING database DDL Query made to DB:
create table `tabAddress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`address_title` varchar(140),
`address_type` varchar(140),
`address_line1` varchar(240),
`address_line2` varchar(240),
`city` varchar(140),
`county` varchar(140),
`state` varchar(140),
`country` varchar(140),
`pincode` varchar(140),
`email_id` varchar(140),
`phone` varchar(140),
`fax` varchar(140),
`is_primary_address` tinyint NOT NULL DEFAULT 0,
`is_shipping_address` tinyint NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `city`(`city`),
index `country`(`country`),
index `pincode`(`pincode`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,925 WARNING database DDL Query made to DB:
create table `tabContact Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`phone` varchar(140),
`is_primary_phone` tinyint NOT NULL DEFAULT 0,
`is_primary_mobile_no` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:03,983 WARNING database DDL Query made to DB:
create table `tabSalutation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`salutation` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,044 WARNING database DDL Query made to DB:
create table `tabGender` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`gender` varchar(140) UNIQUE,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,201 WARNING database DDL Query made to DB:
create table `tabContact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email_id` varchar(140),
`user` varchar(140),
`address` varchar(140),
`sync_with_google_contacts` tinyint NOT NULL DEFAULT 0,
`status` varchar(140) DEFAULT 'Passive',
`salutation` varchar(140),
`designation` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`mobile_no` varchar(140),
`company_name` varchar(140),
`image` text,
`google_contacts` varchar(140),
`google_contacts_id` varchar(140),
`pulled_from_google_contacts` tinyint NOT NULL DEFAULT 0,
`is_primary_contact` tinyint NOT NULL DEFAULT 0,
`department` varchar(140),
`unsubscribed` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_id`(`email_id`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,282 WARNING database DDL Query made to DB:
create table `tabContact Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`email_id` varchar(140),
`is_primary` tinyint NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,418 WARNING database DDL Query made to DB:
create table `tabEnergy Point Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`enabled` tinyint NOT NULL DEFAULT 1,
`rule_name` varchar(140) UNIQUE,
`reference_doctype` varchar(140),
`for_doc_event` varchar(140) DEFAULT 'Custom',
`field_to_check` varchar(140),
`points` int NOT NULL DEFAULT 0,
`for_assigned_users` tinyint NOT NULL DEFAULT 0,
`user_field` varchar(140),
`multiplier_field` varchar(140),
`max_points` int NOT NULL DEFAULT 0,
`apply_only_once` tinyint NOT NULL DEFAULT 0,
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,735 WARNING database DDL Query made to DB:
create table `tabEnergy Point Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`type` varchar(140),
`points` int NOT NULL DEFAULT 0,
`rule` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reverted` tinyint NOT NULL DEFAULT 0,
`revert_of` varchar(140),
`reason` text,
`seen` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `reference_name`(`reference_name`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,884 WARNING database DDL Query made to DB:
create table `tabReview Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`level_name` varchar(140) UNIQUE,
`role` varchar(140) UNIQUE,
`review_points` int NOT NULL DEFAULT 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:04,953 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-04-22 15:33:05,263 WARNING database DDL Query made to DB:
create table `tabMilestone Tracker` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140) UNIQUE,
`track_field` varchar(140),
`disabled` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,357 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,449 WARNING database DDL Query made to DB:
create table `tabAssignment Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`document_type` varchar(140),
`due_date_based_on` varchar(140),
`priority` int NOT NULL DEFAULT 0,
`disabled` tinyint NOT NULL DEFAULT 0,
`description` text DEFAULT 'Automatic Assignment',
`assign_condition` longtext,
`unassign_condition` longtext,
`close_condition` longtext,
`rule` varchar(140),
`field` varchar(140),
`last_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,532 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,596 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,727 WARNING database DDL Query made to DB:
create table `tabAuto Repeat` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`submit_on_creation` tinyint NOT NULL DEFAULT 0,
`start_date` date,
`end_date` date,
`disabled` tinyint NOT NULL DEFAULT 0,
`frequency` varchar(140),
`repeat_on_day` int NOT NULL DEFAULT 0,
`repeat_on_last_day` tinyint NOT NULL DEFAULT 0,
`next_schedule_date` date,
`notify_by_email` tinyint NOT NULL DEFAULT 0,
`recipients` text,
`template` varchar(140),
`subject` varchar(140),
`message` text DEFAULT 'Please find attached {{ doc.doctype }} #{{ doc.name }}',
`print_format` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `next_schedule_date`(`next_schedule_date`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:33:05,793 WARNING database DDL Query made to DB:
create table `tabAssignment Rule Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:31,505 WARNING database DDL Query made to DB:
create table `tabPOS Closing Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`period_start_date` datetime(6),
`period_end_date` datetime(6),
`posting_date` date,
`posting_time` time(6),
`pos_opening_entry` varchar(140),
`status` varchar(140) DEFAULT 'Draft',
`company` varchar(140),
`pos_profile` varchar(140),
`user` varchar(140),
`grand_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_total` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_quantity` decimal(21,9) NOT NULL DEFAULT 0.0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:31,796 WARNING database DDL Query made to DB:
create table `tabUnreconcile Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`company` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:31,851 WARNING database DDL Query made to DB:
create table `tabCustomer Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`customer_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:31,917 WARNING database DDL Query made to DB:
create sequence if not exists bisect_nodes_id_seq nocache nocycle
2025-04-22 15:34:31,936 WARNING database DDL Query made to DB:
create table `tabBisect Nodes` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`root` varchar(140),
`left_child` varchar(140),
`right_child` varchar(140),
`period_from_date` datetime(6),
`period_to_date` datetime(6),
`difference` decimal(21,9) NOT NULL DEFAULT 0.0,
`balance_sheet_summary` decimal(21,9) NOT NULL DEFAULT 0.0,
`profit_loss_summary` decimal(21,9) NOT NULL DEFAULT 0.0,
`generated` tinyint NOT NULL DEFAULT 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,090 WARNING database DDL Query made to DB:
create table `tabGL Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`posting_date` date,
`transaction_date` date,
`fiscal_year` varchar(140),
`due_date` date,
`account` varchar(140),
`account_currency` varchar(140),
`against` text,
`party_type` varchar(140),
`party` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_subtype` text,
`transaction_currency` varchar(140),
`against_voucher_type` varchar(140),
`against_voucher` varchar(140),
`voucher_detail_no` varchar(140),
`transaction_exchange_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit` decimal(21,9) NOT NULL DEFAULT 0.0,
`debit_in_transaction_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_in_account_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit` decimal(21,9) NOT NULL DEFAULT 0.0,
`credit_in_transaction_currency` decimal(21,9) NOT NULL DEFAULT 0.0,
`cost_center` varchar(140),
`project` varchar(140),
`finance_book` varchar(140),
`company` varchar(140),
`is_opening` varchar(140),
`is_advance` varchar(140),
`to_rename` tinyint NOT NULL DEFAULT 1,
`is_cancelled` tinyint NOT NULL DEFAULT 0,
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_date`(`posting_date`),
index `account`(`account`),
index `party_type`(`party_type`),
index `party`(`party`),
index `voucher_no`(`voucher_no`),
index `against_voucher`(`against_voucher`),
index `voucher_detail_no`(`voucher_detail_no`),
index `cost_center`(`cost_center`),
index `company`(`company`),
index `to_rename`(`to_rename`),
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `voucher_type_voucher_no_index`(voucher_type, voucher_no)
2025-04-22 15:34:32,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `posting_date_company_index`(posting_date, company)
2025-04-22 15:34:32,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `party_type_party_index`(party_type, party)
2025-04-22 15:34:32,370 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`report` varchar(140),
`from_date` date,
`posting_date` date,
`company` varchar(140),
`account` varchar(140),
`group_by` varchar(140) DEFAULT 'Group by Voucher (Consolidated)',
`territory` varchar(140),
`ignore_exchange_rate_revaluation_journals` tinyint NOT NULL DEFAULT 0,
`ignore_cr_dr_notes` tinyint NOT NULL DEFAULT 0,
`to_date` date,
`finance_book` varchar(140),
`currency` varchar(140),
`payment_terms_template` varchar(140),
`sales_partner` varchar(140),
`sales_person` varchar(140),
`show_remarks` tinyint NOT NULL DEFAULT 0,
`based_on_payment_terms` tinyint NOT NULL DEFAULT 0,
`customer_collection` varchar(140),
`collection_name` varchar(140),
`primary_mandatory` tinyint NOT NULL DEFAULT 1,
`show_net_values_in_party_account` tinyint NOT NULL DEFAULT 0,
`orientation` varchar(140),
`include_break` tinyint NOT NULL DEFAULT 1,
`include_ageing` tinyint NOT NULL DEFAULT 0,
`ageing_based_on` varchar(140) DEFAULT 'Due Date',
`letter_head` varchar(140),
`terms_and_conditions` varchar(140),
`enable_auto_email` tinyint NOT NULL DEFAULT 0,
`sender` varchar(140),
`frequency` varchar(140),
`filter_duration` int NOT NULL DEFAULT 1,
`start_date` date,
`pdf_name` varchar(140),
`subject` varchar(140),
`body` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,508 WARNING database DDL Query made to DB:
create table `tabShipping Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`from_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`to_value` decimal(21,9) NOT NULL DEFAULT 0.0,
`shipping_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,558 WARNING database DDL Query made to DB:
create table `tabPricing Rule Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`pricing_rule` varchar(140),
`item_code` varchar(140),
`margin_type` varchar(140),
`rate_or_discount` varchar(140),
`child_docname` varchar(140),
`rule_applied` tinyint NOT NULL DEFAULT 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,606 WARNING database DDL Query made to DB:
create table `tabItem Tax Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`tax_type` varchar(140),
`tax_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,657 WARNING database DDL Query made to DB:
create table `tabSupplier Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`supplier` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,736 WARNING database DDL Query made to DB:
create table `tabPayment Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`payment_term_name` varchar(140) UNIQUE,
`invoice_portion` decimal(21,9) NOT NULL DEFAULT 0.0,
`mode_of_payment` varchar(140),
`due_date_based_on` varchar(140),
`credit_days` int NOT NULL DEFAULT 0,
`credit_months` int NOT NULL DEFAULT 0,
`discount_type` varchar(140) DEFAULT 'Percentage',
`discount` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_validity_based_on` varchar(140) DEFAULT 'Day(s) after invoice date',
`discount_validity` int NOT NULL DEFAULT 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index creation(creation))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,798 WARNING database DDL Query made to DB:
create table `tabSales Invoice Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`activity_type` varchar(140),
`description` text,
`from_time` datetime(6),
`to_time` datetime(6),
`billing_hours` decimal(21,9) NOT NULL DEFAULT 0.0,
`billing_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`time_sheet` varchar(140),
`timesheet_detail` varchar(140),
`project_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 15:34:32,967 WARNING database DDL Query made to DB:
create table `tabPurchase Invoice Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus tinyint not null default '0',
			idx int not null default '0',
			`item_code` varchar(140),
`product_bundle` varchar(140),
`item_name` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`received_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`rejected_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) NOT NULL DEFAULT 1.0,
`stock_uom` varchar(140),
`stock_qty` decimal(21,9) NOT NULL DEFAULT 0.0,
`price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_price_list_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_percentage` decimal(21,9) NOT NULL DEFAULT 0.0,
`discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`distributed_discount_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_rate_with_margin` decimal(21,9) NOT NULL DEFAULT 0.0,
`rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`is_free_item` tinyint NOT NULL DEFAULT 0,
`apply_tds` tinyint NOT NULL DEFAULT 1,
`net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`base_net_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`valuation_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`sales_incoming_rate` decimal(21,9) NOT NULL DEFAULT 0.0,
`item_tax_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`landed_cost_voucher_amount` decimal(21,9) NOT NULL DEFAULT 0.0,
`rm_supp_cost` decimal(21,9) NOT NULL DEFAULT 0.0,
`warehouse` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` tinyint NOT NULL DEFAULT 0,
`from_warehouse` varchar(140),
`quality_inspection` varchar(140),
`rejected_warehouse` varchar(140),
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`is_fixed_asset` tinyint NOT NULL DEFAULT 0,
`asset_location` varchar(140),
`asset_category` varchar(140),
`deferred_expense_account` varchar(140),
`service_stop_date` date,
`enable_deferred_expense` tinyint NOT NULL DEFAULT 0,
`service_start_date` date,
`service_end_date` date,
`allow_zero_valuation_rate` tinyint NOT NULL DEFAULT 0,
`item_tax_rate` longtext,
`bom` varchar(140),
`include_exploded_items` tinyint NOT NULL DEFAULT 0,
`purchase_invoice_item` varchar(140),
`purchase_order` varchar(140),
`po_detail` varchar(140),
`purchase_receipt` varchar(140),
`pr_detail` varchar(140),
`sales_invoice_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`weight_per_unit` decimal(21,9) NOT NULL DEFAULT 0.0,
`total_weight` decimal(21,9) NOT NULL DEFAULT 0.0,
`weight_uom` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`page_break` tinyint NOT NULL DEFAULT 0,
index `item_code`(`item_code`),
index `serial_and_batch_bundle`(`serial_and_batch_bundle`),
index `batch_no`(`batch_no`),
index `purchase_order`(`purchase_order`),
index `po_detail`(`po_detail`),
index `purchase_receipt`(`purchase_receipt`),
index `pr_detail`(`pr_detail`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `project`(`project`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
