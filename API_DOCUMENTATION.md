# API Documentation

This document provides comprehensive API documentation for the E-commerce application.

## 🔐 Authentication

All API endpoints require authentication unless marked as `allow_guest=True`.

### Authentication Methods

1. **Session-based Authentication** (Web)
   - Login via `/api/method/login`
   - Session cookie automatically handled

2. **Token-based Authentication** (Mobile/API)
   - Include `Authorization: Bearer <token>` header
   - Get token via login endpoint

### Login
```http
POST /api/method/login
Content-Type: application/json

{
  "usr": "<EMAIL>",
  "pwd": "password"
}
```

### Logout
```http
POST /api/method/logout
```

## 👤 User Management

### Register User
```http
POST /api/method/ecommerce.api.auth.register_user
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "password": "securepassword",
  "user_type": "Customer",
  "phone_number": "+255712345678",
  "company_name": "Optional Company Name"
}
```

### Get User Profile
```http
GET /api/method/ecommerce.api.user.get_user_profile?user_email=<EMAIL>
```

### Update Profile
```http
POST /api/method/ecommerce.api.user.update_profile
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+255712345678",
  "address_line_1": "123 Main Street",
  "city": "Dar es Salaam",
  "country": "Tanzania"
}
```

## 🛍 Product Management

### Get Products
```http
GET /api/resource/Product
```

**Query Parameters:**
- `filters`: JSON string of filters
- `fields`: JSON array of fields to return
- `limit_start`: Pagination start
- `limit_page_length`: Items per page
- `order_by`: Sort field and direction

**Example:**
```http
GET /api/resource/Product?filters={"is_active":1}&fields=["name","product_name","price","featured_image"]&limit_page_length=20
```

### Get Product Details
```http
GET /api/method/ecommerce.ecommerce.doctype.product.product.get_product_details?product_name=PROD-2025-00001
```

### Search Products
```http
GET /api/method/ecommerce.ecommerce.doctype.product.product.search_products?query=laptop&category=Electronics&min_price=100000&max_price=500000&limit=20
```

### Get Featured Products
```http
GET /api/method/ecommerce.ecommerce.doctype.product.product.get_featured_products?limit=8
```

## 📂 Categories

### Get Category Tree
```http
GET /api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_category_tree
```

### Get Featured Categories
```http
GET /api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_featured_categories
```

## 🛒 Shopping Cart

### Get Cart
```http
GET /api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.get_cart
```

### Add to Cart
```http
POST /api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.add_to_cart
Content-Type: application/json

{
  "product": "PROD-2025-00001",
  "quantity": 2
}
```

### Update Cart Quantity
```http
POST /api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.update_cart_quantity
Content-Type: application/json

{
  "product": "PROD-2025-00001",
  "quantity": 3
}
```

### Remove from Cart
```http
POST /api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.remove_from_cart
Content-Type: application/json

{
  "product": "PROD-2025-00001"
}
```

### Clear Cart
```http
POST /api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.clear_cart
```

## 📦 Order Management

### Create Order from Cart
```http
POST /api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.create_order_from_cart
Content-Type: application/json

{
  "shipping_address": "123 Main Street, Dar es Salaam",
  "billing_address": "123 Main Street, Dar es Salaam",
  "payment_method": "Mobile Money"
}
```

### Get Customer Orders
```http
GET /api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_customer_orders?limit=10
```

### Get Order Details
```http
GET /api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_order_details?order_name=ORD-2025-00001
```

### Update Order Status
```http
POST /api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.update_order_status
Content-Type: application/json

{
  "order_name": "ORD-2025-00001",
  "status": "Shipped",
  "notes": "Order shipped via DHL"
}
```

## 📍 Order Tracking

### Get Order Tracking
```http
GET /api/method/ecommerce.ecommerce.doctype.order_tracking.order_tracking.get_order_tracking?order=ORD-2025-00001
```

### Update Tracking Status
```http
POST /api/method/ecommerce.ecommerce.doctype.order_tracking.order_tracking.update_tracking_status
Content-Type: application/json

{
  "order": "ORD-2025-00001",
  "status": "In Transit",
  "location": "Dar es Salaam Hub",
  "notes": "Package sorted and dispatched",
  "latitude": -6.7924,
  "longitude": 39.2083
}
```

### Get Real-time Location
```http
GET /api/method/ecommerce.ecommerce.doctype.order_tracking.order_tracking.get_real_time_location?order=ORD-2025-00001
```

## ⭐ Reviews

### Get Product Reviews
```http
GET /api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_product_reviews?product=PROD-2025-00001&limit=10&offset=0
```

### Submit Review
```http
POST /api/method/ecommerce.ecommerce.doctype.product_review.product_review.submit_review
Content-Type: application/json

{
  "product": "PROD-2025-00001",
  "rating": 5,
  "title": "Excellent product!",
  "review_text": "Very satisfied with the quality and delivery.",
  "order_reference": "ORD-2025-00001"
}
```

### Get Review Summary
```http
GET /api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_review_summary?product=PROD-2025-00001
```

### Mark Review as Helpful
```http
POST /api/method/ecommerce.ecommerce.doctype.product_review.product_review.mark_review_helpful
Content-Type: application/json

{
  "review_id": "REV-2025-00001"
}
```

## 💬 Chat & Messaging

### Send Message
```http
POST /api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.send_message
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "message_text": "Hello, I have a question about this product",
  "message_type": "Text",
  "product_reference": "PROD-2025-00001"
}
```

### Get Chat Messages
```http
GET /api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_messages?chat_room=abc123&limit=50&offset=0
```

### Get Chat Rooms
```http
GET /api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_rooms
```

### Mark Messages as Read
```http
POST /api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.mark_messages_as_read
Content-Type: application/json

{
  "chat_room": "abc123"
}
```

### Get Unread Count
```http
GET /api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_unread_count
```

## 📊 Business Dashboard

### Get Dashboard
```http
GET /api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_dashboard?dashboard_name=user_default
```

### Refresh Dashboard
```http
POST /api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.refresh_dashboard
Content-Type: application/json

{
  "dashboard_name": "user_default"
}
```

### Get Real-time Metrics
```http
GET /api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_real_time_metrics
```

### Get Sales Analytics
```http
GET /api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_sales_analytics?period=month&user_type=Manager
```

## 📄 File Upload

### Upload File
```http
POST /api/method/upload_file
Content-Type: multipart/form-data

{
  "file": <file_data>,
  "doctype": "Product",
  "docname": "PROD-2025-00001",
  "fieldname": "featured_image"
}
```

## 🔍 Search

### Global Search
```http
GET /api/method/frappe.desk.search.search_link
?txt=laptop&doctype=Product&ignore_user_permissions=0
```

## 📱 Mobile API Endpoints

### Get App Configuration
```http
GET /api/method/ecommerce.api.mobile.get_app_config
```

### Sync Offline Data
```http
POST /api/method/ecommerce.api.mobile.sync_offline_data
Content-Type: application/json

{
  "offline_orders": [...],
  "offline_reviews": [...],
  "last_sync": "2025-01-01T00:00:00Z"
}
```

## 🚨 Error Handling

### Standard Error Response
```json
{
  "exc": "ValidationError: Product not found",
  "exc_type": "ValidationError",
  "_server_messages": "[\"Product not found\"]"
}
```

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## 📊 Rate Limiting

- **Authenticated users**: 1000 requests per hour
- **Guest users**: 100 requests per hour
- **File uploads**: 10 uploads per minute

## 🔒 Security Headers

All API responses include security headers:
```
X-Frame-Options: SAMEORIGIN
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000
```

## 📝 Request/Response Examples

### Successful Response
```json
{
  "message": {
    "status": "success",
    "data": {...}
  }
}
```

### Error Response
```json
{
  "exc": "ValidationError",
  "exc_type": "ValidationError",
  "_server_messages": "[\"Required field missing\"]"
}
```

## 🔄 Webhooks

### Order Status Change
```http
POST https://your-app.com/webhooks/order-status
Content-Type: application/json

{
  "event": "order.status_changed",
  "data": {
    "order_id": "ORD-2025-00001",
    "old_status": "Pending",
    "new_status": "Confirmed",
    "timestamp": "2025-01-01T12:00:00Z"
  }
}
```

### Payment Received
```http
POST https://your-app.com/webhooks/payment
Content-Type: application/json

{
  "event": "payment.received",
  "data": {
    "order_id": "ORD-2025-00001",
    "amount": 150000,
    "currency": "TZS",
    "payment_method": "Mobile Money",
    "transaction_id": "MP123456789"
  }
}
```

## 📚 SDK Examples

### JavaScript/Node.js
```javascript
const EcommerceAPI = require('ecommerce-api-sdk');

const api = new EcommerceAPI({
  baseURL: 'https://api.yourdomain.com',
  apiKey: 'your-api-key'
});

// Get products
const products = await api.products.list({
  category: 'Electronics',
  limit: 20
});

// Add to cart
await api.cart.addItem('PROD-2025-00001', 2);
```

### Python
```python
from ecommerce_api import EcommerceAPI

api = EcommerceAPI(
    base_url='https://api.yourdomain.com',
    api_key='your-api-key'
)

# Get products
products = api.products.list(category='Electronics', limit=20)

# Add to cart
api.cart.add_item('PROD-2025-00001', quantity=2)
```

This API documentation provides comprehensive coverage of all available endpoints with examples and proper error handling guidelines.
