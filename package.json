{"name": "ecommerce-frontend", "version": "1.0.0", "description": "E-commerce frontend with Next.js, Daisy UI, and Tailwind CSS", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "daisyui": "^4.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "axios": "^1.6.0", "react-query": "^3.39.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "socket.io-client": "^4.7.0", "js-cookie": "^3.0.0", "@types/js-cookie": "^3.0.0", "react-intersection-observer": "^9.5.0", "swiper": "^11.0.0", "react-rating-stars-component": "^2.2.0", "react-image-gallery": "^1.3.0", "react-select": "^5.8.0", "date-fns": "^2.30.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "react-map-gl": "^7.1.0", "mapbox-gl": "^2.15.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0"}}