.navbar-brand {
  padding: 0;
  color: var(--text-color) !important;

  img {
    height: 20px;
    max-width: fit-content;
  }

  @include media-breakpoint-down(md) {
    border-bottom: unset;
  }
}

.navbar-brand-container {
  width: 17rem;
  display: flex;
  align-items: center;
  padding: 10px 18px;
  background-color: var(--sidebar-bg-color);
  position: sticky;
  top: 0;
  z-index: 5;
  height: 60px;
  position: relative;

  @media (max-width: 768px) {
    width: 12rem;
  }

  @include media-breakpoint-down(md) {
    max-width: 14rem;
    background-color: var(--background-color);
  }
}

.navbar-nav {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: var(--background-color);
  padding: 0 14px;
  border-left: 1px solid var(--border-color);

  @include media-breakpoint-down(sm) {
    max-width: 100vw;
    height: auto;
    align-items: flex-start;
  }

  .search-item {
    margin-right: auto;
    height: auto !important;
  }

  .dropdown-menu {
    position: sticky;
    border: 1px solid var(--border-color);

    .dropdown-item {
      color: var(--text-color);

      &:focus-visible {
        outline: none;
      }
    }

    .dropdown-item:hover {
      background-color: var(--sidebar-hover-color);

      .h6 {
        color: var(--background-color) !important;
      }
    }
  }
}

.nav-item {
  margin-left: 1rem;
  display: flex;
  align-items: center;

  @include media-breakpoint-down(md) {
    // display:;
  }

  #search-container {
    padding-right: 0px;
    padding-left: 0px;

    .dropdown {
      height: 32px;
      width: 240px;
      background-color: var(--searchbar-color);

      &:hover {
        border-color: var(--primary);
      }

      kbd {
        position: absolute;
        top: 7px;
        right: 5px;
        padding: 0.1rem 0.4rem;
        color: var(--sidebar-text-color);
        background-color: transparent;
      }

      span {
        margin-left: 2rem;
        margin-right: 3rem;
      }
    }
  }

  select {
    height: 100%;
  }
}

.wiki-navbar {
  background-color: transparent;
  padding: 0px !important;
  border-bottom: 1px solid var(--border-color);

  @include media-breakpoint-down(md) {
    width: auto;
    padding-left: 2rem;
  }

  .wiki-navbar-container {
    padding-right: 1rem;
    align-items: center;
    background-color: var(--background-color);

    @include media-breakpoint-down(md) {
      box-shadow: unset;
      margin-left: 0;

      .navbar-nav {
        padding-left: 10px !important;
        max-width: 100vw;
      }
    }
  }

  .doc-container .navbar-collapse {
    padding-top: 2rem;
    background-color: var(--background-color);
    margin-left: 2rem;
    padding-bottom: 1rem;

    @include media-breakpoint-down(md) {
      padding-top: 0;
      margin: 0;
    }
  }

  .container {
    height: 36px;
  }

  .sun-moon-container {
    cursor: pointer;
    margin-left: 24px;
    display: flex;
    align-items: center;

    svg {
      width: 16px !important;
    }

    @include media-breakpoint-down(md) {
      margin-left: 0px;
    }
  }

  .mobile-search-icon {
    margin: 0 1rem 0 auto;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}

.navbar {
  .navbar-expand-lg {
    width: 100%;
    position: fixed;
    top: 0;
    /*ensure navbar stays affixes to the top*/
    left: 0;
    right: 0;
  }

  .navbar-link {
    color: var(--text-color);
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0;
    display: block;

    &:hover {
      color: var(--primary);
      text-decoration: none;
    }
  }

  .navbar-toggler {
    border-color: transparent;
    padding: 8px;

    &:focus {
      outline: unset;
    }
  }

  .logged-in {
    display: flex;
    align-items: center;
  }

  .logged-in .nav-avatar {
    padding: 0;
  }
}

@include media-breakpoint-down(md) {
  .navbar {
    position: inherit;
  }

  .nav-item {
    margin-left: 0.5rem;

    #search-container {
      margin: 1rem 0;
      width: 140%;
    }
  }
}
