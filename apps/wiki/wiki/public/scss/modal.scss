.modal {
  .modal-content {
    background-color: var(--background-color);
  }

  .modal-header {
    border-bottom: unset;

    .close {
      font-weight: 400;
    }
  }

  .modal-body {
    padding-top: 0;

    label {
      color: var(--text-color);
    }

    input {
      width: 100%;
      background: $gray-200;
      border-radius: 0.375rem;
      border: none;
      outline: none;
      padding: 0.25rem 0.5rem;
      font-size: 13px;
      line-height: 1.25rem;

      &[type="checkbox"] {
        color: #000;
        padding: 0%;
        border: 1px solid var(--gray-500);
        border-radius: 4px;
        accent-color: black;

        &:checked {
          background-color: var(--primary);
          background-image: url("data:image/svg+xml, <svg viewBox='0 0 8 7' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 4.00001L2.66667 5.80001L7 1.20001' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/></svg>"),
            var(--checkbox-gradient);
          background-size: 57%, 100%;
          box-shadow: none;
          border: none;
          background-repeat: no-repeat;
          background-position: center;
        }
      }
    }

    input,
    textarea {
      background-color: var(--searchbar-color);
      color: var(--text-color);
    }
  }

  .modal-footer {
    border-top: unset;
    justify-content: end;

    .btn {
      width: 100%;
    }
  }
}

.feedback-modal {
  width: 25rem;

  .form-control:focus {
    border: 1px solid var(--background-color);
  }

  .rating-options-buttons {
    display: grid;
    border-radius: 6px;
    overflow: hidden;
    border: 1.5px solid #000;
  }

  .rating-options-buttons > .ratings-number {
    border-right: 1px solid #000;

    &:last-child {
      border-right: none;
    }
  }

  .ratings-number {
    font-size: 15px;
    padding: 8px 0px;
    border: none;
    color: #000;
    background-color: #fff;

    &.rating-active {
      background-color: #000;
      color: #fff;
    }
  }

  .submit-feedback-btn.disabled {
    pointer-events: none;
  }
}

.search-dialog {
  .modal-content {
    background-color: var(--search-modal-bg-color) !important;
  }
}

.search-modal {
  padding: 10px;
  border-radius: 1rem;
  margin-top: 100px;

  .modal-header {
    padding: 0px;
    padding-left: 4px;
  }

  .search-icon {
    width: 16px;
  }

  input {
    color: var(--search-modal-color);
    margin-left: 4px;
    background: transparent;
    border: transparent;
  }

  input:focus {
    background: transparent;
    border: transparent;
  }

  .modal-body {
    padding: 0px;
  }

  .dropdown-border {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0px;
  }

  .dropdown-border {
    &:last-child {
      display: none;
    }
  }

  .search-dropdown-menu {
    max-height: 500px;
    overflow-x: hidden !important;
    overflow-y: auto !important;

    &:not(:empty) {
      margin-top: 18px;
    }

    .result-title {
      font-size: 14px;
      font-weight: 550;
    }

    .result-text {
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .match {
      font-weight: 750;
    }
  }

  .dropdown-item {
    padding: 8px !important;
    color: var(--search-modal-color);

    &:hover {
      background-color: var(--search-modal-hover-color);
    }
  }
}
