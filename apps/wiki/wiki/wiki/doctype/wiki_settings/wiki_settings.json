{"actions": [], "creation": "2020-09-26 16:02:57.409719", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["general_tab", "theme_section", "logo", "dark_mode_logo", "section_break_vefv", "default_wiki_space", "table_of_contents_section", "collapse_sidebar_groups", "enable_table_of_contents", "disable_guest_access", "navbar_tab", "navbar_column", "navbar", "app_switcher_list", "section_break_skhp", "search_column", "use_sqlite_for_search", "add_search_bar", "column_break_yaoi", "use_redisearch_for_search", "feedback_tab", "feedback_section", "enable_feedback", "feedback_submission_limit", "ask_for_contact_details", "section_break_mmtu", "javascript"], "fields": [{"fieldname": "logo", "fieldtype": "Attach Image", "label": "Light Mode Logo"}, {"fieldname": "javascript", "fieldtype": "Code", "label": "Javascript", "options": "Javascript"}, {"default": "1", "fieldname": "add_search_bar", "fieldtype": "Check", "label": "Add Search Bar"}, {"fieldname": "dark_mode_logo", "fieldtype": "Attach Image", "label": "Dark Mode Logo"}, {"fieldname": "navbar_tab", "fieldtype": "Tab Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "navbar_column", "fieldtype": "Column Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "navbar", "fieldtype": "Table", "label": "<PERSON><PERSON><PERSON>", "options": "Top Bar Item"}, {"fieldname": "section_break_mmtu", "fieldtype": "Section Break"}, {"fieldname": "general_tab", "fieldtype": "Tab Break", "label": "General"}, {"default": "Wiki", "fieldname": "default_wiki_space", "fieldtype": "Autocomplete", "label": "Default Wiki Space"}, {"fieldname": "theme_section", "fieldtype": "Section Break", "label": "Theme"}, {"fieldname": "section_break_vefv", "fieldtype": "Section Break", "label": "Wiki Space"}, {"fieldname": "table_of_contents_section", "fieldtype": "Section Break", "label": "Wiki Page Configurations"}, {"default": "1", "fieldname": "enable_table_of_contents", "fieldtype": "Check", "label": "Enable Table of Contents"}, {"fieldname": "section_break_skhp", "fieldtype": "Section Break", "label": "Search"}, {"fieldname": "search_column", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:doc.add_search_bar;", "description": "Use Redisearch instead of Frappe Web Search for faster, accurate results", "fieldname": "use_redisearch_for_search", "fieldtype": "Check", "label": "Use Redisearch for Search"}, {"default": "0", "fieldname": "collapse_sidebar_groups", "fieldtype": "Check", "label": "Collapse Sidebar Groups"}, {"fieldname": "feedback_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON>"}, {"default": "0", "description": "It will show a feedback widget on every Wiki Page when enabled. It will collect the rating, email and related info about the Wiki Page.", "fieldname": "enable_feedback", "fieldtype": "Check", "label": "Enable <PERSON>back"}, {"default": "0", "fieldname": "ask_for_contact_details", "fieldtype": "Check", "label": "Ask for contact details"}, {"fieldname": "feedback_tab", "fieldtype": "Tab Break", "label": "<PERSON><PERSON><PERSON>"}, {"default": "3", "depends_on": "enable_feedback", "description": "Hourly rate limit on submitting feedbacks", "fieldname": "feedback_submission_limit", "fieldtype": "Int", "label": "Feedback Submission Limit", "non_negative": 1}, {"fieldname": "app_switcher_list", "fieldtype": "Table", "label": "App Switcher List", "options": "Wiki App Switcher List Table"}, {"default": "0", "fieldname": "disable_guest_access", "fieldtype": "Check", "label": "Disable guest access"}, {"default": "1", "description": "Uses SQLite FTS for  searching. This takes precedence over Redis search if enabled.\n<br>\nSQLite based search is experimental.", "fieldname": "use_sqlite_for_search", "fieldtype": "Check", "label": "Use SQLite for Search"}, {"fieldname": "column_break_yaoi", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-06-16 13:13:21.868523", "modified_by": "Administrator", "module": "Wiki", "name": "Wiki Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Wiki Approver", "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}