{"actions": [], "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["product", "product_name", "quantity", "price", "tax_rate", "total"], "fields": [{"fieldname": "product", "fieldtype": "Link", "in_list_view": 1, "label": "Product", "options": "Product", "reqd": 1}, {"fieldname": "product_name", "fieldtype": "Data", "in_list_view": 1, "label": "Product Name", "read_only": 1}, {"fieldname": "quantity", "fieldtype": "Int", "in_list_view": 1, "label": "Quantity", "reqd": 1, "default": "1"}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "reqd": 1}, {"fieldname": "tax_rate", "fieldtype": "Percent", "label": "Tax Rate", "default": "0"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Shopping Cart Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}