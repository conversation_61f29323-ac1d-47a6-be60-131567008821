# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now
import hashlib


class ChatMessage(Document):
    def validate(self):
        """Validate the Chat Message document"""
        if not self.timestamp:
            self.timestamp = now()
        
        if not self.chat_room:
            self.chat_room = self.generate_chat_room_id()

    def generate_chat_room_id(self):
        """Generate unique chat room ID for sender and recipient"""
        users = sorted([self.sender, self.recipient])
        room_string = f"{users[0]}_{users[1]}"
        return hashlib.md5(room_string.encode()).hexdigest()[:16]

    def before_insert(self):
        """Actions before inserting the message"""
        # Send real-time notification
        self.send_real_time_notification()

    def send_real_time_notification(self):
        """Send real-time notification to recipient"""
        frappe.publish_realtime(
            event="new_message",
            message={
                "chat_room": self.chat_room,
                "sender": self.sender,
                "message_text": self.message_text,
                "timestamp": self.timestamp,
                "message_type": self.message_type
            },
            user=self.recipient
        )

    def mark_as_read(self):
        """Mark message as read"""
        self.is_read = 1
        self.read_timestamp = now()
        self.save()

    def delete_message(self, user):
        """Soft delete message"""
        self.is_deleted = 1
        self.deleted_by = user
        self.deleted_timestamp = now()
        self.save()


@frappe.whitelist()
def send_message(recipient, message_text, message_type="Text", attachment=None, product_reference=None, order_reference=None):
    """Send a chat message"""
    sender = frappe.session.user
    
    if sender == "Guest":
        frappe.throw("Please login to send messages")
    
    message = frappe.get_doc({
        "doctype": "Chat Message",
        "sender": sender,
        "recipient": recipient,
        "message_text": message_text,
        "message_type": message_type,
        "attachment": attachment,
        "product_reference": product_reference,
        "order_reference": order_reference
    })
    
    message.insert()
    
    return {
        "status": "success",
        "message": "Message sent successfully",
        "message_id": message.name,
        "chat_room": message.chat_room
    }


@frappe.whitelist()
def get_chat_messages(chat_room, limit=50, offset=0):
    """Get chat messages for a chat room"""
    user = frappe.session.user
    
    # Verify user is part of this chat room
    room_messages = frappe.get_all("Chat Message",
                                 filters={
                                     "chat_room": chat_room,
                                     "is_deleted": 0
                                 },
                                 fields=["sender", "recipient"],
                                 limit=1)
    
    if not room_messages:
        return []
    
    # Check if current user is sender or recipient
    if user not in [room_messages[0].sender, room_messages[0].recipient]:
        frappe.throw("Access denied")
    
    messages = frappe.get_all("Chat Message",
                            filters={
                                "chat_room": chat_room,
                                "is_deleted": 0
                            },
                            fields=[
                                "name", "sender", "recipient", "message_text", 
                                "message_type", "attachment", "product_reference",
                                "order_reference", "timestamp", "is_read"
                            ],
                            order_by="timestamp desc",
                            limit=limit,
                            start=offset)
    
    # Mark messages as read for current user
    for message in messages:
        if message.recipient == user and not message.is_read:
            frappe.db.set_value("Chat Message", message.name, "is_read", 1)
            frappe.db.set_value("Chat Message", message.name, "read_timestamp", now())
    
    return messages


@frappe.whitelist()
def get_chat_rooms(user=None):
    """Get all chat rooms for a user"""
    if not user:
        user = frappe.session.user
    
    # Get all unique chat rooms where user is sender or recipient
    chat_rooms = frappe.db.sql("""
        SELECT DISTINCT 
            chat_room,
            CASE 
                WHEN sender = %s THEN recipient 
                ELSE sender 
            END as other_user,
            MAX(timestamp) as last_message_time,
            COUNT(CASE WHEN recipient = %s AND is_read = 0 THEN 1 END) as unread_count
        FROM `tabChat Message`
        WHERE (sender = %s OR recipient = %s) AND is_deleted = 0
        GROUP BY chat_room
        ORDER BY last_message_time DESC
    """, (user, user, user, user), as_dict=True)
    
    # Get user details for each chat room
    for room in chat_rooms:
        other_user = frappe.get_doc("User", room.other_user)
        room["other_user_name"] = f"{other_user.first_name} {other_user.last_name or ''}".strip()
        room["other_user_image"] = other_user.user_image
        
        # Get last message
        last_message = frappe.get_all("Chat Message",
                                    filters={"chat_room": room.chat_room, "is_deleted": 0},
                                    fields=["message_text", "message_type"],
                                    order_by="timestamp desc",
                                    limit=1)
        
        if last_message:
            room["last_message"] = last_message[0].message_text
            room["last_message_type"] = last_message[0].message_type
    
    return chat_rooms


@frappe.whitelist()
def mark_messages_as_read(chat_room):
    """Mark all messages in a chat room as read for current user"""
    user = frappe.session.user
    
    frappe.db.sql("""
        UPDATE `tabChat Message`
        SET is_read = 1, read_timestamp = %s
        WHERE chat_room = %s AND recipient = %s AND is_read = 0
    """, (now(), chat_room, user))
    
    return {"status": "success", "message": "Messages marked as read"}


@frappe.whitelist()
def delete_message(message_id):
    """Delete a message"""
    user = frappe.session.user
    message = frappe.get_doc("Chat Message", message_id)
    
    # Only sender can delete their own messages
    if message.sender != user:
        frappe.throw("You can only delete your own messages")
    
    message.delete_message(user)
    
    return {"status": "success", "message": "Message deleted"}


@frappe.whitelist()
def get_unread_count(user=None):
    """Get total unread message count for user"""
    if not user:
        user = frappe.session.user
    
    count = frappe.db.count("Chat Message", {
        "recipient": user,
        "is_read": 0,
        "is_deleted": 0
    })
    
    return {"unread_count": count}


@frappe.whitelist()
def search_users_for_chat(query):
    """Search users to start a chat with"""
    current_user = frappe.session.user
    
    users = frappe.get_all("User",
                         filters={
                             "enabled": 1,
                             "name": ["!=", current_user],
                             "email": ["not like", "%@example.com"]
                         },
                         or_filters=[
                             {"first_name": ["like", f"%{query}%"]},
                             {"last_name": ["like", f"%{query}%"]},
                             {"email": ["like", f"%{query}%"]}
                         ],
                         fields=["name", "first_name", "last_name", "user_image"],
                         limit=10)
    
    for user in users:
        user["full_name"] = f"{user.first_name} {user.last_name or ''}".strip()
    
    return users
