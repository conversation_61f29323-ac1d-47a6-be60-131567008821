{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "chat_room", "sender", "recipient", "message_type", "column_break_6", "message_text", "attachment", "product_reference", "order_reference", "section_break_11", "timestamp", "is_read", "read_timestamp", "column_break_15", "is_deleted", "deleted_by", "deleted_timestamp"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "MSG-.YYYY.-.#####", "reqd": 1}, {"fieldname": "chat_room", "fieldtype": "Data", "in_list_view": 1, "label": "Chat Room", "reqd": 1}, {"fieldname": "sender", "fieldtype": "Link", "in_list_view": 1, "label": "Sender", "options": "User", "reqd": 1}, {"fieldname": "recipient", "fieldtype": "Link", "in_list_view": 1, "label": "Recipient", "options": "User", "reqd": 1}, {"fieldname": "message_type", "fieldtype": "Select", "label": "Message Type", "options": "Text\nImage\nFile\nProduct\nOrder\nLocation", "default": "Text"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "message_text", "fieldtype": "Text", "label": "Message Text"}, {"fieldname": "attachment", "fieldtype": "Attach", "label": "Attachment"}, {"fieldname": "product_reference", "fieldtype": "Link", "label": "Product Reference", "options": "Product"}, {"fieldname": "order_reference", "fieldtype": "Link", "label": "Order Reference", "options": "Ecommerce Order"}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Message Status"}, {"fieldname": "timestamp", "fieldtype": "Datetime", "in_list_view": 1, "label": "Timestamp", "reqd": 1}, {"fieldname": "is_read", "fieldtype": "Check", "label": "<PERSON>", "default": "0"}, {"fieldname": "read_timestamp", "fieldtype": "Datetime", "label": "Read Timestamp"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "is_deleted", "fieldtype": "Check", "label": "Is Deleted", "default": "0"}, {"fieldname": "deleted_by", "fieldtype": "Link", "label": "Deleted By", "options": "User"}, {"fieldname": "deleted_timestamp", "fieldtype": "Datetime", "label": "Deleted Timestamp"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Chat Message", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}