# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt, cint, nowdate, add_days
from frappe.model.naming import make_autoname


class EcommerceOrder(Document):
    def validate(self):
        """Validate the Ecommerce Order document"""
        self.calculate_totals()
        self.validate_stock_availability()
        self.set_outstanding_amount()
        self.calculate_loyalty_points()

    def calculate_totals(self):
        """Calculate order totals"""
        self.subtotal = 0.0
        self.tax_amount = 0.0
        
        for item in self.order_items:
            item_total = flt(item.price) * cint(item.quantity)
            item.total = item_total
            self.subtotal += item_total
            
            # Calculate tax for item
            if item.tax_rate:
                item_tax = item_total * flt(item.tax_rate) / 100
                self.tax_amount += item_tax

        # Calculate total amount
        self.total_amount = flt(self.subtotal) + flt(self.tax_amount) + flt(self.shipping_amount) - flt(self.discount_amount)

    def validate_stock_availability(self):
        """Validate stock availability for all order items"""
        for item in self.order_items:
            product = frappe.get_doc("Product", item.product)
            if product.track_inventory and not product.is_in_stock(item.quantity):
                frappe.throw(f"Insufficient stock for {item.product_name}. Available: {product.stock_quantity}, Required: {item.quantity}")

    def set_outstanding_amount(self):
        """Set outstanding amount"""
        self.outstanding_amount = flt(self.total_amount) - flt(self.paid_amount)

    def calculate_loyalty_points(self):
        """Calculate loyalty points earned from this order"""
        # 1 point per 100 currency units spent
        self.loyalty_points_earned = int(flt(self.total_amount) / 100)

    def on_submit(self):
        """Actions to perform when order is submitted"""
        self.update_product_stock()
        self.create_sales_invoice()
        self.update_customer_statistics()
        self.award_loyalty_points()

    def update_product_stock(self):
        """Update product stock quantities"""
        for item in self.order_items:
            product = frappe.get_doc("Product", item.product)
            if product.track_inventory:
                product.update_stock(-item.quantity)

    def create_sales_invoice(self):
        """Create Sales Invoice in ERPNext"""
        # Get customer from ecommerce user
        customer_email = self.customer
        customer = frappe.db.get_value("Customer", {"email_id": customer_email}, "name")
        
        if not customer:
            # Create customer if doesn't exist
            user_doc = frappe.get_doc("User", customer_email)
            customer_doc = frappe.get_doc({
                "doctype": "Customer",
                "customer_name": f"{user_doc.first_name} {user_doc.last_name or ''}".strip(),
                "customer_type": "Individual",
                "customer_group": "Individual",
                "territory": "All Territories",
                "email_id": customer_email
            })
            customer_doc.insert(ignore_permissions=True)
            customer = customer_doc.name

        # Create Sales Invoice
        sales_invoice = frappe.get_doc({
            "doctype": "Sales Invoice",
            "customer": customer,
            "posting_date": nowdate(),
            "due_date": add_days(nowdate(), 30),
            "currency": self.currency,
            "conversion_rate": self.exchange_rate or 1.0,
            "ecommerce_order": self.name
        })

        # Add items to sales invoice
        for item in self.order_items:
            item_code = frappe.db.get_value("Product", item.product, "product_code") or item.product
            
            sales_invoice.append("items", {
                "item_code": item_code,
                "item_name": item.product_name,
                "qty": item.quantity,
                "rate": item.price,
                "amount": item.total
            })

        # Add taxes if applicable
        if self.tax_amount > 0:
            sales_invoice.append("taxes", {
                "charge_type": "Actual",
                "account_head": frappe.db.get_single_value("Accounts Settings", "default_tax_account"),
                "description": "Tax",
                "tax_amount": self.tax_amount
            })

        sales_invoice.insert(ignore_permissions=True)
        
        # Submit if payment is completed
        if self.payment_status == "Paid":
            sales_invoice.submit()

        return sales_invoice.name

    def update_customer_statistics(self):
        """Update customer order statistics"""
        ecommerce_user = frappe.db.get_value("Ecommerce User", {"user": self.customer}, "name")
        if ecommerce_user:
            user_doc = frappe.get_doc("Ecommerce User", ecommerce_user)
            user_doc.update_order_statistics(self.total_amount)

    def award_loyalty_points(self):
        """Award loyalty points to customer"""
        if self.loyalty_points_earned > 0:
            ecommerce_user = frappe.db.get_value("Ecommerce User", {"user": self.customer}, "name")
            if ecommerce_user:
                user_doc = frappe.get_doc("Ecommerce User", ecommerce_user)
                user_doc.add_loyalty_points(self.loyalty_points_earned)

    def update_status(self, new_status):
        """Update order status"""
        old_status = self.status
        self.status = new_status
        self.save()
        
        # Create status change log
        self.add_status_change_log(old_status, new_status)
        
        # Send notification to customer
        self.send_status_notification(new_status)

    def add_status_change_log(self, old_status, new_status):
        """Add status change to order tracking"""
        tracking = frappe.get_doc({
            "doctype": "Order Tracking",
            "order": self.name,
            "status": new_status,
            "status_date": frappe.utils.now(),
            "notes": f"Status changed from {old_status} to {new_status}"
        })
        tracking.insert(ignore_permissions=True)

    def send_status_notification(self, status):
        """Send status notification to customer"""
        # Email notification
        frappe.sendmail(
            recipients=[self.customer],
            subject=f"Order {self.name} - Status Update",
            message=f"Your order status has been updated to: {status}",
            reference_doctype=self.doctype,
            reference_name=self.name
        )

    def process_payment(self, payment_amount, payment_method, transaction_id=None):
        """Process payment for the order"""
        self.paid_amount = flt(self.paid_amount) + flt(payment_amount)
        self.payment_method = payment_method
        
        if flt(self.paid_amount) >= flt(self.total_amount):
            self.payment_status = "Paid"
            self.status = "Confirmed"
        elif flt(self.paid_amount) > 0:
            self.payment_status = "Partially Paid"
        
        self.set_outstanding_amount()
        self.save()
        
        # Create payment entry in ERPNext
        self.create_payment_entry(payment_amount, payment_method, transaction_id)

    def create_payment_entry(self, payment_amount, payment_method, transaction_id):
        """Create payment entry in ERPNext"""
        customer_email = self.customer
        customer = frappe.db.get_value("Customer", {"email_id": customer_email}, "name")
        
        if customer:
            payment_entry = frappe.get_doc({
                "doctype": "Payment Entry",
                "payment_type": "Receive",
                "party_type": "Customer",
                "party": customer,
                "paid_amount": payment_amount,
                "received_amount": payment_amount,
                "reference_no": transaction_id,
                "reference_date": nowdate(),
                "mode_of_payment": payment_method,
                "ecommerce_order": self.name
            })
            payment_entry.insert(ignore_permissions=True)
            payment_entry.submit()

    def cancel_order(self, reason=None):
        """Cancel the order"""
        if self.status in ["Delivered", "Cancelled"]:
            frappe.throw("Cannot cancel order in current status")
        
        # Restore stock
        for item in self.order_items:
            product = frappe.get_doc("Product", item.product)
            if product.track_inventory:
                product.update_stock(item.quantity)
        
        self.status = "Cancelled"
        if reason:
            self.internal_notes = f"Cancelled: {reason}"
        
        self.save()
        self.send_status_notification("Cancelled")

    def get_order_timeline(self):
        """Get order status timeline"""
        return frappe.get_all("Order Tracking",
                            filters={"order": self.name},
                            fields=["status", "status_date", "notes"],
                            order_by="status_date")


@frappe.whitelist()
def create_order_from_cart(user=None, shipping_address=None, billing_address=None, payment_method=None):
    """Create order from shopping cart"""
    if not user:
        user = frappe.session.user
    
    # Get user's cart
    cart = frappe.db.get_value("Shopping Cart", user, "name")
    if not cart:
        frappe.throw("No items in cart")
    
    cart_doc = frappe.get_doc("Shopping Cart", cart)
    
    # Convert cart to order
    order_name = cart_doc.convert_to_order()
    order = frappe.get_doc("Ecommerce Order", order_name)
    
    # Update addresses and payment method
    if shipping_address:
        order.shipping_address = shipping_address
    if billing_address:
        order.billing_address = billing_address
    if payment_method:
        order.payment_method = payment_method
    
    order.save()
    
    return order.name


@frappe.whitelist()
def get_order_details(order_name):
    """Get detailed order information"""
    order = frappe.get_doc("Ecommerce Order", order_name)
    timeline = order.get_order_timeline()
    
    return {
        "order": order,
        "timeline": timeline
    }


@frappe.whitelist()
def update_order_status(order_name, status, notes=None):
    """Update order status"""
    if not frappe.has_permission("Ecommerce Order", "write"):
        frappe.throw("Insufficient permissions")
    
    order = frappe.get_doc("Ecommerce Order", order_name)
    order.update_status(status)
    
    if notes:
        order.internal_notes = notes
        order.save()
    
    return {"status": "success", "message": f"Order status updated to {status}"}


@frappe.whitelist()
def get_customer_orders(customer=None, limit=10):
    """Get customer order history"""
    if not customer:
        customer = frappe.session.user
    
    return frappe.get_all("Ecommerce Order",
                         filters={"customer": customer},
                         fields=["name", "order_date", "status", "total_amount", "currency"],
                         order_by="order_date desc",
                         limit=limit)
