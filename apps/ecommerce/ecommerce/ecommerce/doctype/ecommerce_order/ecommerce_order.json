{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "customer", "order_date", "status", "column_break_5", "payment_status", "payment_method", "delivery_status", "section_break_9", "order_items", "section_break_11", "subtotal", "tax_amount", "shipping_amount", "discount_amount", "total_amount", "column_break_17", "currency", "exchange_rate", "paid_amount", "outstanding_amount", "section_break_22", "billing_address", "shipping_address", "column_break_25", "contact_phone", "contact_email", "special_instructions", "section_break_29", "coupon_code", "discount_reason", "loyalty_points_used", "loyalty_points_earned", "column_break_34", "estimated_delivery_date", "actual_delivery_date", "tracking_number", "section_break_38", "notes", "internal_notes"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "ORD-.YYYY.-.#####", "reqd": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "User", "reqd": 1}, {"fieldname": "order_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Order Date", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Pending\nConfirmed\nProcessing\nShipped\nDelivered\nCancelled\nReturned", "reqd": 1, "default": "Pending"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "payment_status", "fieldtype": "Select", "in_list_view": 1, "label": "Payment Status", "options": "Pending\nPaid\nPartially Paid\nRefunded\nFailed", "default": "Pending"}, {"fieldname": "payment_method", "fieldtype": "Select", "label": "Payment Method", "options": "Cash on Delivery\nMobile Money\nCredit Card\nBank Transfer\nPayPal\nStripe"}, {"fieldname": "delivery_status", "fieldtype": "Select", "label": "Delivery Status", "options": "Pending\nPreparing\nIn Transit\nOut for Delivery\nDelivered\nFailed Delivery", "default": "Pending"}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Order Items"}, {"fieldname": "order_items", "fieldtype": "Table", "label": "Order Items", "options": "Ecommerce Order Item", "reqd": 1}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Order Summary"}, {"fieldname": "subtotal", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Subtotal", "read_only": 1}, {"fieldname": "tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tax Amount", "read_only": 1}, {"fieldname": "shipping_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipping Amount"}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total Amount", "read_only": 1}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "default": "TZS"}, {"fieldname": "exchange_rate", "fieldtype": "Float", "label": "Exchange Rate", "default": "1.0"}, {"fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "default": "0.0"}, {"fieldname": "outstanding_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Outstanding Amount", "read_only": 1}, {"fieldname": "section_break_22", "fieldtype": "Section Break", "label": "Address & Contact"}, {"fieldname": "billing_address", "fieldtype": "Text", "label": "Billing Address"}, {"fieldname": "shipping_address", "fieldtype": "Text", "label": "Shipping Address"}, {"fieldname": "column_break_25", "fieldtype": "Column Break"}, {"fieldname": "contact_phone", "fieldtype": "Data", "label": "Contact Phone"}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email"}, {"fieldname": "special_instructions", "fieldtype": "Text", "label": "Special Instructions"}, {"fieldname": "section_break_29", "fieldtype": "Section Break", "label": "Discounts & Loyalty"}, {"fieldname": "coupon_code", "fieldtype": "Data", "label": "Coupon Code"}, {"fieldname": "discount_reason", "fieldtype": "Data", "label": "Discount Reason"}, {"fieldname": "loyalty_points_used", "fieldtype": "Int", "label": "Loyalty Points Used", "default": "0"}, {"fieldname": "loyalty_points_earned", "fieldtype": "Int", "label": "Loyalty Points Earned", "default": "0"}, {"fieldname": "column_break_34", "fieldtype": "Column Break"}, {"fieldname": "estimated_delivery_date", "fieldtype": "Date", "label": "Estimated Delivery Date"}, {"fieldname": "actual_delivery_date", "fieldtype": "Date", "label": "Actual Delivery Date"}, {"fieldname": "tracking_number", "fieldtype": "Data", "label": "Tracking Number"}, {"fieldname": "section_break_38", "fieldtype": "Section Break", "label": "Notes"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Customer Notes"}, {"fieldname": "internal_notes", "fieldtype": "Text", "label": "Internal Notes"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Ecommerce Order", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}