# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now
import json


class OrderTracking(Document):
    def validate(self):
        """Validate the Order Tracking document"""
        if not self.status_date:
            self.status_date = now()
        
        if not self.updated_by:
            self.updated_by = frappe.session.user

    def before_insert(self):
        """Actions before inserting the document"""
        # Update order status
        order = frappe.get_doc("Ecommerce Order", self.order)
        if order.status != self.status:
            order.status = self.status
            if self.status == "Delivered":
                order.delivery_status = "Delivered"
                order.actual_delivery_date = self.status_date
            order.save(ignore_permissions=True)

    def set_gps_location(self, latitude, longitude):
        """Set GPS coordinates"""
        self.gps_coordinates = json.dumps({
            "latitude": latitude,
            "longitude": longitude,
            "timestamp": now()
        })

    def get_gps_location(self):
        """Get GPS coordinates"""
        if self.gps_coordinates:
            try:
                coords = json.loads(self.gps_coordinates) if isinstance(self.gps_coordinates, str) else self.gps_coordinates
                return coords.get("latitude"), coords.get("longitude")
            except:
                return None, None
        return None, None


@frappe.whitelist()
def update_tracking_status(order, status, location=None, notes=None, latitude=None, longitude=None):
    """Update order tracking status"""
    tracking = frappe.get_doc({
        "doctype": "Order Tracking",
        "order": order,
        "status": status,
        "status_date": now(),
        "location": location,
        "notes": notes,
        "updated_by": frappe.session.user
    })
    
    if latitude and longitude:
        tracking.set_gps_location(latitude, longitude)
    
    tracking.insert()
    
    return {
        "status": "success",
        "message": f"Order tracking updated to {status}",
        "tracking_id": tracking.name
    }


@frappe.whitelist()
def get_order_tracking(order):
    """Get order tracking history"""
    return frappe.get_all("Order Tracking",
                         filters={"order": order},
                         fields=["name", "status", "status_date", "location", "notes", "gps_coordinates"],
                         order_by="status_date")


@frappe.whitelist()
def get_real_time_location(order):
    """Get real-time location of order"""
    latest_tracking = frappe.get_all("Order Tracking",
                                   filters={"order": order},
                                   fields=["gps_coordinates", "status", "status_date", "location"],
                                   order_by="status_date desc",
                                   limit=1)
    
    if latest_tracking:
        tracking = latest_tracking[0]
        if tracking.gps_coordinates:
            try:
                coords = json.loads(tracking.gps_coordinates) if isinstance(tracking.gps_coordinates, str) else tracking.gps_coordinates
                return {
                    "latitude": coords.get("latitude"),
                    "longitude": coords.get("longitude"),
                    "timestamp": coords.get("timestamp"),
                    "status": tracking.status,
                    "location": tracking.location
                }
            except:
                pass
    
    return None
