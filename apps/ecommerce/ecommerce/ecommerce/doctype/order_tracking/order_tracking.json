{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "order", "status", "status_date", "column_break_5", "location", "estimated_time", "actual_time", "section_break_9", "notes", "updated_by", "gps_coordinates"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "TRK-.YYYY.-.#####", "reqd": 1}, {"fieldname": "order", "fieldtype": "Link", "in_list_view": 1, "label": "Order", "options": "Ecommerce Order", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Order Placed\nOrder Confirmed\nPreparing\nPicked Up\nIn Transit\nOut for Delivery\nDelivered\nException\nCancelled", "reqd": 1}, {"fieldname": "status_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Status Date", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "location", "fieldtype": "Data", "label": "Location"}, {"fieldname": "estimated_time", "fieldtype": "Datetime", "label": "Estimated Time"}, {"fieldname": "actual_time", "fieldtype": "Datetime", "label": "Actual Time"}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Details"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "updated_by", "fieldtype": "Link", "label": "Updated By", "options": "User", "read_only": 1}, {"fieldname": "gps_coordinates", "fieldtype": "JSON", "label": "GPS Coordinates", "description": "Latitude and longitude for real-time tracking"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Order Tracking", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "Delivery Person", "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}