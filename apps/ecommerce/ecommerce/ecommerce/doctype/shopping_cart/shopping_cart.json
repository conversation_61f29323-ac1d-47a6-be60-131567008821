{"actions": [], "allow_rename": 1, "autoname": "field:user", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["user", "session_id", "section_break_3", "cart_items", "section_break_5", "total_items", "subtotal", "tax_amount", "shipping_amount", "discount_amount", "total_amount", "column_break_12", "currency", "last_updated", "expires_on"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "unique": 1}, {"fieldname": "session_id", "fieldtype": "Data", "label": "Session ID", "description": "For guest users"}, {"fieldname": "section_break_3", "fieldtype": "Section Break", "label": "Cart Items"}, {"fieldname": "cart_items", "fieldtype": "Table", "label": "Cart Items", "options": "Shopping Cart Item", "reqd": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "<PERSON>t <PERSON>mma<PERSON>"}, {"fieldname": "total_items", "fieldtype": "Int", "label": "Total Items", "read_only": 1, "default": "0"}, {"fieldname": "subtotal", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Subtotal", "read_only": 1, "default": "0.0"}, {"fieldname": "tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tax Amount", "read_only": 1, "default": "0.0"}, {"fieldname": "shipping_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipping Amount", "default": "0.0"}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Discount Amount", "default": "0.0"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total Amount", "read_only": 1, "default": "0.0"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "default": "TZS"}, {"fieldname": "last_updated", "fieldtype": "Datetime", "label": "Last Updated", "read_only": 1}, {"fieldname": "expires_on", "fieldtype": "Datetime", "label": "Expires On", "description": "Cart expiration date for guest users"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Shopping Cart", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}