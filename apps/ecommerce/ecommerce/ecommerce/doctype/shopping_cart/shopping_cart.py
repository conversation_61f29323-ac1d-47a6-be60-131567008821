# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt, cint, now, add_days
import json


class ShoppingCart(Document):
    def validate(self):
        """Validate the Shopping Cart document"""
        self.calculate_totals()
        self.set_expiry()

    def calculate_totals(self):
        """Calculate cart totals"""
        self.total_items = 0
        self.subtotal = 0.0
        
        for item in self.cart_items:
            self.total_items += cint(item.quantity)
            item_total = flt(item.price) * cint(item.quantity)
            self.subtotal += item_total
            item.total = item_total
        
        # Calculate tax (if applicable)
        self.tax_amount = self.calculate_tax()
        
        # Calculate total
        self.total_amount = flt(self.subtotal) + flt(self.tax_amount) + flt(self.shipping_amount) - flt(self.discount_amount)

    def calculate_tax(self):
        """Calculate tax amount based on items"""
        tax_amount = 0.0
        for item in self.cart_items:
            if item.tax_rate:
                item_tax = flt(item.price) * cint(item.quantity) * flt(item.tax_rate) / 100
                tax_amount += item_tax
        return tax_amount

    def set_expiry(self):
        """Set cart expiry for guest users"""
        if not self.user or self.user == "Guest":
            if not self.expires_on:
                self.expires_on = add_days(now(), 7)  # 7 days expiry for guest carts

    def before_save(self):
        """Actions before saving"""
        self.last_updated = now()

    def add_item(self, product, quantity=1, price=None):
        """Add item to cart"""
        # Check if item already exists
        existing_item = None
        for item in self.cart_items:
            if item.product == product:
                existing_item = item
                break
        
        if existing_item:
            # Update quantity
            existing_item.quantity = cint(existing_item.quantity) + cint(quantity)
        else:
            # Add new item
            product_doc = frappe.get_doc("Product", product)
            item_price = price or product_doc.get_final_price()
            
            self.append("cart_items", {
                "product": product,
                "product_name": product_doc.product_name,
                "quantity": quantity,
                "price": item_price,
                "tax_rate": self.get_product_tax_rate(product_doc)
            })
        
        self.save()

    def remove_item(self, product):
        """Remove item from cart"""
        for i, item in enumerate(self.cart_items):
            if item.product == product:
                self.cart_items.pop(i)
                break
        self.save()

    def update_item_quantity(self, product, quantity):
        """Update item quantity"""
        for item in self.cart_items:
            if item.product == product:
                if cint(quantity) <= 0:
                    self.remove_item(product)
                else:
                    item.quantity = quantity
                break
        self.save()

    def clear_cart(self):
        """Clear all items from cart"""
        self.cart_items = []
        self.save()

    def get_product_tax_rate(self, product_doc):
        """Get tax rate for product"""
        if product_doc.tax_category:
            # Get tax rate from tax category
            tax_rate = frappe.db.get_value("Tax Category", product_doc.tax_category, "tax_rate")
            return tax_rate or 0.0
        return 0.0

    def apply_coupon(self, coupon_code):
        """Apply coupon code"""
        coupon = frappe.get_doc("Coupon", coupon_code)
        if coupon.is_valid():
            if coupon.discount_type == "Percentage":
                self.discount_amount = flt(self.subtotal) * flt(coupon.discount_value) / 100
            else:
                self.discount_amount = flt(coupon.discount_value)
            
            self.coupon_code = coupon_code
            self.save()
            return True
        return False

    def get_cart_summary(self):
        """Get cart summary for display"""
        return {
            "total_items": self.total_items,
            "subtotal": self.subtotal,
            "tax_amount": self.tax_amount,
            "shipping_amount": self.shipping_amount,
            "discount_amount": self.discount_amount,
            "total_amount": self.total_amount,
            "currency": self.currency
        }

    def validate_stock(self):
        """Validate stock availability for all items"""
        out_of_stock_items = []
        
        for item in self.cart_items:
            product_doc = frappe.get_doc("Product", item.product)
            if not product_doc.is_in_stock(item.quantity):
                out_of_stock_items.append({
                    "product": item.product,
                    "product_name": item.product_name,
                    "requested_quantity": item.quantity,
                    "available_quantity": product_doc.stock_quantity
                })
        
        return out_of_stock_items

    def convert_to_order(self):
        """Convert cart to order"""
        # Validate stock before conversion
        out_of_stock = self.validate_stock()
        if out_of_stock:
            frappe.throw(f"Some items are out of stock: {', '.join([item['product_name'] for item in out_of_stock])}")
        
        # Create order
        order = frappe.get_doc({
            "doctype": "Ecommerce Order",
            "customer": self.user,
            "order_date": now(),
            "status": "Pending",
            "subtotal": self.subtotal,
            "tax_amount": self.tax_amount,
            "shipping_amount": self.shipping_amount,
            "discount_amount": self.discount_amount,
            "total_amount": self.total_amount,
            "currency": self.currency
        })
        
        # Add order items
        for cart_item in self.cart_items:
            order.append("order_items", {
                "product": cart_item.product,
                "product_name": cart_item.product_name,
                "quantity": cart_item.quantity,
                "price": cart_item.price,
                "total": cart_item.total
            })
        
        order.insert()
        
        # Clear cart after successful order creation
        self.clear_cart()
        
        return order.name


@frappe.whitelist()
def get_cart(user=None):
    """Get shopping cart for user"""
    if not user:
        user = frappe.session.user
    
    cart = frappe.db.exists("Shopping Cart", user)
    if cart:
        return frappe.get_doc("Shopping Cart", cart)
    else:
        # Create new cart
        cart = frappe.get_doc({
            "doctype": "Shopping Cart",
            "user": user,
            "currency": "TZS"
        })
        cart.insert()
        return cart


@frappe.whitelist()
def add_to_cart(product, quantity=1, user=None):
    """Add product to cart"""
    if not user:
        user = frappe.session.user
    
    cart = get_cart(user)
    cart.add_item(product, quantity)
    
    return {
        "status": "success",
        "message": "Item added to cart",
        "cart_summary": cart.get_cart_summary()
    }


@frappe.whitelist()
def remove_from_cart(product, user=None):
    """Remove product from cart"""
    if not user:
        user = frappe.session.user
    
    cart = get_cart(user)
    cart.remove_item(product)
    
    return {
        "status": "success",
        "message": "Item removed from cart",
        "cart_summary": cart.get_cart_summary()
    }


@frappe.whitelist()
def update_cart_quantity(product, quantity, user=None):
    """Update cart item quantity"""
    if not user:
        user = frappe.session.user
    
    cart = get_cart(user)
    cart.update_item_quantity(product, quantity)
    
    return {
        "status": "success",
        "message": "Cart updated",
        "cart_summary": cart.get_cart_summary()
    }


@frappe.whitelist()
def clear_cart(user=None):
    """Clear shopping cart"""
    if not user:
        user = frappe.session.user
    
    cart = get_cart(user)
    cart.clear_cart()
    
    return {
        "status": "success",
        "message": "Cart cleared"
    }
