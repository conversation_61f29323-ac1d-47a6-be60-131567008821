{"actions": [], "allow_rename": 1, "autoname": "field:dashboard_name", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["dashboard_name", "user", "user_type", "section_break_4", "date_range", "from_date", "to_date", "section_break_8", "total_revenue", "total_orders", "total_customers", "average_order_value", "column_break_13", "conversion_rate", "customer_acquisition_cost", "customer_lifetime_value", "return_rate", "section_break_18", "top_products", "top_categories", "sales_by_region", "section_break_22", "revenue_trend", "order_trend", "customer_trend", "section_break_26", "last_updated", "auto_refresh", "refresh_interval"], "fields": [{"fieldname": "dashboard_name", "fieldtype": "Data", "in_list_view": 1, "label": "Dashboard Name", "reqd": 1, "unique": 1}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1}, {"fieldname": "user_type", "fieldtype": "Select", "label": "User Type", "options": "Admin\nManager\n<PERSON><PERSON>\nCustomer", "reqd": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break", "label": "Date Range"}, {"fieldname": "date_range", "fieldtype": "Select", "label": "Date Range", "options": "Today\nYesterday\nThis Week\nLast Week\nThis Month\nLast Month\nThis Quarter\nLast Quarter\nThis Year\nLast Year\nCustom", "default": "This Month"}, {"fieldname": "from_date", "fieldtype": "Date", "label": "From Date"}, {"fieldname": "to_date", "fieldtype": "Date", "label": "To Date"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Key Metrics"}, {"fieldname": "total_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Revenue", "read_only": 1}, {"fieldname": "total_orders", "fieldtype": "Int", "label": "Total Orders", "read_only": 1}, {"fieldname": "total_customers", "fieldtype": "Int", "label": "Total Customers", "read_only": 1}, {"fieldname": "average_order_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Average Order Value", "read_only": 1}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "conversion_rate", "fieldtype": "Percent", "label": "Conversion Rate", "read_only": 1}, {"fieldname": "customer_acquisition_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Customer Acquisition Cost", "read_only": 1}, {"fieldname": "customer_lifetime_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Customer Lifetime Value", "read_only": 1}, {"fieldname": "return_rate", "fieldtype": "Percent", "label": "Return Rate", "read_only": 1}, {"fieldname": "section_break_18", "fieldtype": "Section Break", "label": "Top Performers"}, {"fieldname": "top_products", "fieldtype": "JSON", "label": "Top Products", "read_only": 1}, {"fieldname": "top_categories", "fieldtype": "JSON", "label": "Top Categories", "read_only": 1}, {"fieldname": "sales_by_region", "fieldtype": "JSON", "label": "Sales by Region", "read_only": 1}, {"fieldname": "section_break_22", "fieldtype": "Section Break", "label": "Trends"}, {"fieldname": "revenue_trend", "fieldtype": "JSON", "label": "Revenue Trend", "read_only": 1}, {"fieldname": "order_trend", "fieldtype": "JSON", "label": "Order Trend", "read_only": 1}, {"fieldname": "customer_trend", "fieldtype": "JSON", "label": "Customer Trend", "read_only": 1}, {"fieldname": "section_break_26", "fieldtype": "Section Break", "label": "Settings"}, {"fieldname": "last_updated", "fieldtype": "Datetime", "label": "Last Updated", "read_only": 1}, {"fieldname": "auto_refresh", "fieldtype": "Check", "label": "Auto Refresh", "default": "1"}, {"fieldname": "refresh_interval", "fieldtype": "Int", "label": "Refresh <PERSON> (minutes)", "default": "15"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Business Dashboard", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}