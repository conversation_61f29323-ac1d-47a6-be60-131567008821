# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now, getdate, add_days, flt
import json
from datetime import datetime, timedelta


class BusinessDashboard(Document):
    def validate(self):
        """Validate the Business Dashboard document"""
        self.set_date_range()
        self.refresh_data()

    def set_date_range(self):
        """Set from_date and to_date based on date_range selection"""
        today = getdate()
        
        if self.date_range == "Today":
            self.from_date = today
            self.to_date = today
        elif self.date_range == "Yesterday":
            yesterday = add_days(today, -1)
            self.from_date = yesterday
            self.to_date = yesterday
        elif self.date_range == "This Week":
            # Get Monday of current week
            days_since_monday = today.weekday()
            monday = add_days(today, -days_since_monday)
            self.from_date = monday
            self.to_date = today
        elif self.date_range == "Last Week":
            days_since_monday = today.weekday()
            this_monday = add_days(today, -days_since_monday)
            last_monday = add_days(this_monday, -7)
            last_sunday = add_days(this_monday, -1)
            self.from_date = last_monday
            self.to_date = last_sunday
        elif self.date_range == "This Month":
            self.from_date = today.replace(day=1)
            self.to_date = today
        elif self.date_range == "Last Month":
            first_day_this_month = today.replace(day=1)
            last_day_last_month = add_days(first_day_this_month, -1)
            first_day_last_month = last_day_last_month.replace(day=1)
            self.from_date = first_day_last_month
            self.to_date = last_day_last_month

    def refresh_data(self):
        """Refresh dashboard data"""
        self.calculate_key_metrics()
        self.calculate_top_performers()
        self.calculate_trends()
        self.last_updated = now()

    def calculate_key_metrics(self):
        """Calculate key business metrics"""
        # Get orders in date range
        orders = frappe.db.sql("""
            SELECT 
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                AVG(total_amount) as average_order_value,
                COUNT(DISTINCT customer) as total_customers
            FROM `tabEcommerce Order`
            WHERE order_date BETWEEN %s AND %s
            AND status NOT IN ('Cancelled', 'Returned')
        """, (self.from_date, self.to_date), as_dict=True)[0]

        self.total_orders = orders.total_orders or 0
        self.total_revenue = orders.total_revenue or 0.0
        self.average_order_value = orders.average_order_value or 0.0
        self.total_customers = orders.total_customers or 0

        # Calculate conversion rate (orders / unique visitors)
        # This would need web analytics integration
        self.conversion_rate = 0.0  # Placeholder

        # Calculate return rate
        returned_orders = frappe.db.count("Ecommerce Order", {
            "order_date": ["between", [self.from_date, self.to_date]],
            "status": "Returned"
        })
        
        if self.total_orders > 0:
            self.return_rate = (returned_orders / self.total_orders) * 100

    def calculate_top_performers(self):
        """Calculate top performing products and categories"""
        # Top products by revenue
        top_products = frappe.db.sql("""
            SELECT 
                p.product_name,
                SUM(eoi.quantity) as total_quantity,
                SUM(eoi.total) as total_revenue
            FROM `tabEcommerce Order Item` eoi
            JOIN `tabEcommerce Order` eo ON eoi.parent = eo.name
            JOIN `tabProduct` p ON eoi.product = p.name
            WHERE eo.order_date BETWEEN %s AND %s
            AND eo.status NOT IN ('Cancelled', 'Returned')
            GROUP BY eoi.product
            ORDER BY total_revenue DESC
            LIMIT 10
        """, (self.from_date, self.to_date), as_dict=True)

        self.top_products = json.dumps(top_products)

        # Top categories by revenue
        top_categories = frappe.db.sql("""
            SELECT 
                pc.category_name,
                SUM(eoi.quantity) as total_quantity,
                SUM(eoi.total) as total_revenue
            FROM `tabEcommerce Order Item` eoi
            JOIN `tabEcommerce Order` eo ON eoi.parent = eo.name
            JOIN `tabProduct` p ON eoi.product = p.name
            JOIN `tabProduct Category` pc ON p.category = pc.name
            WHERE eo.order_date BETWEEN %s AND %s
            AND eo.status NOT IN ('Cancelled', 'Returned')
            GROUP BY p.category
            ORDER BY total_revenue DESC
            LIMIT 10
        """, (self.from_date, self.to_date), as_dict=True)

        self.top_categories = json.dumps(top_categories)

    def calculate_trends(self):
        """Calculate trend data for charts"""
        # Revenue trend (daily)
        revenue_trend = frappe.db.sql("""
            SELECT 
                DATE(order_date) as date,
                SUM(total_amount) as revenue,
                COUNT(*) as orders
            FROM `tabEcommerce Order`
            WHERE order_date BETWEEN %s AND %s
            AND status NOT IN ('Cancelled', 'Returned')
            GROUP BY DATE(order_date)
            ORDER BY date
        """, (self.from_date, self.to_date), as_dict=True)

        self.revenue_trend = json.dumps(revenue_trend)

        # Order trend (daily)
        order_trend = frappe.db.sql("""
            SELECT 
                DATE(order_date) as date,
                COUNT(*) as orders,
                COUNT(DISTINCT customer) as customers
            FROM `tabEcommerce Order`
            WHERE order_date BETWEEN %s AND %s
            GROUP BY DATE(order_date)
            ORDER BY date
        """, (self.from_date, self.to_date), as_dict=True)

        self.order_trend = json.dumps(order_trend)

        # Customer trend (new customers daily)
        customer_trend = frappe.db.sql("""
            SELECT 
                DATE(creation) as date,
                COUNT(*) as new_customers
            FROM `tabEcommerce User`
            WHERE creation BETWEEN %s AND %s
            GROUP BY DATE(creation)
            ORDER BY date
        """, (self.from_date, self.to_date), as_dict=True)

        self.customer_trend = json.dumps(customer_trend)

    def get_dashboard_data(self):
        """Get formatted dashboard data for frontend"""
        return {
            "key_metrics": {
                "total_revenue": self.total_revenue,
                "total_orders": self.total_orders,
                "total_customers": self.total_customers,
                "average_order_value": self.average_order_value,
                "conversion_rate": self.conversion_rate,
                "return_rate": self.return_rate
            },
            "top_performers": {
                "products": json.loads(self.top_products) if self.top_products else [],
                "categories": json.loads(self.top_categories) if self.top_categories else []
            },
            "trends": {
                "revenue": json.loads(self.revenue_trend) if self.revenue_trend else [],
                "orders": json.loads(self.order_trend) if self.order_trend else [],
                "customers": json.loads(self.customer_trend) if self.customer_trend else []
            },
            "last_updated": self.last_updated
        }


@frappe.whitelist()
def get_dashboard(dashboard_name=None, user=None):
    """Get dashboard data"""
    if not user:
        user = frappe.session.user
    
    if dashboard_name:
        dashboard = frappe.get_doc("Business Dashboard", dashboard_name)
    else:
        # Get default dashboard for user
        dashboard_name = f"{user}_default"
        if not frappe.db.exists("Business Dashboard", dashboard_name):
            # Create default dashboard
            dashboard = frappe.get_doc({
                "doctype": "Business Dashboard",
                "dashboard_name": dashboard_name,
                "user": user,
                "user_type": "Manager",
                "date_range": "This Month"
            })
            dashboard.insert()
        else:
            dashboard = frappe.get_doc("Business Dashboard", dashboard_name)
    
    return dashboard.get_dashboard_data()


@frappe.whitelist()
def refresh_dashboard(dashboard_name):
    """Refresh dashboard data"""
    dashboard = frappe.get_doc("Business Dashboard", dashboard_name)
    dashboard.refresh_data()
    dashboard.save()
    
    return {
        "status": "success",
        "message": "Dashboard refreshed successfully",
        "data": dashboard.get_dashboard_data()
    }


@frappe.whitelist()
def get_real_time_metrics():
    """Get real-time metrics for live dashboard"""
    today = getdate()
    
    # Today's metrics
    today_metrics = frappe.db.sql("""
        SELECT 
            COUNT(*) as orders_today,
            SUM(total_amount) as revenue_today,
            COUNT(DISTINCT customer) as customers_today
        FROM `tabEcommerce Order`
        WHERE DATE(order_date) = %s
        AND status NOT IN ('Cancelled', 'Returned')
    """, (today,), as_dict=True)[0]

    # This month's metrics
    month_start = today.replace(day=1)
    month_metrics = frappe.db.sql("""
        SELECT 
            COUNT(*) as orders_month,
            SUM(total_amount) as revenue_month,
            COUNT(DISTINCT customer) as customers_month
        FROM `tabEcommerce Order`
        WHERE order_date BETWEEN %s AND %s
        AND status NOT IN ('Cancelled', 'Returned')
    """, (month_start, today), as_dict=True)[0]

    # Pending orders
    pending_orders = frappe.db.count("Ecommerce Order", {"status": "Pending"})
    
    # Low stock products
    low_stock = frappe.db.count("Product", {
        "track_inventory": 1,
        "stock_quantity": ["<=", "min_stock_level"]
    })

    return {
        "today": today_metrics,
        "month": month_metrics,
        "pending_orders": pending_orders,
        "low_stock_products": low_stock,
        "timestamp": now()
    }


@frappe.whitelist()
def get_sales_analytics(period="month", user_type=None):
    """Get detailed sales analytics"""
    if period == "day":
        date_format = "%Y-%m-%d"
        date_trunc = "DATE(order_date)"
    elif period == "week":
        date_format = "%Y-%u"
        date_trunc = "YEARWEEK(order_date)"
    elif period == "month":
        date_format = "%Y-%m"
        date_trunc = "DATE_FORMAT(order_date, '%Y-%m')"
    else:
        date_format = "%Y"
        date_trunc = "YEAR(order_date)"

    # Sales by period
    sales_data = frappe.db.sql(f"""
        SELECT 
            {date_trunc} as period,
            COUNT(*) as orders,
            SUM(total_amount) as revenue,
            AVG(total_amount) as avg_order_value,
            COUNT(DISTINCT customer) as customers
        FROM `tabEcommerce Order`
        WHERE status NOT IN ('Cancelled', 'Returned')
        AND order_date >= DATE_SUB(CURDATE(), INTERVAL 12 {period.upper()})
        GROUP BY {date_trunc}
        ORDER BY period
    """, as_dict=True)

    return {
        "sales_data": sales_data,
        "period": period
    }
