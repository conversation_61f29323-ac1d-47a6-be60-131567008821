# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils.nestedset import NestedSet
from frappe.website.utils import clear_cache


class ProductCategory(NestedSet):
    nsm_parent_field = 'parent_category'

    def validate(self):
        """Validate the Product Category document"""
        self.validate_category_code()
        self.set_meta_defaults()

    def validate_category_code(self):
        """Validate category code uniqueness"""
        if self.category_code:
            existing = frappe.db.exists("Product Category", {
                "category_code": self.category_code,
                "name": ["!=", self.name]
            })
            if existing:
                frappe.throw(f"Category Code '{self.category_code}' already exists")

    def set_meta_defaults(self):
        """Set default meta information if not provided"""
        if not self.meta_title:
            self.meta_title = self.category_name
        
        if not self.meta_description and self.description:
            # Extract first 160 characters from description for meta description
            from frappe.utils import strip_html
            clean_desc = strip_html(self.description)
            self.meta_description = clean_desc[:160] + "..." if len(clean_desc) > 160 else clean_desc

    def on_update(self):
        """Actions to perform after updating the document"""
        super().on_update()
        if self.show_in_website:
            clear_cache()

    def on_trash(self):
        """Actions to perform before deleting the document"""
        # Check if category has products
        products_count = frappe.db.count("Product", {"category": self.name})
        if products_count > 0:
            frappe.throw(f"Cannot delete category. {products_count} products are linked to this category.")
        
        super().on_trash()

    def get_subcategories(self, include_self=False):
        """Get all subcategories of this category"""
        lft, rgt = frappe.db.get_value("Product Category", self.name, ["lft", "rgt"])
        
        filters = {
            "lft": [">=", lft],
            "rgt": ["<=", rgt],
            "is_active": 1
        }
        
        if not include_self:
            filters["name"] = ["!=", self.name]
        
        return frappe.get_all("Product Category", 
                            filters=filters,
                            fields=["name", "category_name", "parent_category", "lft", "rgt"],
                            order_by="lft")

    def get_products_count(self, include_subcategories=True):
        """Get count of products in this category"""
        if include_subcategories:
            subcategories = self.get_subcategories(include_self=True)
            category_names = [cat.name for cat in subcategories]
            return frappe.db.count("Product", {"category": ["in", category_names], "is_active": 1})
        else:
            return frappe.db.count("Product", {"category": self.name, "is_active": 1})

    def get_category_path(self):
        """Get the full path of the category (parent > child > grandchild)"""
        path = []
        current = self
        
        while current:
            path.insert(0, current.category_name)
            if current.parent_category:
                current = frappe.get_doc("Product Category", current.parent_category)
            else:
                break
        
        return " > ".join(path)


@frappe.whitelist()
def get_category_tree():
    """Get category tree structure for frontend"""
    categories = frappe.get_all("Product Category",
                              filters={"is_active": 1},
                              fields=["name", "category_name", "parent_category", "category_image", "lft", "rgt"],
                              order_by="lft")
    
    # Build tree structure
    tree = []
    category_map = {}
    
    for category in categories:
        category_data = {
            "name": category.name,
            "category_name": category.category_name,
            "category_image": category.category_image,
            "children": []
        }
        category_map[category.name] = category_data
        
        if category.parent_category and category.parent_category in category_map:
            category_map[category.parent_category]["children"].append(category_data)
        else:
            tree.append(category_data)
    
    return tree


@frappe.whitelist()
def get_featured_categories():
    """Get featured categories for homepage"""
    return frappe.get_all("Product Category",
                         filters={"featured_category": 1, "is_active": 1, "show_in_website": 1},
                         fields=["name", "category_name", "description", "category_image"],
                         order_by="sort_order, category_name")


@frappe.whitelist()
def search_categories(query):
    """Search categories by name or description"""
    return frappe.get_all("Product Category",
                         filters={
                             "is_active": 1,
                             "show_in_website": 1,
                             "category_name": ["like", f"%{query}%"]
                         },
                         fields=["name", "category_name", "description", "category_image"],
                         order_by="category_name",
                         limit=20)


@frappe.whitelist()
def get_category_breadcrumb(category_name):
    """Get breadcrumb for a category"""
    category = frappe.get_doc("Product Category", category_name)
    breadcrumb = []
    
    current = category
    while current:
        breadcrumb.insert(0, {
            "name": current.name,
            "category_name": current.category_name
        })
        if current.parent_category:
            current = frappe.get_doc("Product Category", current.parent_category)
        else:
            break
    
    return breadcrumb
