{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "product_name", "product_code", "category", "brand", "section_break_6", "short_description", "description", "column_break_9", "product_images", "featured_image", "section_break_12", "price", "compare_price", "cost_price", "currency", "column_break_17", "tax_category", "tax_inclusive", "discount_percentage", "section_break_21", "sku", "barcode", "weight", "dimensions", "column_break_26", "track_inventory", "stock_quantity", "min_stock_level", "max_stock_level", "section_break_31", "supplier", "manufacturer", "country_of_origin", "column_break_35", "warranty_period", "return_policy", "shipping_class", "section_break_39", "is_active", "is_featured", "show_in_website", "requires_shipping", "column_break_44", "digital_product", "downloadable", "virtual_product", "section_break_48", "meta_title", "meta_description", "meta_keywords", "section_break_52", "tags", "attributes"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "PROD-.YYYY.-.#####", "reqd": 1}, {"fieldname": "product_name", "fieldtype": "Data", "in_list_view": 1, "label": "Product Name", "reqd": 1}, {"fieldname": "product_code", "fieldtype": "Data", "label": "Product Code", "unique": 1}, {"fieldname": "category", "fieldtype": "Link", "in_list_view": 1, "label": "Category", "options": "Product Category", "reqd": 1}, {"fieldname": "brand", "fieldtype": "Link", "label": "Brand", "options": "Brand"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Product Details"}, {"fieldname": "short_description", "fieldtype": "Text", "label": "Short Description"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "product_images", "fieldtype": "Table", "label": "Product Images", "options": "Product Image"}, {"fieldname": "featured_image", "fieldtype": "Attach Image", "label": "Featured Image"}, {"fieldname": "section_break_12", "fieldtype": "Section Break", "label": "Pricing"}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "reqd": 1}, {"fieldname": "compare_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Compare Price", "description": "Original price before discount"}, {"fieldname": "cost_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost Price"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "default": "TZS"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category"}, {"fieldname": "tax_inclusive", "fieldtype": "Check", "label": "Tax Inclusive", "default": "0"}, {"fieldname": "discount_percentage", "fieldtype": "Percent", "label": "Discount Percentage"}, {"fieldname": "section_break_21", "fieldtype": "Section Break", "label": "Inventory"}, {"fieldname": "sku", "fieldtype": "Data", "label": "SKU", "unique": 1}, {"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode"}, {"fieldname": "weight", "fieldtype": "Float", "label": "Weight (kg)"}, {"fieldname": "dimensions", "fieldtype": "Data", "label": "Dimensions (L x W x H)"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "track_inventory", "fieldtype": "Check", "label": "Track Inventory", "default": "1"}, {"fieldname": "stock_quantity", "fieldtype": "Int", "label": "Stock Quantity", "default": "0"}, {"fieldname": "min_stock_level", "fieldtype": "Int", "label": "Minimum Stock Level", "default": "0"}, {"fieldname": "max_stock_level", "fieldtype": "Int", "label": "Maximum Stock Level"}, {"fieldname": "section_break_31", "fieldtype": "Section Break", "label": "Supplier Information"}, {"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Supplier"}, {"fieldname": "manufacturer", "fieldtype": "Data", "label": "Manufacturer"}, {"fieldname": "country_of_origin", "fieldtype": "Link", "label": "Country of Origin", "options": "Country"}, {"fieldname": "column_break_35", "fieldtype": "Column Break"}, {"fieldname": "warranty_period", "fieldtype": "Data", "label": "Warranty Period"}, {"fieldname": "return_policy", "fieldtype": "Text", "label": "Return Policy"}, {"fieldname": "shipping_class", "fieldtype": "Select", "label": "Shipping Class", "options": "Standard\nExpress\nFree\nHeavy\nFragile"}, {"fieldname": "section_break_39", "fieldtype": "Section Break", "label": "Product Settings"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": "1"}, {"fieldname": "is_featured", "fieldtype": "Check", "label": "Is Featured", "default": "0"}, {"fieldname": "show_in_website", "fieldtype": "Check", "label": "Show in Website", "default": "1"}, {"fieldname": "requires_shipping", "fieldtype": "Check", "label": "Requires Shipping", "default": "1"}, {"fieldname": "column_break_44", "fieldtype": "Column Break"}, {"fieldname": "digital_product", "fieldtype": "Check", "label": "Digital Product", "default": "0"}, {"fieldname": "downloadable", "fieldtype": "Check", "label": "Downloadable", "default": "0"}, {"fieldname": "virtual_product", "fieldtype": "Check", "label": "Virtual Product", "default": "0"}, {"fieldname": "section_break_48", "fieldtype": "Section Break", "label": "SEO Settings"}, {"fieldname": "meta_title", "fieldtype": "Data", "label": "Meta Title"}, {"fieldname": "meta_description", "fieldtype": "Text", "label": "Meta Description"}, {"fieldname": "meta_keywords", "fieldtype": "Data", "label": "Meta Keywords"}, {"fieldname": "section_break_52", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "tags", "fieldtype": "Data", "label": "Tags", "description": "Comma-separated tags"}, {"fieldname": "attributes", "fieldtype": "Table", "label": "Product Attributes", "options": "Product Attribute"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Product", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "Supplier", "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}