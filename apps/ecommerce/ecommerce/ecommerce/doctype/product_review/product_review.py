# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now, flt
import json


class ProductReview(Document):
    def validate(self):
        """Validate the Product Review document"""
        self.validate_duplicate_review()
        self.check_verified_purchase()
        self.set_review_date()

    def validate_duplicate_review(self):
        """Check if user has already reviewed this product"""
        existing_review = frappe.db.exists("Product Review", {
            "product": self.product,
            "customer": self.customer,
            "name": ["!=", self.name]
        })
        
        if existing_review:
            frappe.throw("You have already reviewed this product")

    def check_verified_purchase(self):
        """Check if this is a verified purchase"""
        if self.order_reference:
            # Check if customer actually purchased this product
            order_item = frappe.db.exists("Ecommerce Order Item", {
                "parent": self.order_reference,
                "product": self.product
            })
            
            if order_item:
                order = frappe.get_doc("Ecommerce Order", self.order_reference)
                if order.customer == self.customer and order.status == "Delivered":
                    self.is_verified_purchase = 1
        else:
            # Check if customer has ever purchased this product
            purchased = frappe.db.sql("""
                SELECT eo.name 
                FROM `tabEcommerce Order` eo
                JOIN `tabEcommerce Order Item` eoi ON eo.name = eoi.parent
                WHERE eo.customer = %s AND eoi.product = %s AND eo.status = 'Delivered'
                LIMIT 1
            """, (self.customer, self.product))
            
            if purchased:
                self.is_verified_purchase = 1
                self.order_reference = purchased[0][0]

    def set_review_date(self):
        """Set review date if not set"""
        if not self.review_date:
            self.review_date = now()

    def on_update(self):
        """Actions after updating the review"""
        self.update_product_rating()

    def update_product_rating(self):
        """Update product's average rating"""
        if self.is_approved:
            # Calculate average rating for the product
            avg_rating = frappe.db.sql("""
                SELECT AVG(overall_rating) as avg_rating, COUNT(*) as review_count
                FROM `tabProduct Review`
                WHERE product = %s AND is_approved = 1
            """, (self.product,))[0]
            
            # Update product with new rating
            product = frappe.get_doc("Product", self.product)
            product.db_set("average_rating", flt(avg_rating[0] or 0, 2))
            product.db_set("review_count", avg_rating[1] or 0)

    def approve_review(self):
        """Approve the review"""
        self.is_approved = 1
        self.approved_by = frappe.session.user
        self.approved_date = now()
        self.save()

    def reject_review(self, reason=None):
        """Reject the review"""
        self.is_approved = 0
        if reason:
            self.add_comment("Comment", f"Review rejected: {reason}")
        self.save()

    def add_seller_response(self, response):
        """Add seller response to review"""
        self.seller_response = response
        self.response_date = now()
        self.responded_by = frappe.session.user
        self.save()

    def mark_helpful(self, user):
        """Mark review as helpful"""
        # Check if user already marked as helpful
        existing = frappe.db.exists("Review Helpful", {
            "review": self.name,
            "user": user
        })
        
        if not existing:
            frappe.get_doc({
                "doctype": "Review Helpful",
                "review": self.name,
                "user": user
            }).insert()
            
            self.helpful_count = (self.helpful_count or 0) + 1
            self.save()

    def get_sentiment_score(self):
        """Get sentiment analysis score for the review"""
        # This would integrate with AI/ML sentiment analysis
        # For now, return a simple score based on rating
        if self.overall_rating >= 4:
            return "Positive"
        elif self.overall_rating >= 3:
            return "Neutral"
        else:
            return "Negative"


@frappe.whitelist()
def submit_review(product, rating, title, review_text, order_reference=None, images=None):
    """Submit a product review"""
    customer = frappe.session.user
    
    # Check if user can review (must be logged in and not Guest)
    if customer == "Guest":
        frappe.throw("Please login to submit a review")
    
    review = frappe.get_doc({
        "doctype": "Product Review",
        "product": product,
        "customer": customer,
        "overall_rating": rating,
        "review_title": title,
        "review_text": review_text,
        "order_reference": order_reference
    })
    
    # Add images if provided
    if images:
        for image in images:
            review.append("review_images", {
                "image": image.get("image"),
                "caption": image.get("caption")
            })
    
    review.insert()
    
    return {
        "status": "success",
        "message": "Review submitted successfully. It will be published after approval.",
        "review_id": review.name
    }


@frappe.whitelist()
def get_product_reviews(product, limit=10, offset=0):
    """Get reviews for a product"""
    reviews = frappe.get_all("Product Review",
                           filters={
                               "product": product,
                               "is_approved": 1
                           },
                           fields=[
                               "name", "customer", "overall_rating", "review_title", 
                               "review_text", "review_date", "is_verified_purchase",
                               "helpful_count", "seller_response", "response_date"
                           ],
                           order_by="review_date desc",
                           limit=limit,
                           start=offset)
    
    # Get customer names
    for review in reviews:
        user = frappe.get_doc("User", review.customer)
        review["customer_name"] = f"{user.first_name} {user.last_name or ''}".strip()
        
        # Get review images
        review["images"] = frappe.get_all("Review Image",
                                        filters={"parent": review.name},
                                        fields=["image", "caption"])
    
    return reviews


@frappe.whitelist()
def get_review_summary(product):
    """Get review summary for a product"""
    summary = frappe.db.sql("""
        SELECT 
            AVG(overall_rating) as average_rating,
            COUNT(*) as total_reviews,
            SUM(CASE WHEN overall_rating = 5 THEN 1 ELSE 0 END) as five_star,
            SUM(CASE WHEN overall_rating = 4 THEN 1 ELSE 0 END) as four_star,
            SUM(CASE WHEN overall_rating = 3 THEN 1 ELSE 0 END) as three_star,
            SUM(CASE WHEN overall_rating = 2 THEN 1 ELSE 0 END) as two_star,
            SUM(CASE WHEN overall_rating = 1 THEN 1 ELSE 0 END) as one_star
        FROM `tabProduct Review`
        WHERE product = %s AND is_approved = 1
    """, (product,), as_dict=True)[0]
    
    return summary


@frappe.whitelist()
def mark_review_helpful(review_id):
    """Mark a review as helpful"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw("Please login to mark reviews as helpful")
    
    review = frappe.get_doc("Product Review", review_id)
    review.mark_helpful(user)
    
    return {
        "status": "success",
        "message": "Review marked as helpful",
        "helpful_count": review.helpful_count
    }


@frappe.whitelist()
def respond_to_review(review_id, response):
    """Add seller response to review"""
    # Check if user is authorized to respond (seller or admin)
    if not frappe.has_permission("Product Review", "write"):
        frappe.throw("Insufficient permissions")
    
    review = frappe.get_doc("Product Review", review_id)
    review.add_seller_response(response)
    
    return {
        "status": "success",
        "message": "Response added successfully"
    }
