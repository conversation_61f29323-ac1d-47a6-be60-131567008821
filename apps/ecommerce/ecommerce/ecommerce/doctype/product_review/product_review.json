{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "product", "customer", "order_reference", "section_break_5", "overall_rating", "product_quality_rating", "delivery_rating", "service_rating", "column_break_10", "review_title", "review_text", "pros", "cons", "section_break_15", "is_verified_purchase", "is_approved", "is_featured", "helpful_count", "column_break_20", "review_date", "approved_by", "approved_date", "section_break_24", "review_images", "section_break_26", "seller_response", "response_date", "responded_by"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "REV-.YYYY.-.#####", "reqd": 1}, {"fieldname": "product", "fieldtype": "Link", "in_list_view": 1, "label": "Product", "options": "Product", "reqd": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "User", "reqd": 1}, {"fieldname": "order_reference", "fieldtype": "Link", "label": "Order Reference", "options": "Ecommerce Order"}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Ratings"}, {"fieldname": "overall_rating", "fieldtype": "Rating", "in_list_view": 1, "label": "Overall Rating", "reqd": 1}, {"fieldname": "product_quality_rating", "fieldtype": "Rating", "label": "Product Quality"}, {"fieldname": "delivery_rating", "fieldtype": "Rating", "label": "Delivery"}, {"fieldname": "service_rating", "fieldtype": "Rating", "label": "Service"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "review_title", "fieldtype": "Data", "label": "Review Title", "reqd": 1}, {"fieldname": "review_text", "fieldtype": "Text", "label": "Review Text", "reqd": 1}, {"fieldname": "pros", "fieldtype": "Text", "label": "Pros"}, {"fieldname": "cons", "fieldtype": "Text", "label": "Cons"}, {"fieldname": "section_break_15", "fieldtype": "Section Break", "label": "Review Status"}, {"fieldname": "is_verified_purchase", "fieldtype": "Check", "label": "Verified Purchase", "default": "0", "read_only": 1}, {"fieldname": "is_approved", "fieldtype": "Check", "label": "Approved", "default": "0"}, {"fieldname": "is_featured", "fieldtype": "Check", "label": "Featured Review", "default": "0"}, {"fieldname": "helpful_count", "fieldtype": "Int", "label": "Helpful Count", "default": "0", "read_only": 1}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "review_date", "fieldtype": "Datetime", "label": "Review Date", "read_only": 1}, {"fieldname": "approved_by", "fieldtype": "Link", "label": "Approved By", "options": "User", "read_only": 1}, {"fieldname": "approved_date", "fieldtype": "Datetime", "label": "Approved Date", "read_only": 1}, {"fieldname": "section_break_24", "fieldtype": "Section Break", "label": "Review Images"}, {"fieldname": "review_images", "fieldtype": "Table", "label": "Review Images", "options": "Review Image"}, {"fieldname": "section_break_26", "fieldtype": "Section Break", "label": "Se<PERSON> Response"}, {"fieldname": "seller_response", "fieldtype": "Text", "label": "Se<PERSON> Response"}, {"fieldname": "response_date", "fieldtype": "Datetime", "label": "Response Date", "read_only": 1}, {"fieldname": "responded_by", "fieldtype": "Link", "label": "Responded By", "options": "User", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Product Review", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}