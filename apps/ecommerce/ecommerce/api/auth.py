# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import validate_email_address
import re


@frappe.whitelist(allow_guest=True)
def register_user(email, first_name, last_name, password, user_type="Customer", phone_number=None, company_name=None):
    """Register a new user and create ecommerce user profile"""
    
    # Validate input
    if not email or not first_name or not password:
        frappe.throw(_("Email, first name, and password are required"))
    
    if not validate_email_address(email):
        frappe.throw(_("Please enter a valid email address"))
    
    if len(password) < 8:
        frappe.throw(_("Password must be at least 8 characters long"))
    
    # Check if user already exists
    if frappe.db.exists("User", email):
        frappe.throw(_("User with this email already exists"))
    
    # Validate phone number if provided
    if phone_number and not re.match(r'^(\+255|0)[67]\d{8}$', phone_number):
        frappe.throw(_("Please enter a valid Tanzanian phone number"))
    
    try:
        # Create user
        user = frappe.get_doc({
            "doctype": "User",
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
            "enabled": 1,
            "new_password": password,
            "user_type": "Website User",
            "send_welcome_email": 0
        })
        
        # Add appropriate role
        if user_type == "Customer":
            user.add_roles("Customer")
        elif user_type in ["Company", "Wholesaler", "Retailer"]:
            user.add_roles("Supplier")
        
        user.insert(ignore_permissions=True)
        
        # Create ecommerce user profile
        ecommerce_user = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": email,
            "user_type": user_type,
            "phone_number": phone_number,
            "company_name": company_name,
            "verification_status": "Pending",
            "account_tier": "Basic",
            "is_active": 1
        })
        ecommerce_user.insert(ignore_permissions=True)
        
        # Send verification email
        send_verification_email(user)
        
        return {
            "message": _("User registered successfully. Please check your email for verification."),
            "user_id": user.name
        }
        
    except Exception as e:
        frappe.log_error(f"User registration failed: {str(e)}")
        frappe.throw(_("Registration failed. Please try again."))


def send_verification_email(user):
    """Send email verification to new user"""
    try:
        # Generate verification link
        verification_key = frappe.generate_hash(length=32)
        
        # Store verification key in user
        user.db_set("reset_password_key", verification_key)
        
        # Send email
        frappe.sendmail(
            recipients=[user.email],
            subject=_("Verify your email address"),
            template="verify_email",
            args={
                "user": user,
                "verification_link": f"{frappe.utils.get_url()}/verify-email?key={verification_key}"
            }
        )
        
    except Exception as e:
        frappe.log_error(f"Failed to send verification email: {str(e)}")


@frappe.whitelist(allow_guest=True)
def verify_email(key):
    """Verify user email address"""
    if not key:
        frappe.throw(_("Verification key is required"))
    
    user = frappe.db.get_value("User", {"reset_password_key": key}, "name")
    if not user:
        frappe.throw(_("Invalid verification key"))
    
    # Update user as verified
    frappe.db.set_value("User", user, "email_verified", 1)
    frappe.db.set_value("User", user, "reset_password_key", "")
    
    return {"message": _("Email verified successfully")}


@frappe.whitelist()
def change_password(old_password, new_password):
    """Change user password"""
    user = frappe.session.user
    
    if not old_password or not new_password:
        frappe.throw(_("Old password and new password are required"))
    
    if len(new_password) < 8:
        frappe.throw(_("New password must be at least 8 characters long"))
    
    # Verify old password
    from frappe.auth import check_password
    if not check_password(user, old_password):
        frappe.throw(_("Incorrect old password"))
    
    # Update password
    frappe.db.set_value("User", user, "new_password", new_password)
    
    return {"message": _("Password changed successfully")}


@frappe.whitelist(allow_guest=True)
def reset_password(email):
    """Send password reset email"""
    if not email:
        frappe.throw(_("Email is required"))
    
    if not validate_email_address(email):
        frappe.throw(_("Please enter a valid email address"))
    
    user = frappe.db.get_value("User", {"email": email}, "name")
    if not user:
        frappe.throw(_("User with this email does not exist"))
    
    # Generate reset key
    reset_key = frappe.generate_hash(length=32)
    frappe.db.set_value("User", user, "reset_password_key", reset_key)
    
    # Send reset email
    frappe.sendmail(
        recipients=[email],
        subject=_("Reset your password"),
        template="reset_password",
        args={
            "user": frappe.get_doc("User", user),
            "reset_link": f"{frappe.utils.get_url()}/reset-password?key={reset_key}"
        }
    )
    
    return {"message": _("Password reset email sent")}


@frappe.whitelist(allow_guest=True)
def update_password(key, new_password):
    """Update password using reset key"""
    if not key or not new_password:
        frappe.throw(_("Reset key and new password are required"))
    
    if len(new_password) < 8:
        frappe.throw(_("Password must be at least 8 characters long"))
    
    user = frappe.db.get_value("User", {"reset_password_key": key}, "name")
    if not user:
        frappe.throw(_("Invalid reset key"))
    
    # Update password
    frappe.db.set_value("User", user, "new_password", new_password)
    frappe.db.set_value("User", user, "reset_password_key", "")
    
    return {"message": _("Password updated successfully")}


@frappe.whitelist()
def get_user_info():
    """Get current user information"""
    user = frappe.session.user
    if user == "Guest":
        return {"message": "Not logged in"}
    
    user_doc = frappe.get_doc("User", user)
    ecommerce_user = frappe.db.get_value("Ecommerce User", {"user": user}, "*")
    
    return {
        "user": {
            "name": user_doc.name,
            "email": user_doc.email,
            "first_name": user_doc.first_name,
            "last_name": user_doc.last_name,
            "user_image": user_doc.user_image,
            "enabled": user_doc.enabled
        },
        "ecommerce_user": ecommerce_user
    }
