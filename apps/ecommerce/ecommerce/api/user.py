# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def create_ecommerce_user_profile(doc, method):
    """Create ecommerce user profile when a new user is created"""
    if doc.user_type == "Website User" and not frappe.db.exists("Ecommerce User", {"user": doc.email}):
        try:
            ecommerce_user = frappe.get_doc({
                "doctype": "Ecommerce User",
                "user": doc.email,
                "user_type": "Customer",
                "verification_status": "Pending",
                "account_tier": "Basic",
                "is_active": 1
            })
            ecommerce_user.insert(ignore_permissions=True)
        except Exception as e:
            frappe.log_error(f"Failed to create ecommerce user profile: {str(e)}")


@frappe.whitelist()
def update_profile(first_name=None, last_name=None, phone_number=None, address_line_1=None, 
                  address_line_2=None, city=None, state=None, postal_code=None, country=None,
                  company_name=None, business_type=None, website=None, preferred_language=None):
    """Update user profile information"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to update profile"))
    
    try:
        # Update User doctype
        user_doc = frappe.get_doc("User", user)
        if first_name:
            user_doc.first_name = first_name
        if last_name:
            user_doc.last_name = last_name
        user_doc.save(ignore_permissions=True)
        
        # Update Ecommerce User doctype
        ecommerce_user = frappe.get_doc("Ecommerce User", {"user": user})
        
        if phone_number:
            ecommerce_user.phone_number = phone_number
        if address_line_1:
            ecommerce_user.address_line_1 = address_line_1
        if address_line_2:
            ecommerce_user.address_line_2 = address_line_2
        if city:
            ecommerce_user.city = city
        if state:
            ecommerce_user.state = state
        if postal_code:
            ecommerce_user.postal_code = postal_code
        if country:
            ecommerce_user.country = country
        if company_name:
            ecommerce_user.company_name = company_name
        if business_type:
            ecommerce_user.business_type = business_type
        if website:
            ecommerce_user.website = website
        if preferred_language:
            ecommerce_user.preferred_language = preferred_language
        
        ecommerce_user.save(ignore_permissions=True)
        
        return {"message": _("Profile updated successfully")}
        
    except Exception as e:
        frappe.log_error(f"Profile update failed: {str(e)}")
        frappe.throw(_("Failed to update profile"))


@frappe.whitelist()
def upload_profile_image(file_url):
    """Update user profile image"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to upload profile image"))
    
    try:
        user_doc = frappe.get_doc("User", user)
        user_doc.user_image = file_url
        user_doc.save(ignore_permissions=True)
        
        return {"message": _("Profile image updated successfully"), "image_url": file_url}
        
    except Exception as e:
        frappe.log_error(f"Profile image update failed: {str(e)}")
        frappe.throw(_("Failed to update profile image"))


@frappe.whitelist()
def get_user_dashboard():
    """Get user dashboard data"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to view dashboard"))
    
    ecommerce_user = frappe.get_doc("Ecommerce User", {"user": user})
    
    # Get recent orders
    recent_orders = frappe.get_all("Ecommerce Order",
                                 filters={"customer": user},
                                 fields=["name", "order_date", "status", "total_amount"],
                                 order_by="order_date desc",
                                 limit=5)
    
    # Get cart summary
    cart = frappe.db.get_value("Shopping Cart", user, "*")
    cart_summary = {
        "total_items": cart.get("total_items", 0) if cart else 0,
        "total_amount": cart.get("total_amount", 0.0) if cart else 0.0
    }
    
    # Get wishlist count (placeholder)
    wishlist_count = 0
    
    # Get unread messages count
    unread_messages = frappe.db.count("Chat Message", {
        "recipient": user,
        "is_read": 0,
        "is_deleted": 0
    })
    
    return {
        "user_info": {
            "name": ecommerce_user.user,
            "user_type": ecommerce_user.user_type,
            "account_tier": ecommerce_user.account_tier,
            "verification_status": ecommerce_user.verification_status,
            "loyalty_points": ecommerce_user.loyalty_points,
            "total_orders": ecommerce_user.total_orders,
            "total_spent": ecommerce_user.total_spent
        },
        "recent_orders": recent_orders,
        "cart_summary": cart_summary,
        "wishlist_count": wishlist_count,
        "unread_messages": unread_messages
    }


@frappe.whitelist()
def request_verification(nida_number=None, business_license=None, business_license_number=None):
    """Request account verification"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to request verification"))
    
    try:
        ecommerce_user = frappe.get_doc("Ecommerce User", {"user": user})
        
        if ecommerce_user.user_type == "Company":
            if not nida_number or not business_license or not business_license_number:
                frappe.throw(_("NIDA number, business license, and business license number are required for company verification"))
        
        if nida_number:
            ecommerce_user.nida_number = nida_number
        if business_license:
            ecommerce_user.business_license = business_license
        if business_license_number:
            ecommerce_user.business_license_number = business_license_number
        
        ecommerce_user.verification_status = "Pending"
        ecommerce_user.save(ignore_permissions=True)
        
        # Notify administrators
        notify_verification_request(ecommerce_user)
        
        return {"message": _("Verification request submitted successfully")}
        
    except Exception as e:
        frappe.log_error(f"Verification request failed: {str(e)}")
        frappe.throw(_("Failed to submit verification request"))


def notify_verification_request(ecommerce_user):
    """Notify administrators about verification request"""
    try:
        # Get all users with Ecommerce Manager role
        managers = frappe.get_all("Has Role",
                                filters={"role": "Ecommerce Manager"},
                                fields=["parent"])
        
        if managers:
            recipients = [manager.parent for manager in managers]
            
            frappe.sendmail(
                recipients=recipients,
                subject=_("New Verification Request"),
                message=_(f"User {ecommerce_user.user} has requested account verification."),
                reference_doctype="Ecommerce User",
                reference_name=ecommerce_user.name
            )
    except Exception as e:
        frappe.log_error(f"Failed to notify verification request: {str(e)}")


@frappe.whitelist()
def get_user_addresses():
    """Get user addresses"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to view addresses"))
    
    ecommerce_user = frappe.get_doc("Ecommerce User", {"user": user})
    
    addresses = []
    
    # Primary address from ecommerce user
    if ecommerce_user.address_line_1:
        addresses.append({
            "type": "Primary",
            "address_line_1": ecommerce_user.address_line_1,
            "address_line_2": ecommerce_user.address_line_2,
            "city": ecommerce_user.city,
            "state": ecommerce_user.state,
            "postal_code": ecommerce_user.postal_code,
            "country": ecommerce_user.country,
            "phone_number": ecommerce_user.phone_number
        })
    
    return {"addresses": addresses}


@frappe.whitelist()
def update_location(latitude, longitude, address=None):
    """Update user location coordinates"""
    user = frappe.session.user
    if user == "Guest":
        frappe.throw(_("Please login to update location"))
    
    try:
        ecommerce_user = frappe.get_doc("Ecommerce User", {"user": user})
        ecommerce_user.set_location_coordinates(latitude, longitude)
        if address:
            ecommerce_user.address_line_1 = address
        ecommerce_user.save(ignore_permissions=True)
        
        return {"message": _("Location updated successfully")}
        
    except Exception as e:
        frappe.log_error(f"Location update failed: {str(e)}")
        frappe.throw(_("Failed to update location"))
