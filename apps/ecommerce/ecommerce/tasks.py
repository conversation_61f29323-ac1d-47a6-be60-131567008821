# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import now, add_days, getdate
from datetime import datetime, timedelta


def cleanup_expired_carts():
    """Clean up expired shopping carts"""
    try:
        # Get expired carts (older than 7 days for guest users)
        expired_date = add_days(getdate(), -7)
        
        expired_carts = frappe.get_all("Shopping Cart",
                                     filters={
                                         "expires_on": ["<", now()],
                                         "user": ["like", "%guest%"]
                                     },
                                     fields=["name"])
        
        for cart in expired_carts:
            frappe.delete_doc("Shopping Cart", cart.name, ignore_permissions=True)
        
        if expired_carts:
            frappe.logger().info(f"Cleaned up {len(expired_carts)} expired carts")
            
    except Exception as e:
        frappe.log_error(f"Failed to cleanup expired carts: {str(e)}")


def send_abandoned_cart_reminders():
    """Send reminders for abandoned carts"""
    try:
        # Get carts abandoned for 24 hours
        abandoned_date = datetime.now() - timed<PERSON>(hours=24)
        
        abandoned_carts = frappe.db.sql("""
            SELECT sc.name, sc.user, sc.total_amount, sc.last_updated
            FROM `tabShopping Cart` sc
            JOIN `tabUser` u ON sc.user = u.name
            WHERE sc.last_updated < %s
            AND sc.total_items > 0
            AND u.enabled = 1
            AND u.email NOT LIKE '%%guest%%'
            AND NOT EXISTS (
                SELECT 1 FROM `tabEcommerce Order` eo 
                WHERE eo.customer = sc.user 
                AND eo.order_date > sc.last_updated
            )
        """, (abandoned_date,), as_dict=True)
        
        for cart in abandoned_carts:
            send_abandoned_cart_email(cart)
        
        if abandoned_carts:
            frappe.logger().info(f"Sent {len(abandoned_carts)} abandoned cart reminders")
            
    except Exception as e:
        frappe.log_error(f"Failed to send abandoned cart reminders: {str(e)}")


def send_abandoned_cart_email(cart):
    """Send abandoned cart reminder email"""
    try:
        user = frappe.get_doc("User", cart.user)
        
        frappe.sendmail(
            recipients=[cart.user],
            subject="Don't forget your items!",
            template="abandoned_cart_reminder",
            args={
                "user": user,
                "cart_total": cart.total_amount,
                "cart_url": f"{frappe.utils.get_url()}/cart"
            }
        )
        
    except Exception as e:
        frappe.log_error(f"Failed to send abandoned cart email to {cart.user}: {str(e)}")


def update_dashboard_metrics():
    """Update dashboard metrics for all users"""
    try:
        # Get all business dashboards
        dashboards = frappe.get_all("Business Dashboard",
                                  filters={"auto_refresh": 1},
                                  fields=["name"])
        
        for dashboard in dashboards:
            dashboard_doc = frappe.get_doc("Business Dashboard", dashboard.name)
            dashboard_doc.refresh_data()
            dashboard_doc.save(ignore_permissions=True)
        
        frappe.logger().info(f"Updated {len(dashboards)} dashboard metrics")
        
    except Exception as e:
        frappe.log_error(f"Failed to update dashboard metrics: {str(e)}")


def sync_inventory_levels():
    """Sync inventory levels between Product and Item doctypes"""
    try:
        # Get products with inventory tracking
        products = frappe.get_all("Product",
                                filters={"track_inventory": 1},
                                fields=["name", "product_code", "stock_quantity"])
        
        synced_count = 0
        
        for product in products:
            item_code = product.product_code or product.name
            
            if frappe.db.exists("Item", item_code):
                # Get actual stock from ERPNext
                actual_stock = frappe.db.sql("""
                    SELECT SUM(actual_qty) as total_qty
                    FROM `tabBin`
                    WHERE item_code = %s
                """, (item_code,))[0][0] or 0
                
                # Update product stock if different
                if actual_stock != product.stock_quantity:
                    frappe.db.set_value("Product", product.name, "stock_quantity", actual_stock)
                    synced_count += 1
        
        if synced_count > 0:
            frappe.logger().info(f"Synced inventory for {synced_count} products")
            
    except Exception as e:
        frappe.log_error(f"Failed to sync inventory levels: {str(e)}")


def generate_weekly_reports():
    """Generate weekly sales and performance reports"""
    try:
        # Get date range for last week
        end_date = getdate()
        start_date = add_days(end_date, -7)
        
        # Generate sales report
        sales_data = frappe.db.sql("""
            SELECT 
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                AVG(total_amount) as avg_order_value,
                COUNT(DISTINCT customer) as unique_customers
            FROM `tabEcommerce Order`
            WHERE order_date BETWEEN %s AND %s
            AND status NOT IN ('Cancelled', 'Returned')
        """, (start_date, end_date), as_dict=True)[0]
        
        # Generate top products report
        top_products = frappe.db.sql("""
            SELECT 
                p.product_name,
                SUM(eoi.quantity) as total_sold,
                SUM(eoi.total) as total_revenue
            FROM `tabEcommerce Order Item` eoi
            JOIN `tabEcommerce Order` eo ON eoi.parent = eo.name
            JOIN `tabProduct` p ON eoi.product = p.name
            WHERE eo.order_date BETWEEN %s AND %s
            AND eo.status NOT IN ('Cancelled', 'Returned')
            GROUP BY eoi.product
            ORDER BY total_revenue DESC
            LIMIT 10
        """, (start_date, end_date), as_dict=True)
        
        # Send report to managers
        send_weekly_report(sales_data, top_products, start_date, end_date)
        
        frappe.logger().info("Generated weekly sales report")
        
    except Exception as e:
        frappe.log_error(f"Failed to generate weekly reports: {str(e)}")


def send_weekly_report(sales_data, top_products, start_date, end_date):
    """Send weekly report email to managers"""
    try:
        # Get managers
        managers = frappe.get_all("Has Role",
                                filters={"role": "Ecommerce Manager"},
                                fields=["parent"])
        
        if managers:
            recipients = [manager.parent for manager in managers]
            
            frappe.sendmail(
                recipients=recipients,
                subject=f"Weekly Sales Report ({start_date} to {end_date})",
                template="weekly_sales_report",
                args={
                    "sales_data": sales_data,
                    "top_products": top_products,
                    "start_date": start_date,
                    "end_date": end_date
                }
            )
            
    except Exception as e:
        frappe.log_error(f"Failed to send weekly report: {str(e)}")


def cleanup_old_tracking_data():
    """Clean up old order tracking data"""
    try:
        # Delete tracking data older than 6 months
        old_date = add_days(getdate(), -180)
        
        old_tracking = frappe.get_all("Order Tracking",
                                    filters={"status_date": ["<", old_date]},
                                    fields=["name"])
        
        for tracking in old_tracking:
            frappe.delete_doc("Order Tracking", tracking.name, ignore_permissions=True)
        
        if old_tracking:
            frappe.logger().info(f"Cleaned up {len(old_tracking)} old tracking records")
            
    except Exception as e:
        frappe.log_error(f"Failed to cleanup old tracking data: {str(e)}")


def update_product_ratings():
    """Update product average ratings based on approved reviews"""
    try:
        # Get all products with reviews
        products_with_reviews = frappe.db.sql("""
            SELECT DISTINCT product
            FROM `tabProduct Review`
            WHERE is_approved = 1
        """, as_list=True)
        
        updated_count = 0
        
        for product_tuple in products_with_reviews:
            product = product_tuple[0]
            
            # Calculate average rating
            avg_data = frappe.db.sql("""
                SELECT 
                    AVG(overall_rating) as avg_rating,
                    COUNT(*) as review_count
                FROM `tabProduct Review`
                WHERE product = %s AND is_approved = 1
            """, (product,), as_dict=True)[0]
            
            # Update product
            frappe.db.set_value("Product", product, {
                "average_rating": round(avg_data.avg_rating, 2),
                "review_count": avg_data.review_count
            })
            
            updated_count += 1
        
        if updated_count > 0:
            frappe.logger().info(f"Updated ratings for {updated_count} products")
            
    except Exception as e:
        frappe.log_error(f"Failed to update product ratings: {str(e)}")


def send_low_stock_alerts():
    """Send alerts for products with low stock"""
    try:
        # Get products with low stock
        low_stock_products = frappe.db.sql("""
            SELECT name, product_name, stock_quantity, min_stock_level
            FROM `tabProduct`
            WHERE track_inventory = 1
            AND stock_quantity <= min_stock_level
            AND is_active = 1
        """, as_dict=True)
        
        if low_stock_products:
            # Send alert to managers
            managers = frappe.get_all("Has Role",
                                    filters={"role": "Ecommerce Manager"},
                                    fields=["parent"])
            
            if managers:
                recipients = [manager.parent for manager in managers]
                
                frappe.sendmail(
                    recipients=recipients,
                    subject="Low Stock Alert",
                    template="low_stock_alert",
                    args={
                        "products": low_stock_products
                    }
                )
                
            frappe.logger().info(f"Sent low stock alert for {len(low_stock_products)} products")
            
    except Exception as e:
        frappe.log_error(f"Failed to send low stock alerts: {str(e)}")


def process_pending_orders():
    """Process orders that have been pending for too long"""
    try:
        # Get orders pending for more than 24 hours
        pending_date = datetime.now() - timedelta(hours=24)
        
        pending_orders = frappe.get_all("Ecommerce Order",
                                      filters={
                                          "status": "Pending",
                                          "order_date": ["<", pending_date]
                                      },
                                      fields=["name", "customer"])
        
        for order in pending_orders:
            # Send reminder to customer
            send_order_reminder(order)
            
            # Auto-cancel after 72 hours
            cancel_date = datetime.now() - timedelta(hours=72)
            if order.order_date < cancel_date:
                order_doc = frappe.get_doc("Ecommerce Order", order.name)
                order_doc.cancel_order("Auto-cancelled due to no payment")
        
        if pending_orders:
            frappe.logger().info(f"Processed {len(pending_orders)} pending orders")
            
    except Exception as e:
        frappe.log_error(f"Failed to process pending orders: {str(e)}")


def send_order_reminder(order):
    """Send payment reminder for pending order"""
    try:
        frappe.sendmail(
            recipients=[order.customer],
            subject="Payment Reminder for Your Order",
            template="order_payment_reminder",
            args={
                "order": order,
                "payment_url": f"{frappe.utils.get_url()}/orders/{order.name}/payment"
            }
        )
        
    except Exception as e:
        frappe.log_error(f"Failed to send order reminder for {order.name}: {str(e)}")
