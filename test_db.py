#!/usr/bin/env python3

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, '/home/<USER>/Desktop/work-bench')

# Install PyMySQL as MySQLdb
import pymysql
pymysql.install_as_MySQLdb()

# Set environment variables
os.environ['FRAPPE_SITE'] = 'work'

try:
    import frappe
    
    # Initialize Frappe
    frappe.init(site='work')
    frappe.connect()
    
    print("✅ Database connection successful!")
    
    # Test basic database operations
    print("📊 Testing database operations...")
    
    # Check if we can query the database
    result = frappe.db.sql("SELECT 1 as test", as_dict=True)
    print(f"✅ Basic query successful: {result}")
    
    # Check if we can see existing tables
    tables = frappe.db.sql("SHOW TABLES LIKE 'tab%'", as_list=True)
    print(f"✅ Found {len(tables)} Frappe tables in database")
    
    # Test if we can access User doctype
    try:
        users = frappe.get_all("User", limit=1)
        print(f"✅ User doctype accessible, found {len(users)} users")
    except Exception as e:
        print(f"⚠️  User doctype not accessible: {e}")
    
    # Check if our ecommerce app is installed
    try:
        apps = frappe.get_installed_apps()
        if 'ecommerce' in apps:
            print("✅ Ecommerce app is installed")
        else:
            print("⚠️  Ecommerce app not found in installed apps")
            print(f"Installed apps: {apps}")
    except Exception as e:
        print(f"⚠️  Could not check installed apps: {e}")
    
    frappe.destroy()
    print("✅ Database test completed successfully!")
    
except Exception as e:
    print(f"❌ Database test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
