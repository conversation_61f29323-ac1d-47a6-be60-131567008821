#!/usr/bin/env python3

import pymysql
import json

# Read site config
with open('/home/<USER>/Desktop/work-bench/sites/work/site_config.json', 'r') as f:
    config = json.load(f)

print("🔍 Testing direct database connection...")
print(f"Database: {config['db_name']}")
print(f"User: {config['db_user']}")

try:
    # Test direct PyMySQL connection
    connection = pymysql.connect(
        host='localhost',
        user=config['db_user'],
        password=config['db_password'],
        database=config['db_name'],
        charset='utf8mb4'
    )
    
    print("✅ Direct PyMySQL connection successful!")
    
    # Test basic query
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ Basic query successful: {result}")
    
    # Check tables
    with connection.cursor() as cursor:
        cursor.execute("SHOW TABLES LIKE 'tab%'")
        tables = cursor.fetchall()
        print(f"✅ Found {len(tables)} Frappe tables")
        
        # Show first few tables
        if tables:
            print("Sample tables:")
            for i, table in enumerate(tables[:5]):
                print(f"  - {table[0]}")
            if len(tables) > 5:
                print(f"  ... and {len(tables) - 5} more")
    
    # Check if User table exists
    with connection.cursor() as cursor:
        cursor.execute("SHOW TABLES LIKE 'tabUser'")
        user_table = cursor.fetchone()
        if user_table:
            print("✅ User table exists")
            
            # Count users
            cursor.execute("SELECT COUNT(*) FROM `tabUser`")
            user_count = cursor.fetchone()[0]
            print(f"✅ Found {user_count} users in database")
        else:
            print("⚠️  User table not found")
    
    connection.close()
    print("✅ Database test completed successfully!")
    print("\n🎉 The database is working correctly!")
    
except Exception as e:
    print(f"❌ Database connection failed: {e}")
    import traceback
    traceback.print_exc()
