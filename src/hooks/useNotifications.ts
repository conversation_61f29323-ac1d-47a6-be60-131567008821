import { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { Notification } from '@/types';

// Mock notifications for now - replace with actual API calls
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'success',
    title: 'Order Delivered',
    message: 'Your order #ORD-2025-00001 has been delivered successfully',
    timestamp: new Date().toISOString(),
    read: false,
    action_url: '/orders/ORD-2025-00001'
  },
  {
    id: '2',
    type: 'info',
    title: 'New Message',
    message: 'You have a new message from seller',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    read: false,
    action_url: '/messages'
  },
  {
    id: '3',
    type: 'warning',
    title: 'Payment Reminder',
    message: 'Payment pending for order #ORD-2025-00002',
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    read: true,
    action_url: '/orders/ORD-2025-00002'
  }
];

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Fetch notifications
  const { data, isLoading, error } = useQuery(
    'notifications',
    async () => {
      // Replace with actual API call
      return mockNotifications;
    },
    {
      refetchInterval: 30000, // Refetch every 30 seconds
      onSuccess: (data) => {
        setNotifications(data);
      }
    }
  );

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(n => n.id !== notificationId)
    );
  };

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    removeNotification
  };
};
