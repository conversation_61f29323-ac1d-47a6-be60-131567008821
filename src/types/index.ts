// User Types
export interface User {
  name: string;
  email: string;
  first_name: string;
  last_name: string;
  user_image?: string;
  enabled: boolean;
}

export interface EcommerceUser {
  name: string;
  user: string;
  user_type: 'Customer' | 'Company' | 'Wholesaler' | 'Retailer' | 'Delivery Person' | 'Middleman';
  account_tier: 'Basic' | 'Premium' | 'Enterprise';
  verification_status: 'Pending' | 'Verified' | 'Rejected';
  nida_number?: string;
  business_license?: string;
  business_license_number?: string;
  phone_number?: string;
  location_details?: any;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  company_name?: string;
  business_type?: string;
  tax_id?: string;
  website?: string;
  social_media_links?: any;
  preferred_language: string;
  account_balance: number;
  credit_limit: number;
  loyalty_points: number;
  total_orders: number;
  total_spent: number;
  last_order_date?: string;
  is_active: boolean;
}

// Product Types
export interface ProductCategory {
  name: string;
  category_name: string;
  parent_category?: string;
  category_code?: string;
  description?: string;
  category_image?: string;
  is_active: boolean;
  show_in_website: boolean;
  featured_category: boolean;
  sort_order: number;
  commission_rate?: number;
}

export interface ProductImage {
  image: string;
  image_title?: string;
  alt_text?: string;
  sort_order: number;
}

export interface ProductAttribute {
  attribute_name: string;
  attribute_value: string;
  attribute_type: 'Text' | 'Number' | 'Color' | 'Size' | 'Material' | 'Other';
}

export interface Product {
  name: string;
  product_name: string;
  product_code?: string;
  category: string;
  brand?: string;
  short_description?: string;
  description?: string;
  product_images: ProductImage[];
  featured_image?: string;
  price: number;
  compare_price?: number;
  cost_price?: number;
  currency: string;
  tax_category?: string;
  tax_inclusive: boolean;
  discount_percentage?: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  dimensions?: string;
  track_inventory: boolean;
  stock_quantity: number;
  min_stock_level: number;
  max_stock_level?: number;
  supplier?: string;
  manufacturer?: string;
  country_of_origin?: string;
  warranty_period?: string;
  return_policy?: string;
  shipping_class?: string;
  is_active: boolean;
  is_featured: boolean;
  show_in_website: boolean;
  requires_shipping: boolean;
  digital_product: boolean;
  downloadable: boolean;
  virtual_product: boolean;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  tags?: string;
  attributes: ProductAttribute[];
  average_rating?: number;
  review_count?: number;
}

// Cart Types
export interface CartItem {
  product: string;
  product_name: string;
  quantity: number;
  price: number;
  tax_rate?: number;
  total: number;
}

export interface ShoppingCart {
  name: string;
  user: string;
  session_id?: string;
  cart_items: CartItem[];
  total_items: number;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  last_updated: string;
  expires_on?: string;
}

// Order Types
export interface OrderItem {
  product: string;
  product_name: string;
  quantity: number;
  price: number;
  tax_rate?: number;
  total: number;
}

export interface EcommerceOrder {
  name: string;
  customer: string;
  order_date: string;
  status: 'Pending' | 'Confirmed' | 'Processing' | 'Shipped' | 'Delivered' | 'Cancelled' | 'Returned';
  payment_status: 'Pending' | 'Paid' | 'Partially Paid' | 'Refunded' | 'Failed';
  payment_method?: string;
  delivery_status: 'Pending' | 'Preparing' | 'In Transit' | 'Out for Delivery' | 'Delivered' | 'Failed Delivery';
  order_items: OrderItem[];
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  exchange_rate: number;
  paid_amount: number;
  outstanding_amount: number;
  billing_address?: string;
  shipping_address?: string;
  contact_phone?: string;
  contact_email?: string;
  special_instructions?: string;
  coupon_code?: string;
  discount_reason?: string;
  loyalty_points_used: number;
  loyalty_points_earned: number;
  estimated_delivery_date?: string;
  actual_delivery_date?: string;
  tracking_number?: string;
  notes?: string;
  internal_notes?: string;
}

export interface OrderTracking {
  name: string;
  order: string;
  status: string;
  status_date: string;
  location?: string;
  estimated_time?: string;
  actual_time?: string;
  notes?: string;
  updated_by: string;
  gps_coordinates?: any;
}

// Review Types
export interface ReviewImage {
  image: string;
  caption?: string;
}

export interface ProductReview {
  name: string;
  product: string;
  customer: string;
  order_reference?: string;
  overall_rating: number;
  product_quality_rating?: number;
  delivery_rating?: number;
  service_rating?: number;
  review_title: string;
  review_text: string;
  pros?: string;
  cons?: string;
  is_verified_purchase: boolean;
  is_approved: boolean;
  is_featured: boolean;
  helpful_count: number;
  review_date: string;
  approved_by?: string;
  approved_date?: string;
  review_images: ReviewImage[];
  seller_response?: string;
  response_date?: string;
  responded_by?: string;
}

// Chat Types
export interface ChatMessage {
  name: string;
  chat_room: string;
  sender: string;
  recipient: string;
  message_type: 'Text' | 'Image' | 'File' | 'Product' | 'Order' | 'Location';
  message_text?: string;
  attachment?: string;
  product_reference?: string;
  order_reference?: string;
  timestamp: string;
  is_read: boolean;
  read_timestamp?: string;
  is_deleted: boolean;
}

export interface ChatRoom {
  chat_room: string;
  other_user: string;
  other_user_name: string;
  other_user_image?: string;
  last_message_time: string;
  unread_count: number;
  last_message?: string;
  last_message_type?: string;
}

// Dashboard Types
export interface BusinessDashboard {
  name: string;
  dashboard_name: string;
  user: string;
  user_type: 'Admin' | 'Manager' | 'Seller' | 'Customer';
  date_range: string;
  from_date: string;
  to_date: string;
  total_revenue: number;
  total_orders: number;
  total_customers: number;
  average_order_value: number;
  conversion_rate: number;
  customer_acquisition_cost: number;
  customer_lifetime_value: number;
  return_rate: number;
  top_products: any;
  top_categories: any;
  sales_by_region: any;
  revenue_trend: any;
  order_trend: any;
  customer_trend: any;
  last_updated: string;
  auto_refresh: boolean;
  refresh_interval: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  message: T;
  exc?: string;
  exc_type?: string;
  _server_messages?: string;
}

export interface ListResponse<T = any> {
  data: T[];
  total_count?: number;
  page_length?: number;
  start?: number;
}

// Form Types
export interface LoginForm {
  usr: string;
  pwd: string;
  remember_me?: boolean;
}

export interface RegisterForm {
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  confirm_password: string;
  user_type: string;
  phone_number?: string;
  company_name?: string;
  terms_accepted: boolean;
}

export interface AddressForm {
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone_number?: string;
}

export interface PaymentForm {
  payment_method: string;
  card_number?: string;
  expiry_date?: string;
  cvv?: string;
  cardholder_name?: string;
  mobile_number?: string;
  bank_account?: string;
}

// Filter Types
export interface ProductFilters {
  category?: string;
  min_price?: number;
  max_price?: number;
  brand?: string;
  rating?: number;
  in_stock?: boolean;
  featured?: boolean;
  search?: string;
  sort_by?: 'name' | 'price' | 'rating' | 'newest' | 'popularity';
  sort_order?: 'asc' | 'desc';
}

export interface OrderFilters {
  status?: string;
  payment_status?: string;
  from_date?: string;
  to_date?: string;
  customer?: string;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  action_url?: string;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// Payment Types
export interface PaymentMethod {
  id: string;
  name: string;
  type: 'card' | 'mobile_money' | 'bank_transfer' | 'cash';
  icon: string;
  enabled: boolean;
  description?: string;
}

// Shipping Types
export interface ShippingMethod {
  id: string;
  name: string;
  description: string;
  cost: number;
  estimated_days: number;
  enabled: boolean;
}
