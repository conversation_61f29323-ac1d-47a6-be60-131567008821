// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/api/method/login',
  LOGOUT: '/api/method/logout',
  REGISTER: '/api/method/ecommerce.api.auth.register_user',
  CURRENT_USER: '/api/method/frappe.auth.get_logged_user',
  
  // Products
  PRODUCTS: '/api/resource/Product',
  FEATURED_PRODUCTS: '/api/method/ecommerce.ecommerce.doctype.product.product.get_featured_products',
  SEARCH_PRODUCTS: '/api/method/ecommerce.ecommerce.doctype.product.product.search_products',
  PRODUCT_DETAILS: '/api/method/ecommerce.ecommerce.doctype.product.product.get_product_details',
  
  // Categories
  CATEGORIES: '/api/resource/Product Category',
  CATEGORY_TREE: '/api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_category_tree',
  FEATURED_CATEGORIES: '/api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_featured_categories',
  
  // Cart
  GET_CART: '/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.get_cart',
  ADD_TO_CART: '/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.add_to_cart',
  REMOVE_FROM_CART: '/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.remove_from_cart',
  UPDATE_CART: '/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.update_cart_quantity',
  CLEAR_CART: '/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.clear_cart',
  
  // Orders
  CREATE_ORDER: '/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.create_order_from_cart',
  GET_ORDERS: '/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_customer_orders',
  ORDER_DETAILS: '/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_order_details',
  UPDATE_ORDER_STATUS: '/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.update_order_status',
  
  // Reviews
  PRODUCT_REVIEWS: '/api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_product_reviews',
  SUBMIT_REVIEW: '/api/method/ecommerce.ecommerce.doctype.product_review.product_review.submit_review',
  REVIEW_SUMMARY: '/api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_review_summary',
  
  // Chat
  SEND_MESSAGE: '/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.send_message',
  GET_MESSAGES: '/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_messages',
  GET_CHAT_ROOMS: '/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_rooms',
  
  // Dashboard
  GET_DASHBOARD: '/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_dashboard',
  REFRESH_DASHBOARD: '/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.refresh_dashboard',
  REAL_TIME_METRICS: '/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_real_time_metrics',
};

// User Types
export const USER_TYPES = {
  CUSTOMER: 'Customer',
  COMPANY: 'Company',
  WHOLESALER: 'Wholesaler',
  RETAILER: 'Retailer',
  DELIVERY_PERSON: 'Delivery Person',
  MIDDLEMAN: 'Middleman'
} as const;

// Account Tiers
export const ACCOUNT_TIERS = {
  BASIC: 'Basic',
  PREMIUM: 'Premium',
  ENTERPRISE: 'Enterprise'
} as const;

// Order Status
export const ORDER_STATUS = {
  PENDING: 'Pending',
  CONFIRMED: 'Confirmed',
  PROCESSING: 'Processing',
  SHIPPED: 'Shipped',
  DELIVERED: 'Delivered',
  CANCELLED: 'Cancelled',
  RETURNED: 'Returned'
} as const;

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'Pending',
  PAID: 'Paid',
  PARTIALLY_PAID: 'Partially Paid',
  REFUNDED: 'Refunded',
  FAILED: 'Failed'
} as const;

// Delivery Status
export const DELIVERY_STATUS = {
  PENDING: 'Pending',
  PREPARING: 'Preparing',
  IN_TRANSIT: 'In Transit',
  OUT_FOR_DELIVERY: 'Out for Delivery',
  DELIVERED: 'Delivered',
  FAILED_DELIVERY: 'Failed Delivery'
} as const;

// Payment Methods
export const PAYMENT_METHODS = [
  { id: 'cash_on_delivery', name: 'Cash on Delivery', type: 'cash', icon: '💵' },
  { id: 'mobile_money', name: 'Mobile Money', type: 'mobile_money', icon: '📱' },
  { id: 'credit_card', name: 'Credit Card', type: 'card', icon: '💳' },
  { id: 'bank_transfer', name: 'Bank Transfer', type: 'bank_transfer', icon: '🏦' },
  { id: 'paypal', name: 'PayPal', type: 'paypal', icon: '🅿️' },
  { id: 'stripe', name: 'Stripe', type: 'stripe', icon: '💎' }
];

// Shipping Methods
export const SHIPPING_METHODS = [
  { id: 'standard', name: 'Standard Delivery', cost: 5000, days: '3-5 business days' },
  { id: 'express', name: 'Express Delivery', cost: 10000, days: '1-2 business days' },
  { id: 'same_day', name: 'Same Day Delivery', cost: 15000, days: 'Same day' },
  { id: 'pickup', name: 'Store Pickup', cost: 0, days: 'Ready in 2 hours' }
];

// Product Sort Options
export const SORT_OPTIONS = [
  { value: 'name_asc', label: 'Name (A-Z)' },
  { value: 'name_desc', label: 'Name (Z-A)' },
  { value: 'price_asc', label: 'Price (Low to High)' },
  { value: 'price_desc', label: 'Price (High to Low)' },
  { value: 'rating_desc', label: 'Highest Rated' },
  { value: 'newest', label: 'Newest First' },
  { value: 'popularity', label: 'Most Popular' }
];

// Price Ranges
export const PRICE_RANGES = [
  { min: 0, max: 10000, label: 'Under TZS 10,000' },
  { min: 10000, max: 50000, label: 'TZS 10,000 - 50,000' },
  { min: 50000, max: 100000, label: 'TZS 50,000 - 100,000' },
  { min: 100000, max: 500000, label: 'TZS 100,000 - 500,000' },
  { min: 500000, max: null, label: 'Over TZS 500,000' }
];

// Rating Options
export const RATING_OPTIONS = [
  { value: 5, label: '5 Stars' },
  { value: 4, label: '4 Stars & Up' },
  { value: 3, label: '3 Stars & Up' },
  { value: 2, label: '2 Stars & Up' },
  { value: 1, label: '1 Star & Up' }
];

// Countries
export const COUNTRIES = [
  { code: 'TZ', name: 'Tanzania' },
  { code: 'KE', name: 'Kenya' },
  { code: 'UG', name: 'Uganda' },
  { code: 'RW', name: 'Rwanda' },
  { code: 'BI', name: 'Burundi' }
];

// Currencies
export const CURRENCIES = [
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TZS' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KES' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'UGX' },
  { code: 'RWF', name: 'Rwandan Franc', symbol: 'RWF' },
  { code: 'USD', name: 'US Dollar', symbol: '$' }
];

// Languages
export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'sw', name: 'Swahili' },
  { code: 'fr', name: 'French' },
  { code: 'ar', name: 'Arabic' }
];

// Business Types
export const BUSINESS_TYPES = [
  'Sole Proprietorship',
  'Partnership',
  'Corporation',
  'LLC',
  'NGO',
  'Government'
];

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'ecommerce_theme',
  LANGUAGE: 'ecommerce_language',
  CURRENCY: 'ecommerce_currency',
  RECENT_SEARCHES: 'ecommerce_recent_searches',
  WISHLIST: 'ecommerce_wishlist',
  VIEWED_PRODUCTS: 'ecommerce_viewed_products'
};

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^(\+255|0)[67]\d{8}$/,
  PASSWORD_MIN_LENGTH: 8,
  NIDA_LENGTH: 20,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_CART_QUANTITY: 99
};

// Error Messages
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  PASSWORD_TOO_SHORT: `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`,
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_NIDA: `NIDA number must be ${VALIDATION_RULES.NIDA_LENGTH} digits`,
  FILE_TOO_LARGE: 'File size must be less than 5MB',
  INVALID_FILE_TYPE: 'Please select a valid image file',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  SERVER_ERROR: 'Server error. Please try again later.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  LOGOUT_SUCCESS: 'Logged out successfully',
  REGISTER_SUCCESS: 'Registration successful! Please check your email.',
  PROFILE_UPDATED: 'Profile updated successfully',
  PRODUCT_ADDED_TO_CART: 'Product added to cart',
  PRODUCT_REMOVED_FROM_CART: 'Product removed from cart',
  ORDER_PLACED: 'Order placed successfully',
  REVIEW_SUBMITTED: 'Review submitted successfully',
  MESSAGE_SENT: 'Message sent successfully'
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'EcommerceApp',
  VERSION: '1.0.0',
  DESCRIPTION: 'Your one-stop shop for everything',
  DEFAULT_CURRENCY: 'TZS',
  DEFAULT_LANGUAGE: 'en',
  DEFAULT_COUNTRY: 'TZ',
  ITEMS_PER_PAGE: 20,
  MAX_SEARCH_RESULTS: 100,
  CART_EXPIRY_DAYS: 7,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutes
  DEBOUNCE_DELAY: 300, // milliseconds
  ANIMATION_DURATION: 300 // milliseconds
};
