import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { cartAPI } from '@/lib/api';
import { ShoppingCart, CartItem } from '@/types';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

interface CartContextType {
  cart: ShoppingCart | null;
  cartItems: CartItem[];
  cartCount: number;
  subtotal: number;
  total: number;
  isLoading: boolean;
  addToCart: (productId: string, quantity?: number) => Promise<void>;
  removeFromCart: (productId: string) => Promise<void>;
  updateQuantity: (productId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<ShoppingCart | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  const cartItems = cart?.cart_items || [];
  const cartCount = cartItems.reduce((total, item) => total + item.quantity, 0);
  const subtotal = cart?.subtotal || 0;
  const total = cart?.total_amount || 0;

  // Load cart when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      refreshCart();
    } else {
      setCart(null);
    }
  }, [isAuthenticated]);

  const refreshCart = async () => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      const response = await cartAPI.getCart();
      if (response.message) {
        setCart(response.message);
      }
    } catch (error) {
      console.error('Failed to fetch cart:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addToCart = async (productId: string, quantity = 1) => {
    if (!isAuthenticated) {
      toast.error('Please login to add items to cart');
      return;
    }

    try {
      setIsLoading(true);
      const response = await cartAPI.addToCart(productId, quantity);
      
      if (response.message === 'Item added to cart') {
        await refreshCart();
        toast.success('Item added to cart!');
      }
    } catch (error: any) {
      console.error('Failed to add to cart:', error);
      toast.error(error.message || 'Failed to add item to cart');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromCart = async (productId: string) => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      const response = await cartAPI.removeFromCart(productId);
      
      if (response.message === 'Item removed from cart') {
        await refreshCart();
        toast.success('Item removed from cart');
      }
    } catch (error: any) {
      console.error('Failed to remove from cart:', error);
      toast.error(error.message || 'Failed to remove item from cart');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateQuantity = async (productId: string, quantity: number) => {
    if (!isAuthenticated) return;

    if (quantity <= 0) {
      await removeFromCart(productId);
      return;
    }

    try {
      setIsLoading(true);
      const response = await cartAPI.updateQuantity(productId, quantity);
      
      if (response.message === 'Cart updated') {
        await refreshCart();
      }
    } catch (error: any) {
      console.error('Failed to update quantity:', error);
      toast.error(error.message || 'Failed to update quantity');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearCart = async () => {
    if (!isAuthenticated) return;

    try {
      setIsLoading(true);
      const response = await cartAPI.clearCart();
      
      if (response.message === 'Cart cleared') {
        setCart(null);
        toast.success('Cart cleared');
      }
    } catch (error: any) {
      console.error('Failed to clear cart:', error);
      toast.error(error.message || 'Failed to clear cart');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: CartContextType = {
    cart,
    cartItems,
    cartCount,
    subtotal,
    total,
    isLoading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
