import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import { authAPI, userAPI } from '@/lib/api';
import { User, EcommerceUser } from '@/types';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  ecommerceUser: EcommerceUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { usr: string; pwd: string }) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [ecommerceUser, setEcommerceUser] = useState<EcommerceUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = Cookies.get('sid');
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Get current user from Frappe
      const response = await authAPI.getCurrentUser();
      if (response.message && response.message !== 'Guest') {
        setUser({
          name: response.message,
          email: response.message,
          first_name: '',
          last_name: '',
          enabled: true
        });

        // Get ecommerce user profile
        await fetchEcommerceUser(response.message);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      Cookies.remove('sid');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchEcommerceUser = async (email: string) => {
    try {
      const response = await userAPI.getProfile(email);
      if (response.message) {
        setEcommerceUser(response.message);
      }
    } catch (error) {
      console.error('Failed to fetch ecommerce user:', error);
    }
  };

  const login = async (credentials: { usr: string; pwd: string }) => {
    try {
      setIsLoading(true);
      const response = await authAPI.login(credentials);
      
      if (response.message === 'Logged In') {
        // Set the session cookie
        const sidCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('sid='))
          ?.split('=')[1];
        
        if (sidCookie) {
          Cookies.set('sid', sidCookie, { expires: 7 }); // 7 days
        }

        // Fetch user data
        await checkAuthStatus();
        
        toast.success('Login successful!');
        
        // Redirect to intended page or home
        const redirectTo = router.query.redirect as string || '/';
        router.push(redirectTo);
      } else {
        throw new Error('Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.message || 'Login failed. Please check your credentials.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authAPI.logout();
      
      // Clear local state
      setUser(null);
      setEcommerceUser(null);
      
      // Clear cookies
      Cookies.remove('sid');
      
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local state even if API call fails
      setUser(null);
      setEcommerceUser(null);
      Cookies.remove('sid');
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: any) => {
    try {
      setIsLoading(true);
      const response = await authAPI.register(userData);
      
      if (response.message) {
        toast.success('Registration successful! Please check your email for verification.');
        router.push('/login');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: any) => {
    try {
      setIsLoading(true);
      const response = await userAPI.updateProfile(data);
      
      if (response.message) {
        // Refresh user data
        await refreshUser();
        toast.success('Profile updated successfully!');
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    if (user?.email) {
      await fetchEcommerceUser(user.email);
    }
  };

  const value: AuthContextType = {
    user,
    ecommerceUser,
    isAuthenticated,
    isLoading,
    login,
    logout,
    register,
    updateProfile,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protected routes
export const withAuth = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  redirectTo: string = '/login'
) => {
  const AuthenticatedComponent: React.FC<P> = (props) => {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push(`${redirectTo}?redirect=${encodeURIComponent(router.asPath)}`);
      }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="loading loading-spinner loading-lg text-primary"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };

  AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return AuthenticatedComponent;
};

// Higher-order component for guest-only routes (login, register)
export const withGuest = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  redirectTo: string = '/'
) => {
  const GuestComponent: React.FC<P> = (props) => {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && isAuthenticated) {
        router.push(redirectTo);
      }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="loading loading-spinner loading-lg text-primary"></div>
        </div>
      );
    }

    if (isAuthenticated) {
      return null;
    }

    return <WrappedComponent {...props} />;
  };

  GuestComponent.displayName = `withGuest(${WrappedComponent.displayName || WrappedComponent.name})`;

  return GuestComponent;
};
