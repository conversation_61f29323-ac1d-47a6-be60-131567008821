import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { ApiResponse, ListResponse } from '@/types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.FRAPPE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('sid');
    if (token) {
      config.headers.Cookie = `sid=${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear auth and redirect to login
      Cookies.remove('sid');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generic API functions
export const apiCall = async <T = any>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> => {
  try {
    const response = await api(endpoint, options);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || error.message || 'API call failed');
  }
};

export const get = async <T = any>(endpoint: string, params?: any): Promise<T> => {
  return apiCall<T>(endpoint, { method: 'GET', params });
};

export const post = async <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiCall<T>(endpoint, { method: 'POST', data });
};

export const put = async <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiCall<T>(endpoint, { method: 'PUT', data });
};

export const del = async <T = any>(endpoint: string): Promise<T> => {
  return apiCall<T>(endpoint, { method: 'DELETE' });
};

// Authentication API
export const authAPI = {
  login: async (credentials: { usr: string; pwd: string }) => {
    return post<ApiResponse>('/api/method/login', credentials);
  },

  logout: async () => {
    return post<ApiResponse>('/api/method/logout');
  },

  getCurrentUser: async () => {
    return get<ApiResponse>('/api/method/frappe.auth.get_logged_user');
  },

  register: async (userData: any) => {
    return post<ApiResponse>('/api/method/ecommerce.api.auth.register_user', userData);
  },

  forgotPassword: async (email: string) => {
    return post<ApiResponse>('/api/method/frappe.core.doctype.user.user.reset_password', { user: email });
  },

  resetPassword: async (key: string, password: string) => {
    return post<ApiResponse>('/api/method/frappe.core.doctype.user.user.update_password', {
      key,
      new_password: password
    });
  }
};

// User API
export const userAPI = {
  getProfile: async (email?: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_user.ecommerce_user.get_user_profile', {
      user_email: email
    });
  },

  updateProfile: async (data: any) => {
    return post<ApiResponse>('/api/method/ecommerce.api.user.update_profile', data);
  },

  getStatistics: async (userType?: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_user.ecommerce_user.get_user_statistics', {
      user_type: userType
    });
  }
};

// Product API
export const productAPI = {
  getProducts: async (filters?: any) => {
    return get<ListResponse>('/api/resource/Product', {
      filters: JSON.stringify(filters || {}),
      fields: JSON.stringify([
        'name', 'product_name', 'price', 'featured_image', 'short_description',
        'category', 'brand', 'is_featured', 'average_rating', 'review_count'
      ])
    });
  },

  getProduct: async (productId: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product.product.get_product_details', {
      product_name: productId
    });
  },

  searchProducts: async (query: string, filters?: any) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product.product.search_products', {
      query,
      ...filters
    });
  },

  getFeaturedProducts: async (limit = 8) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product.product.get_featured_products', {
      limit
    });
  },

  getCategories: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_category_tree');
  },

  getFeaturedCategories: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_category.product_category.get_featured_categories');
  }
};

// Cart API
export const cartAPI = {
  getCart: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.get_cart');
  },

  addToCart: async (product: string, quantity = 1) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.add_to_cart', {
      product,
      quantity
    });
  },

  removeFromCart: async (product: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.remove_from_cart', {
      product
    });
  },

  updateQuantity: async (product: string, quantity: number) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.update_cart_quantity', {
      product,
      quantity
    });
  },

  clearCart: async () => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.shopping_cart.shopping_cart.clear_cart');
  }
};

// Order API
export const orderAPI = {
  createOrder: async (orderData: any) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.create_order_from_cart', orderData);
  },

  getOrders: async (limit = 10) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_customer_orders', {
      limit
    });
  },

  getOrder: async (orderId: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.get_order_details', {
      order_name: orderId
    });
  },

  updateOrderStatus: async (orderId: string, status: string, notes?: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.ecommerce_order.ecommerce_order.update_order_status', {
      order_name: orderId,
      status,
      notes
    });
  },

  getOrderTracking: async (orderId: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.order_tracking.order_tracking.get_order_tracking', {
      order: orderId
    });
  }
};

// Review API
export const reviewAPI = {
  getProductReviews: async (product: string, limit = 10, offset = 0) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_product_reviews', {
      product,
      limit,
      offset
    });
  },

  submitReview: async (reviewData: any) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_review.product_review.submit_review', reviewData);
  },

  getReviewSummary: async (product: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_review.product_review.get_review_summary', {
      product
    });
  },

  markHelpful: async (reviewId: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_review.product_review.mark_review_helpful', {
      review_id: reviewId
    });
  },

  respondToReview: async (reviewId: string, response: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.product_review.product_review.respond_to_review', {
      review_id: reviewId,
      response
    });
  }
};

// Chat API
export const chatAPI = {
  sendMessage: async (messageData: any) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.send_message', messageData);
  },

  getMessages: async (chatRoom: string, limit = 50, offset = 0) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_messages', {
      chat_room: chatRoom,
      limit,
      offset
    });
  },

  getChatRooms: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_chat_rooms');
  },

  markAsRead: async (chatRoom: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.mark_messages_as_read', {
      chat_room: chatRoom
    });
  },

  deleteMessage: async (messageId: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.delete_message', {
      message_id: messageId
    });
  },

  getUnreadCount: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.get_unread_count');
  },

  searchUsers: async (query: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.chat_message.chat_message.search_users_for_chat', {
      query
    });
  }
};

// Dashboard API
export const dashboardAPI = {
  getDashboard: async (dashboardName?: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_dashboard', {
      dashboard_name: dashboardName
    });
  },

  refreshDashboard: async (dashboardName: string) => {
    return post<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.refresh_dashboard', {
      dashboard_name: dashboardName
    });
  },

  getRealTimeMetrics: async () => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_real_time_metrics');
  },

  getSalesAnalytics: async (period = 'month', userType?: string) => {
    return get<ApiResponse>('/api/method/ecommerce.ecommerce.doctype.business_dashboard.business_dashboard.get_sales_analytics', {
      period,
      user_type: userType
    });
  }
};

export default api;
