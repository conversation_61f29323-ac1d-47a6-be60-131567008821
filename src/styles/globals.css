@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90 focus:ring-2 focus:ring-primary/20 transition-all duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary/90 focus:ring-2 focus:ring-secondary/20 transition-all duration-200;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  .input-focus {
    @apply focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary to-secondary/80;
  }
  
  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary;
  }
  
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
  
  .scale-in {
    @apply animate-scale-in;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .border-gradient {
    border-image: linear-gradient(45deg, theme('colors.primary.500'), theme('colors.secondary.500')) 1;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-photo {
    aspect-ratio: 4 / 3;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .skeleton {
    @apply bg-gray-700;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* Focus visible for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-primary/50 ring-offset-2;
}

/* Custom animations for specific components */
.cart-item-enter {
  opacity: 0;
  transform: translateX(100%);
}

.cart-item-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.cart-item-exit {
  opacity: 1;
  transform: translateX(0);
}

.cart-item-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: opacity 300ms, transform 300ms;
}

/* Product card animations */
.product-card {
  @apply transition-all duration-300 ease-in-out;
}

.product-card:hover {
  @apply transform scale-105 shadow-xl;
}

.product-card:hover .product-image {
  @apply scale-110;
}

.product-image {
  @apply transition-transform duration-500 ease-in-out;
}

/* Button loading state */
.btn-loading {
  @apply relative text-transparent;
}

.btn-loading::after {
  content: '';
  @apply absolute inset-0 m-auto w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Toast notifications */
.toast-enter {
  opacity: 0;
  transform: translateY(-100%);
}

.toast-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.toast-exit {
  opacity: 1;
  transform: translateY(0);
}

.toast-exit-active {
  opacity: 0;
  transform: translateY(-100%);
  transition: opacity 300ms, transform 300ms;
}

/* Modal animations */
.modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300;
}

.modal-enter .modal-content {
  @apply scale-95 opacity-0;
}

.modal-enter-active .modal-content {
  @apply scale-100 opacity-100;
}

.modal-exit .modal-content {
  @apply scale-100 opacity-100;
}

.modal-exit-active .modal-content {
  @apply scale-95 opacity-0;
}
