# Deployment Guide

This guide covers deploying the E-commerce application to production environments.

## 🏗 Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Next.js App   │    │  Frappe/ERPNext │
│    (Nginx)      │────│   (Frontend)    │────│    (Backend)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │      Redis      │    │   MariaDB/MySQL │
                       │   (Cache/Queue) │    │   (Database)    │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 Production Deployment

### Prerequisites

- Ubuntu 20.04+ or CentOS 8+
- 4GB+ RAM
- 50GB+ storage
- Domain name with SSL certificate

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3-dev python3-pip nodejs npm nginx redis-server mariadb-server

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2
```

### 2. Database Setup

```bash
# Secure MariaDB installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE ecommerce_db;
CREATE USER 'ecommerce_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON ecommerce_db.* TO 'ecommerce_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Frappe/ERPNext Backend Deployment

```bash
# Install Frappe Bench
pip3 install frappe-bench

# Create production site
bench new-site ecommerce.yourdomain.com --db-name ecommerce_db --db-user ecommerce_user --db-password strong_password

# Set site as current
bench use ecommerce.yourdomain.com

# Install ERPNext
bench get-app erpnext
bench install-app erpnext

# Install Ecommerce app
bench get-app ecommerce https://github.com/yourusername/ecommerce-app.git
bench install-app ecommerce

# Setup production
bench setup production --user frappe
sudo bench setup nginx
sudo bench setup supervisor
```

### 4. Next.js Frontend Deployment

```bash
# Clone frontend repository
git clone https://github.com/yourusername/ecommerce-frontend.git /var/www/ecommerce-frontend
cd /var/www/ecommerce-frontend

# Install dependencies
npm ci --production

# Set environment variables
cp .env.example .env.production
# Edit .env.production with production values

# Build application
npm run build

# Start with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 5. Nginx Configuration

Create `/etc/nginx/sites-available/ecommerce`:

```nginx
# Frontend (Next.js)
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Backend API (Frappe)
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/ecommerce /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL Certificate Setup

Using Let's Encrypt:
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com
```

### 7. Environment Configuration

Update production environment files:

**Frappe site_config.json:**
```json
{
  "db_name": "ecommerce_db",
  "db_password": "strong_password",
  "allow_cors": ["https://yourdomain.com"],
  "host_name": "https://api.yourdomain.com",
  "mail_server": "smtp.gmail.com",
  "mail_port": 587,
  "use_tls": 1,
  "mail_login": "<EMAIL>",
  "mail_password": "your-app-password",
  "redis_cache": "redis://localhost:6379/0",
  "redis_queue": "redis://localhost:6379/1",
  "redis_socketio": "redis://localhost:6379/2"
}
```

**Next.js .env.production:**
```env
NODE_ENV=production
FRAPPE_URL=https://api.yourdomain.com
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
# Add other production environment variables
```

## 🐳 Docker Deployment

### Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  mariadb:
    image: mariadb:10.6
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ecommerce_db
      MYSQL_USER: ecommerce_user
      MYSQL_PASSWORD: userpassword
    volumes:
      - mariadb_data:/var/lib/mysql
    networks:
      - ecommerce_network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - ecommerce_network

  frappe:
    build:
      context: ./apps/ecommerce
      dockerfile: Dockerfile
    depends_on:
      - mariadb
      - redis
    environment:
      - DB_HOST=mariadb
      - DB_NAME=ecommerce_db
      - DB_USER=ecommerce_user
      - DB_PASSWORD=userpassword
      - REDIS_CACHE=redis://redis:6379/0
      - REDIS_QUEUE=redis://redis:6379/1
    volumes:
      - frappe_data:/home/<USER>/frappe-bench
    ports:
      - "8000:8000"
    networks:
      - ecommerce_network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - frappe
    environment:
      - NODE_ENV=production
      - FRAPPE_URL=http://frappe:8000
    ports:
      - "3000:3000"
    networks:
      - ecommerce_network

  nginx:
    image: nginx:alpine
    depends_on:
      - frontend
      - frappe
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - ecommerce_network

volumes:
  mariadb_data:
  redis_data:
  frappe_data:

networks:
  ecommerce_network:
    driver: bridge
```

### Frontend Dockerfile

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

## 📊 Monitoring and Maintenance

### 1. Application Monitoring

```bash
# PM2 monitoring
pm2 monit

# Frappe logs
tail -f /home/<USER>/frappe-bench/logs/web.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. Database Backup

```bash
# Create backup script
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u ecommerce_user -p ecommerce_db > $BACKUP_DIR/db_backup_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz /home/<USER>/frappe-bench/sites/ecommerce.yourdomain.com/private/files

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup.sh

# Add to crontab
echo "0 2 * * * /home/<USER>/backup.sh" | crontab -
```

### 3. Performance Optimization

```bash
# Enable Frappe caching
bench --site ecommerce.yourdomain.com set-config enable_redis_cache 1

# Optimize database
mysqlcheck -u root -p --optimize --all-databases

# Configure log rotation
sudo logrotate -f /etc/logrotate.conf
```

### 4. Security Hardening

```bash
# Firewall setup
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Fail2ban for SSH protection
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Regular security updates
sudo apt update && sudo apt upgrade -y
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/ecommerce-frontend
          git pull origin main
          npm ci --production
          npm run build
          pm2 reload ecosystem.config.js --env production
```

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database status
   sudo systemctl status mariadb
   
   # Test connection
   mysql -u ecommerce_user -p ecommerce_db
   ```

2. **Frappe Site Issues**
   ```bash
   # Restart Frappe services
   sudo supervisorctl restart all
   
   # Check site status
   bench --site ecommerce.yourdomain.com migrate
   ```

3. **Frontend Build Issues**
   ```bash
   # Clear cache and rebuild
   rm -rf .next node_modules
   npm install
   npm run build
   ```

4. **SSL Certificate Issues**
   ```bash
   # Renew certificates
   sudo certbot renew
   sudo systemctl reload nginx
   ```

### Performance Issues

1. **Database Optimization**
   ```sql
   -- Check slow queries
   SHOW PROCESSLIST;
   
   -- Optimize tables
   OPTIMIZE TABLE `tabProduct`;
   OPTIMIZE TABLE `tabEcommerce Order`;
   ```

2. **Redis Monitoring**
   ```bash
   # Check Redis status
   redis-cli info memory
   redis-cli info stats
   ```

3. **Application Monitoring**
   ```bash
   # Check resource usage
   htop
   iotop
   
   # Monitor logs
   journalctl -f -u nginx
   ```

This deployment guide provides a comprehensive approach to deploying the e-commerce application in production environments with proper security, monitoring, and maintenance procedures.
