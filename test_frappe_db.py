#!/usr/bin/env python3

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, "/home/<USER>/Desktop/work-bench")

# Install PyMySQL as MySQLdb before importing frappe
import pymysql

pymysql.install_as_MySQLdb()

# Set environment variables
os.environ["FRAPPE_SITE"] = "work"
os.chdir("/home/<USER>/Desktop/work-bench")

try:
    import frappe

    # Initialize Frappe
    frappe.init(site="work", sites_path="/home/<USER>/Desktop/work-bench/sites")
    frappe.connect()

    print("✅ Frappe database connection successful!")

    # Test basic database operations
    print("📊 Testing Frappe database operations...")

    # Check if we can query the database
    result = frappe.db.sql("SELECT 1 as test", as_dict=True)
    print(f"✅ Basic query successful: {result}")

    # Check if we can see existing tables
    tables = frappe.db.sql("SHOW TABLES LIKE 'tab%'", as_list=True)
    print(f"✅ Found {len(tables)} Frappe tables in database")

    # Test if we can access User doctype
    try:
        users = frappe.get_all("User", limit=5, fields=["name", "email", "enabled"])
        print(f"✅ User doctype accessible, found {len(users)} users")
        for user in users:
            print(
                f"  - {user.name} ({user.email}) - {'Enabled' if user.enabled else 'Disabled'}"
            )
    except Exception as e:
        print(f"⚠️  User doctype not accessible: {e}")

    # Check if our ecommerce app is installed
    try:
        apps = frappe.get_installed_apps()
        if "ecommerce" in apps:
            print("✅ Ecommerce app is installed")
        else:
            print("⚠️  Ecommerce app not found in installed apps")
            print(f"Installed apps: {apps}")
    except Exception as e:
        print(f"⚠️  Could not check installed apps: {e}")

    # Test if we can create a simple document
    try:
        # Try to get a simple doctype
        doctype_list = frappe.db.sql(
            "SELECT name FROM `tabDocType` LIMIT 5", as_list=True
        )
        print(f"✅ Found {len(doctype_list)} doctypes")
        if doctype_list:
            print("Sample doctypes:")
            for dt in doctype_list[:3]:
                print(f"  - {dt[0]}")
    except Exception as e:
        print(f"⚠️  Could not query doctypes: {e}")

    frappe.destroy()
    print("✅ Frappe database test completed successfully!")
    print("\n🎉 The Frappe database integration is working correctly!")

except Exception as e:
    print(f"❌ Frappe database test failed: {e}")
    import traceback

    traceback.print_exc()
    sys.exit(1)
