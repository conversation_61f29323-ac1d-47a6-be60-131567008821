# Frappe Backend Configuration
FRAPPE_URL=http://localhost:8000

# Map Integration
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here

# Payment Gateways
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here

# Mobile Money (Tanzania)
MPESA_API_KEY=your_mpesa_api_key_here
TIGO_PESA_API_KEY=your_tigo_pesa_api_key_here

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here

# Analytics and Monitoring
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
SENTRY_DSN=your_sentry_dsn_here

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# App Configuration
APP_NAME=EcommerceApp
APP_VERSION=1.0.0
APP_DESCRIPTION=Your one-stop shop for everything
DEFAULT_CURRENCY=TZS
DEFAULT_LANGUAGE=en
DEFAULT_COUNTRY=TZ

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Development
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug

# Production
# NODE_ENV=production
# DEBUG=false
# LOG_LEVEL=error
