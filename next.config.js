/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: [
      'localhost',
      '127.0.0.1',
      // Add your Frappe server domain here
      'your-frappe-domain.com'
    ],
    unoptimized: true
  },
  env: {
    FRAPPE_URL: process.env.FRAPPE_URL || 'http://localhost:8000',
    MAPBOX_ACCESS_TOKEN: process.env.MAPBOX_ACCESS_TOKEN,
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
    PAYPAL_CLIENT_ID: process.env.PAYPAL_CLIENT_ID
  },
  async rewrites() {
    return [
      {
        source: '/api/frappe/:path*',
        destination: `${process.env.FRAPPE_URL || 'http://localhost:8000'}/api/:path*`
      }
    ]
  },
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false
    }
    return config
  }
}

module.exports = nextConfig
