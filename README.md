# E-commerce Application with Frappe/ERPNext Integration

A comprehensive e-commerce platform built with Next.js frontend and Frappe/ERPNext backend, featuring multi-user support, real-time chat, order tracking, and business analytics.

## 🚀 Features

### User Management
- **Multi-user types**: Customer, Company, Wholesaler, Retailer, Delivery Person, Middleman
- **Account verification** with NIDA and business license validation
- **Tiered accounts**: Basic, Premium, Enterprise
- **User profiles** with location tracking and preferences

### Product Management
- **Hierarchical categories** with nested structure
- **Product variants** and attributes
- **Inventory tracking** with stock levels
- **Image galleries** and rich descriptions
- **SEO optimization** with meta tags

### Shopping & Orders
- **Shopping cart** with real-time updates
- **Order management** with status tracking
- **Payment integration** (Mobile Money, Cards, Bank Transfer)
- **Real-time order tracking** with GPS coordinates
- **Delivery management** with route optimization

### Communication & Social
- **Real-time chat** between users
- **Product reviews** with ratings and images
- **Seller responses** to reviews
- **In-app notifications**

### Business Analytics
- **Sales dashboards** with real-time metrics
- **Revenue tracking** and trend analysis
- **Customer analytics** and behavior insights
- **Inventory reports** and alerts

## 🛠 Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Daisy UI** - Component library
- **Framer Motion** - Animations and transitions
- **React Query** - Data fetching and caching
- **Socket.io** - Real-time communication

### Backend
- **Frappe Framework** - Python web framework
- **ERPNext** - Business management system
- **MariaDB/MySQL** - Database
- **Redis** - Caching and sessions
- **Celery** - Background tasks

## 📋 Prerequisites

- Python 3.8+
- Node.js 18+
- MariaDB/MySQL 10.3+
- Redis 6+
- Git

## 🚀 Installation

### Backend Setup (Frappe/ERPNext)

1. **Install Frappe Bench**
```bash
pip install frappe-bench
```

2. **Create a new site**
```bash
bench new-site ecommerce.local
bench use ecommerce.local
```

3. **Install ERPNext**
```bash
bench get-app erpnext
bench install-app erpnext
```

4. **Install Ecommerce App**
```bash
cd apps
git clone <repository-url> ecommerce
cd ..
bench install-app ecommerce
```

5. **Start the development server**
```bash
bench start
```

### Frontend Setup (Next.js)

1. **Install dependencies**
```bash
npm install
```

2. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local`:
```env
FRAPPE_URL=http://localhost:8000
MAPBOX_ACCESS_TOKEN=your_mapbox_token
STRIPE_PUBLISHABLE_KEY=your_stripe_key
PAYPAL_CLIENT_ID=your_paypal_client_id
```

3. **Start the development server**
```bash
npm run dev
```

## 🏗 Project Structure

### Backend Structure
```
apps/ecommerce/
├── ecommerce/
│   ├── ecommerce/
│   │   ├── doctype/
│   │   │   ├── ecommerce_user/
│   │   │   ├── product/
│   │   │   ├── product_category/
│   │   │   ├── shopping_cart/
│   │   │   ├── ecommerce_order/
│   │   │   ├── product_review/
│   │   │   ├── chat_message/
│   │   │   └── business_dashboard/
│   │   ├── api/
│   │   │   ├── auth.py
│   │   │   ├── user.py
│   │   │   └── order.py
│   │   └── tasks.py
│   ├── hooks.py
│   └── modules.txt
```

### Frontend Structure
```
src/
├── components/
│   ├── layout/
│   ├── ui/
│   ├── product/
│   ├── cart/
│   ├── order/
│   ├── user/
│   └── dashboard/
├── pages/
│   ├── api/
│   ├── auth/
│   ├── products/
│   ├── orders/
│   └── dashboard/
├── context/
│   ├── AuthContext.tsx
│   └── CartContext.tsx
├── hooks/
├── lib/
│   └── api.ts
├── types/
│   └── index.ts
└── utils/
    ├── constants.ts
    └── helpers.ts
```

## 🔧 Configuration

### Frappe Configuration

1. **Enable CORS** in `site_config.json`:
```json
{
  "allow_cors": "*",
  "cors_headers": ["Content-Type", "Authorization"]
}
```

2. **Configure email** for notifications:
```json
{
  "mail_server": "smtp.gmail.com",
  "mail_port": 587,
  "use_tls": 1,
  "mail_login": "<EMAIL>",
  "mail_password": "your-app-password"
}
```

### Next.js Configuration

The `next.config.js` file includes:
- API proxy to Frappe backend
- Image optimization settings
- Environment variable configuration

## 📱 Key Features Implementation

### User Authentication
- JWT-based authentication with Frappe
- Role-based access control
- Email verification and password reset

### Real-time Features
- WebSocket connections for chat
- Live order tracking updates
- Real-time inventory updates

### Payment Integration
- Multiple payment gateways
- Mobile money integration (M-Pesa, Tigo Pesa)
- Secure payment processing

### Mobile Optimization
- Responsive design with Tailwind CSS
- Progressive Web App (PWA) capabilities
- Touch-friendly interface

## 🧪 Testing

### Backend Tests
```bash
bench run-tests --app ecommerce
```

### Frontend Tests
```bash
npm test
npm run test:e2e
```

## 🚀 Deployment

### Production Deployment

1. **Backend (Frappe)**
```bash
bench setup production
sudo bench setup nginx
sudo bench setup supervisor
```

2. **Frontend (Next.js)**
```bash
npm run build
npm start
```

### Docker Deployment
```bash
docker-compose up -d
```

## 📊 Monitoring

- **Application logs**: Available in Frappe logs
- **Performance monitoring**: Built-in Frappe profiler
- **Error tracking**: Frappe error log system
- **Analytics**: Custom business dashboard

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Technical Specifications](TECHNICAL_SPECIFICATIONS.md)
- **Issues**: GitHub Issues
- **Community**: Frappe Community Forum

## 🔮 Roadmap

- [ ] AI-powered product recommendations
- [ ] Advanced analytics and reporting
- [ ] Multi-language support
- [ ] Mobile app development
- [ ] Blockchain integration for supply chain
- [ ] AR/VR product visualization

## 📞 Contact

- **Developer**: Emanuel Fidelis
- **Email**: <EMAIL>
- **GitHub**: [@Emmafidelis](https://github.com/Emmafidelis)

---

Built with ❤️ using Frappe Framework and Next.js
