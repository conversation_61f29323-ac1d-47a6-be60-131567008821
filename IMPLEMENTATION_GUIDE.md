# Implementation Guide - E-commerce Application

## Phase 1: Project Setup & Foundation (Weeks 1-4)

### 1.1 Development Environment Setup

#### Frappe/ERPNext Backend Setup
```bash
# Install Frappe Bench
pip3 install frappe-bench

# Create new site
bench new-site ecommerce.local
bench --site ecommerce.local install-app erpnext
bench --site ecommerce.local install-app webshop

# Create custom app for e-commerce extensions
bench new-app ecommerce_platform
bench --site ecommerce.local install-app ecommerce_platform

# Start development server
bench start
```

#### Next.js Frontend Setup
```bash
# Create Next.js application
npx create-next-app@latest ecommerce-frontend --typescript --tailwind --eslint
cd ecommerce-frontend

# Install additional dependencies
npm install @daisyui/react daisyui
npm install @headlessui/react @heroicons/react
npm install zustand axios socket.io-client
npm install @tanstack/react-query
npm install framer-motion
npm install chart.js react-chartjs-2
npm install react-hook-form @hookform/resolvers yup
```

### 1.2 Database Schema Implementation

#### Create Custom DocTypes in Frappe
```python
# apps/ecommerce_platform/ecommerce_platform/ecommerce_platform/doctype/business_profile/business_profile.py

import frappe
from frappe.model.document import Document

class BusinessProfile(Document):
    def validate(self):
        self.validate_nida_number()
        self.validate_business_license()
    
    def validate_nida_number(self):
        if self.business_type in ["Company", "Wholesaler", "Retailer"]:
            if not self.nida_number:
                frappe.throw("NIDA number is required for business accounts")
    
    def validate_business_license(self):
        if self.business_type == "Company":
            if not self.business_license:
                frappe.throw("Business license is required for company accounts")
```

#### Custom User Fields
```python
# apps/ecommerce_platform/ecommerce_platform/fixtures/custom_field.json
[
    {
        "doctype": "Custom Field",
        "dt": "User",
        "fieldname": "user_account_type",
        "label": "Account Type",
        "fieldtype": "Select",
        "options": "Customer\nCompany\nWholesaler\nRetailer\nDelivery Person\nMiddleman",
        "insert_after": "user_type"
    },
    {
        "doctype": "Custom Field",
        "dt": "User",
        "fieldname": "verification_status",
        "label": "Verification Status",
        "fieldtype": "Select",
        "options": "Pending\nVerified\nRejected",
        "default": "Pending",
        "insert_after": "user_account_type"
    }
]
```

### 1.3 API Layer Development

#### Frappe API Controllers
```python
# apps/ecommerce_platform/ecommerce_platform/api/auth.py

import frappe
from frappe import _
from frappe.utils import cint
import jwt
from datetime import datetime, timedelta

@frappe.whitelist(allow_guest=True)
def login(usr, pwd):
    """Enhanced login with user type detection"""
    try:
        login_manager = frappe.auth.LoginManager()
        login_manager.authenticate(user=usr, pwd=pwd)
        login_manager.post_login()
        
        user_doc = frappe.get_doc("User", usr)
        
        # Generate JWT token
        token = generate_jwt_token(usr, user_doc.user_account_type)
        
        return {
            "message": "Logged in successfully",
            "user": {
                "name": user_doc.name,
                "full_name": user_doc.full_name,
                "user_type": user_doc.user_account_type,
                "verification_status": user_doc.verification_status
            },
            "token": token
        }
    except Exception as e:
        frappe.throw(_("Invalid login credentials"))

def generate_jwt_token(user_id, user_type):
    """Generate JWT token for API authentication"""
    payload = {
        'user_id': user_id,
        'user_type': user_type,
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    return jwt.encode(payload, frappe.conf.secret_key, algorithm='HS256')

@frappe.whitelist()
def get_user_profile():
    """Get current user profile with business details"""
    user = frappe.session.user
    user_doc = frappe.get_doc("User", user)
    
    profile_data = {
        "user": user_doc.as_dict(),
        "business_profile": None
    }
    
    # Get business profile if exists
    if user_doc.user_account_type in ["Company", "Wholesaler", "Retailer"]:
        business_profile = frappe.db.get_value("Business Profile", 
                                             {"user": user}, "*", as_dict=True)
        if business_profile:
            profile_data["business_profile"] = business_profile
    
    return profile_data
```

#### Product Search API
```python
# apps/ecommerce_platform/ecommerce_platform/api/products.py

import frappe
from frappe.utils import cint, flt
import json

@frappe.whitelist(allow_guest=True)
def search_products(query="", filters=None, page=1, page_size=20):
    """Enhanced product search with AI recommendations"""
    
    if filters:
        filters = json.loads(filters) if isinstance(filters, str) else filters
    else:
        filters = {}
    
    # Build search conditions
    conditions = ["published = 1"]
    
    if query:
        conditions.append(f"(web_item_name LIKE '%{query}%' OR item_name LIKE '%{query}%')")
    
    # Apply filters
    if filters.get("category"):
        conditions.append(f"item_group = '{filters['category']}'")
    
    if filters.get("min_price"):
        conditions.append(f"price >= {flt(filters['min_price'])}")
    
    if filters.get("max_price"):
        conditions.append(f"price <= {flt(filters['max_price'])}")
    
    # Calculate pagination
    start = (cint(page) - 1) * cint(page_size)
    
    # Execute query
    products = frappe.db.sql(f"""
        SELECT 
            name, web_item_name, item_name, item_code,
            website_image, route, price, item_group,
            short_description, web_long_description
        FROM `tabWebsite Item`
        WHERE {' AND '.join(conditions)}
        ORDER BY ranking DESC, modified DESC
        LIMIT {start}, {page_size}
    """, as_dict=True)
    
    # Get total count
    total_count = frappe.db.sql(f"""
        SELECT COUNT(*) as count
        FROM `tabWebsite Item`
        WHERE {' AND '.join(conditions)}
    """, as_dict=True)[0].count
    
    return {
        "products": products,
        "total_count": total_count,
        "page": cint(page),
        "page_size": cint(page_size),
        "total_pages": (total_count + cint(page_size) - 1) // cint(page_size)
    }

@frappe.whitelist()
def get_recommendations(user_id=None, product_id=None, limit=10):
    """Get AI-powered product recommendations"""
    
    if not user_id:
        user_id = frappe.session.user
    
    # Simple recommendation logic (to be enhanced with ML)
    if product_id:
        # Get similar products based on category
        product = frappe.get_doc("Website Item", product_id)
        similar_products = frappe.db.sql("""
            SELECT name, web_item_name, website_image, route, price
            FROM `tabWebsite Item`
            WHERE item_group = %s AND name != %s AND published = 1
            ORDER BY ranking DESC
            LIMIT %s
        """, (product.item_group, product_id, limit), as_dict=True)
        
        return {"recommendations": similar_products}
    
    else:
        # Get trending products for user
        trending_products = frappe.db.sql("""
            SELECT name, web_item_name, website_image, route, price
            FROM `tabWebsite Item`
            WHERE published = 1
            ORDER BY ranking DESC, modified DESC
            LIMIT %s
        """, (limit,), as_dict=True)
        
        return {"recommendations": trending_products}
```

## Phase 2: Frontend Development (Weeks 5-8)

### 2.1 Next.js Project Structure Setup

#### Configure Tailwind with DaisyUI
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'bolt-green': '#00D632',
        'bolt-dark': '#1A1A1A',
        'bolt-gray': '#F5F5F5',
      },
      fontFamily: {
        'bolt': ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        bolt: {
          "primary": "#00D632",
          "secondary": "#1A1A1A",
          "accent": "#37CDBE",
          "neutral": "#3D4451",
          "base-100": "#FFFFFF",
          "info": "#3ABFF8",
          "success": "#36D399",
          "warning": "#FBBD23",
          "error": "#F87272",
        },
      },
    ],
  },
}
```

#### State Management with Zustand
```typescript
// src/store/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  name: string;
  full_name: string;
  user_type: string;
  verification_status: string;
}

interface AuthStore {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: async (credentials) => {
        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(credentials),
          });
          
          const data = await response.json();
          
          if (data.token) {
            set({
              user: data.user,
              token: data.token,
              isAuthenticated: true,
            });
          }
        } catch (error) {
          throw new Error('Login failed');
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
      },
      
      setUser: (user) => set({ user }),
    }),
    {
      name: 'auth-storage',
    }
  )
);
```

#### Product Store
```typescript
// src/store/productStore.ts
import { create } from 'zustand';

interface Product {
  name: string;
  web_item_name: string;
  item_name: string;
  website_image: string;
  price: number;
  route: string;
  item_group: string;
}

interface ProductStore {
  products: Product[];
  loading: boolean;
  searchQuery: string;
  filters: Record<string, any>;
  searchProducts: (query: string, filters?: Record<string, any>) => Promise<void>;
  setFilters: (filters: Record<string, any>) => void;
  clearFilters: () => void;
}

export const useProductStore = create<ProductStore>((set, get) => ({
  products: [],
  loading: false,
  searchQuery: '',
  filters: {},
  
  searchProducts: async (query, filters = {}) => {
    set({ loading: true, searchQuery: query });
    
    try {
      const params = new URLSearchParams({
        query,
        filters: JSON.stringify(filters),
      });
      
      const response = await fetch(`/api/products/search?${params}`);
      const data = await response.json();
      
      set({ products: data.products, loading: false });
    } catch (error) {
      set({ loading: false });
      console.error('Search failed:', error);
    }
  },
  
  setFilters: (filters) => set({ filters }),
  clearFilters: () => set({ filters: {} }),
}));
```

### 2.2 Core Components Development

#### Bolt-Inspired Product Card
```typescript
// src/components/ProductCard.tsx
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { HeartIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';

interface ProductCardProps {
  product: Product;
  variant?: 'grid' | 'list';
  onAddToCart?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  variant = 'grid',
  onAddToCart,
  onAddToWishlist,
}) => {
  return (
    <motion.div
      whileHover={{ y: -4 }}
      className={`card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 ${
        variant === 'list' ? 'card-side' : ''
      }`}
    >
      <figure className={variant === 'list' ? 'w-48' : 'h-48'}>
        <Image
          src={product.website_image || '/placeholder-product.jpg'}
          alt={product.web_item_name}
          width={variant === 'list' ? 192 : 300}
          height={192}
          className="object-cover w-full h-full"
        />
      </figure>
      
      <div className="card-body">
        <h3 className="card-title text-sm font-medium line-clamp-2">
          {product.web_item_name}
        </h3>
        
        <p className="text-xs text-gray-500 line-clamp-2">
          {product.short_description}
        </p>
        
        <div className="flex items-center justify-between mt-2">
          <span className="text-lg font-bold text-primary">
            ${product.price}
          </span>
          
          <div className="flex gap-2">
            <button
              onClick={() => onAddToWishlist?.(product)}
              className="btn btn-ghost btn-sm btn-circle"
            >
              <HeartIcon className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => onAddToCart?.(product)}
              className="btn btn-primary btn-sm"
            >
              <ShoppingCartIcon className="w-4 h-4" />
              Add
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
```

#### Search Component with Filters
```typescript
// src/components/SearchBar.tsx
import { useState } from 'react';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { useProductStore } from '@/store/productStore';

export const SearchBar: React.FC = () => {
  const [query, setQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const { searchProducts, filters, setFilters } = useProductStore();

  const handleSearch = () => {
    searchProducts(query, filters);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Search Input */}
      <div className="flex gap-2 mb-4">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Search products, brands, categories..."
            className="input input-bordered w-full pl-10"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
        
        <button
          onClick={handleSearch}
          className="btn btn-primary"
        >
          Search
        </button>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="btn btn-outline"
        >
          <FunnelIcon className="w-5 h-5" />
        </button>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="bg-base-200 p-4 rounded-lg mb-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="label">
                <span className="label-text">Category</span>
              </label>
              <select
                className="select select-bordered w-full"
                onChange={(e) => setFilters({ ...filters, category: e.target.value })}
              >
                <option value="">All Categories</option>
                <option value="Electronics">Electronics</option>
                <option value="Clothing">Clothing</option>
                <option value="Home">Home & Garden</option>
              </select>
            </div>
            
            <div>
              <label className="label">
                <span className="label-text">Price Range</span>
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min"
                  className="input input-bordered flex-1"
                  onChange={(e) => setFilters({ ...filters, min_price: e.target.value })}
                />
                <input
                  type="number"
                  placeholder="Max"
                  className="input input-bordered flex-1"
                  onChange={(e) => setFilters({ ...filters, max_price: e.target.value })}
                />
              </div>
            </div>
            
            <div>
              <label className="label">
                <span className="label-text">Sort By</span>
              </label>
              <select
                className="select select-bordered w-full"
                onChange={(e) => setFilters({ ...filters, sort: e.target.value })}
              >
                <option value="relevance">Relevance</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
                <option value="rating">Customer Rating</option>
              </select>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
```

## Phase 3: Bolt-Inspired Features Implementation (Weeks 9-12)

### 3.1 Real-time Delivery Tracking (Bolt-style)

#### Delivery Tracking Component
```typescript
// src/components/DeliveryTracking.tsx
import { useEffect, useState } from 'react';
import { GoogleMap, Marker, DirectionsRenderer } from '@react-google-maps/api';
import { motion } from 'framer-motion';

interface DeliveryTrackingProps {
  orderId: string;
  deliveryPersonLocation: { lat: number; lng: number };
  customerLocation: { lat: number; lng: number };
  estimatedArrival: string;
}

export const DeliveryTracking: React.FC<DeliveryTrackingProps> = ({
  orderId,
  deliveryPersonLocation,
  customerLocation,
  estimatedArrival,
}) => {
  const [directions, setDirections] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(deliveryPersonLocation);

  useEffect(() => {
    // Real-time location updates via WebSocket
    const socket = io();
    socket.on(`delivery_update_${orderId}`, (data) => {
      setCurrentLocation(data.location);
    });

    return () => socket.disconnect();
  }, [orderId]);

  return (
    <div className="h-screen flex flex-col">
      {/* Header with ETA */}
      <div className="bg-primary text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-bold">Your order is on the way!</h2>
            <p className="text-sm opacity-90">Estimated arrival: {estimatedArrival}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">12 min</div>
            <div className="text-xs">away</div>
          </div>
        </div>
      </div>

      {/* Map */}
      <div className="flex-1">
        <GoogleMap
          mapContainerStyle={{ width: '100%', height: '100%' }}
          center={currentLocation}
          zoom={15}
        >
          <Marker
            position={currentLocation}
            icon={{
              url: '/delivery-icon.png',
              scaledSize: new google.maps.Size(40, 40),
            }}
          />
          <Marker
            position={customerLocation}
            icon={{
              url: '/home-icon.png',
              scaledSize: new google.maps.Size(30, 30),
            }}
          />
          {directions && <DirectionsRenderer directions={directions} />}
        </GoogleMap>
      </div>

      {/* Bottom Panel */}
      <div className="bg-white p-4 border-t">
        <div className="flex items-center gap-3 mb-3">
          <div className="avatar">
            <div className="w-12 rounded-full">
              <img src="/delivery-person.jpg" alt="Delivery Person" />
            </div>
          </div>
          <div>
            <div className="font-medium">John Doe</div>
            <div className="text-sm text-gray-500">Delivery Partner</div>
          </div>
          <div className="ml-auto flex gap-2">
            <button className="btn btn-outline btn-sm">Message</button>
            <button className="btn btn-outline btn-sm">Call</button>
          </div>
        </div>

        <div className="text-center">
          <button className="btn btn-primary btn-wide">
            Share Live Location
          </button>
        </div>
      </div>
    </div>
  );
};
```

### 3.2 Bottom Navigation (Bolt-style)

```typescript
// src/components/BottomNavigation.tsx
import { useRouter } from 'next/router';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  ShoppingBagIcon,
  ChatBubbleLeftIcon,
  UserIcon,
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as SearchIconSolid,
  ShoppingBagIcon as BagIconSolid,
  ChatBubbleLeftIcon as ChatIconSolid,
  UserIcon as UserIconSolid,
} from '@heroicons/react/24/solid';

export const BottomNavigation: React.FC = () => {
  const router = useRouter();
  const currentPath = router.pathname;

  const navItems = [
    {
      name: 'Home',
      path: '/',
      icon: HomeIcon,
      activeIcon: HomeIconSolid,
    },
    {
      name: 'Search',
      path: '/search',
      icon: MagnifyingGlassIcon,
      activeIcon: SearchIconSolid,
    },
    {
      name: 'Orders',
      path: '/orders',
      icon: ShoppingBagIcon,
      activeIcon: BagIconSolid,
    },
    {
      name: 'Chat',
      path: '/chat',
      icon: ChatBubbleLeftIcon,
      activeIcon: ChatIconSolid,
    },
    {
      name: 'Profile',
      path: '/profile',
      icon: UserIcon,
      activeIcon: UserIconSolid,
    },
  ];

  return (
    <div className="btm-nav btm-nav-lg bg-white border-t">
      {navItems.map((item) => {
        const isActive = currentPath === item.path;
        const IconComponent = isActive ? item.activeIcon : item.icon;

        return (
          <button
            key={item.name}
            onClick={() => router.push(item.path)}
            className={`${isActive ? 'active text-primary' : 'text-gray-500'}`}
          >
            <IconComponent className="w-5 h-5" />
            <span className="btm-nav-label text-xs">{item.name}</span>
          </button>
        );
      })}
    </div>
  );
};
```

### 3.3 Smooth Animations & Transitions

```typescript
// src/components/PageTransition.tsx
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';

const pageVariants = {
  initial: {
    opacity: 0,
    x: -20,
  },
  in: {
    opacity: 1,
    x: 0,
  },
  out: {
    opacity: 0,
    x: 20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
};

export const PageTransition: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={router.route}
        initial="initial"
        animate="in"
        exit="out"
        variants={pageVariants}
        transition={pageTransition}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};
```

### 3.4 Real-time Notifications

```typescript
// src/components/NotificationSystem.tsx
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

export const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    const socket = io();

    socket.on('notification', (notification: Notification) => {
      setNotifications(prev => [...prev, { ...notification, id: Date.now().toString() }]);

      // Auto remove after duration
      if (notification.duration !== 0) {
        setTimeout(() => {
          removeNotification(notification.id);
        }, notification.duration || 5000);
      }
    });

    return () => socket.disconnect();
  }, []);

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`alert max-w-sm ${
              notification.type === 'success' ? 'alert-success' :
              notification.type === 'error' ? 'alert-error' :
              notification.type === 'warning' ? 'alert-warning' :
              'alert-info'
            }`}
          >
            <div className="flex-1">
              <h4 className="font-medium">{notification.title}</h4>
              <p className="text-sm">{notification.message}</p>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="btn btn-ghost btn-sm btn-circle"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};
```

This implementation guide provides a solid foundation for building the e-commerce application with the specified requirements and Bolt-inspired design.
