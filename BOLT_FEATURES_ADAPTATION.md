# Bolt App Features Adaptation for E-commerce

## Overview
This document outlines how to adapt Bolt's successful UX/UI patterns and features for our e-commerce platform, maintaining the clean, intuitive design while adding e-commerce specific functionality.

## 1. Core Bolt Features to Adapt

### 1.1 Real-time Tracking System
**Bolt Feature**: Live ride tracking with ETA updates
**E-commerce Adaptation**: Live order and delivery tracking

#### Implementation Strategy
```typescript
// Real-time Order Tracking Component
interface OrderTrackingProps {
  orderId: string;
  status: 'confirmed' | 'preparing' | 'picked_up' | 'in_transit' | 'delivered';
  estimatedDelivery: Date;
  deliveryLocation: { lat: number; lng: number };
}

const OrderTracking: React.FC<OrderTrackingProps> = ({
  orderId,
  status,
  estimatedDelivery,
  deliveryLocation
}) => {
  const [currentStatus, setCurrentStatus] = useState(status);
  const [liveLocation, setLiveLocation] = useState(deliveryLocation);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const socket = io();
    socket.on(`order_${orderId}_update`, (data) => {
      setCurrentStatus(data.status);
      setLiveLocation(data.location);
    });
    
    return () => socket.disconnect();
  }, [orderId]);

  return (
    <div className="tracking-container">
      {/* Status Timeline */}
      <div className="status-timeline">
        {statusSteps.map((step, index) => (
          <StatusStep 
            key={step.id}
            step={step}
            isActive={getStepIndex(currentStatus) >= index}
            isCompleted={getStepIndex(currentStatus) > index}
          />
        ))}
      </div>
      
      {/* Live Map */}
      {currentStatus === 'in_transit' && (
        <LiveDeliveryMap 
          deliveryLocation={liveLocation}
          customerLocation={customerLocation}
          estimatedArrival={estimatedDelivery}
        />
      )}
    </div>
  );
};
```

### 1.2 Bottom Navigation Pattern
**Bolt Feature**: Clean bottom navigation with 5 main sections
**E-commerce Adaptation**: Home, Search, Cart, Orders, Profile

#### Design Specifications
```css
/* Bolt-inspired bottom navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: white;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1000;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  min-width: 60px;
  transition: all 0.2s ease;
}

.nav-item.active {
  color: #00D632; /* Bolt green */
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.nav-label {
  font-size: 10px;
  font-weight: 500;
}
```

### 1.3 Smooth Animations & Micro-interactions
**Bolt Feature**: Fluid animations and responsive feedback
**E-commerce Adaptation**: Product interactions, cart animations, loading states

#### Animation Library Setup
```typescript
// Animation utilities inspired by Bolt
export const boltAnimations = {
  // Page transitions
  pageSlide: {
    initial: { x: 300, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -300, opacity: 0 },
    transition: { type: "spring", stiffness: 300, damping: 30 }
  },
  
  // Card hover effects
  cardHover: {
    whileHover: { 
      y: -4, 
      scale: 1.02,
      boxShadow: "0 10px 25px rgba(0,0,0,0.1)"
    },
    transition: { type: "spring", stiffness: 400, damping: 17 }
  },
  
  // Button press feedback
  buttonPress: {
    whileTap: { scale: 0.95 },
    transition: { type: "spring", stiffness: 400, damping: 17 }
  },
  
  // Loading states
  pulse: {
    animate: {
      scale: [1, 1.05, 1],
      opacity: [0.7, 1, 0.7]
    },
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};
```

### 1.4 Minimalist Design Language
**Bolt Feature**: Clean, uncluttered interface with strategic use of white space
**E-commerce Adaptation**: Product-focused design with clear hierarchy

#### Design System
```typescript
// Bolt-inspired design tokens
export const designTokens = {
  colors: {
    primary: '#00D632',      // Bolt green
    secondary: '#1A1A1A',    // Dark gray
    background: '#FFFFFF',   // Pure white
    surface: '#F8F9FA',      // Light gray
    text: {
      primary: '#1A1A1A',
      secondary: '#6B7280',
      disabled: '#9CA3AF'
    },
    accent: {
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6'
    }
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px'
  },
  
  typography: {
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    sizes: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '30px'
    },
    weights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  },
  
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    full: '9999px'
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
  }
};
```

## 2. E-commerce Specific Adaptations

### 2.1 Product Discovery (Inspired by Bolt's Location Selection)
**Bolt Feature**: Map-based location selection with search
**E-commerce Adaptation**: Visual product discovery with category mapping

```typescript
// Visual Product Discovery Component
const ProductDiscovery: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [viewMode, setViewMode] = useState<'map' | 'grid' | 'list'>('grid');

  return (
    <div className="product-discovery">
      {/* Category Map View */}
      {viewMode === 'map' && (
        <div className="category-map">
          <InteractiveMap
            categories={productCategories}
            onCategorySelect={setSelectedCategory}
            style="bolt-minimal"
          />
        </div>
      )}
      
      {/* Grid/List View */}
      <div className={`products-grid ${viewMode}`}>
        <ProductGrid 
          products={filteredProducts}
          layout={viewMode}
          animations={boltAnimations}
        />
      </div>
      
      {/* View Toggle (Bolt-style) */}
      <div className="view-toggle">
        <SegmentedControl
          options={[
            { value: 'map', icon: MapIcon },
            { value: 'grid', icon: GridIcon },
            { value: 'list', icon: ListIcon }
          ]}
          value={viewMode}
          onChange={setViewMode}
        />
      </div>
    </div>
  );
};
```

### 2.2 Quick Actions (Inspired by Bolt's Ride Options)
**Bolt Feature**: Quick ride type selection with pricing
**E-commerce Adaptation**: Quick purchase options and delivery preferences

```typescript
// Quick Purchase Options Component
const QuickPurchaseOptions: React.FC<{ product: Product }> = ({ product }) => {
  const purchaseOptions = [
    {
      id: 'standard',
      title: 'Standard Delivery',
      subtitle: '3-5 business days',
      price: product.price,
      icon: TruckIcon,
      color: 'gray'
    },
    {
      id: 'express',
      title: 'Express Delivery',
      subtitle: 'Next day delivery',
      price: product.price + 5,
      icon: BoltIcon,
      color: 'primary',
      popular: true
    },
    {
      id: 'pickup',
      title: 'Store Pickup',
      subtitle: 'Ready in 2 hours',
      price: product.price - 2,
      icon: BuildingStorefrontIcon,
      color: 'green'
    }
  ];

  return (
    <div className="quick-purchase-options">
      <h3 className="text-lg font-semibold mb-4">Choose delivery option</h3>
      
      <div className="space-y-3">
        {purchaseOptions.map((option) => (
          <motion.div
            key={option.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`option-card ${option.popular ? 'popular' : ''}`}
          >
            <div className="flex items-center justify-between p-4 border rounded-lg cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className={`icon-container ${option.color}`}>
                  <option.icon className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium">{option.title}</div>
                  <div className="text-sm text-gray-500">{option.subtitle}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold">${option.price}</div>
                {option.popular && (
                  <div className="text-xs text-primary">Most popular</div>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};
```

### 2.3 Social Proof Integration (Inspired by Bolt's Driver Ratings)
**Bolt Feature**: Driver ratings and reviews
**E-commerce Adaptation**: Seller ratings, product reviews, and social validation

```typescript
// Social Proof Component
const SocialProof: React.FC<{ product: Product; seller: Seller }> = ({ 
  product, 
  seller 
}) => {
  return (
    <div className="social-proof">
      {/* Product Rating */}
      <div className="rating-section">
        <div className="flex items-center space-x-2">
          <StarRating rating={product.averageRating} size="sm" />
          <span className="font-medium">{product.averageRating}</span>
          <span className="text-gray-500">({product.reviewCount} reviews)</span>
        </div>
      </div>
      
      {/* Seller Trust Indicators */}
      <div className="seller-trust">
        <div className="flex items-center space-x-2 text-sm">
          <CheckBadgeIcon className="w-4 h-4 text-green-500" />
          <span>Verified Seller</span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <TruckIcon className="w-4 h-4 text-blue-500" />
          <span>Fast Shipping</span>
        </div>
      </div>
      
      {/* Recent Activity */}
      <div className="recent-activity">
        <div className="text-sm text-gray-600">
          <span className="font-medium">23 people</span> bought this in the last 24 hours
        </div>
      </div>
      
      {/* Customer Photos */}
      <div className="customer-photos">
        <div className="text-sm font-medium mb-2">Customer Photos</div>
        <div className="flex space-x-2">
          {product.customerPhotos?.slice(0, 4).map((photo, index) => (
            <img
              key={index}
              src={photo.url}
              alt="Customer photo"
              className="w-12 h-12 rounded-lg object-cover"
            />
          ))}
          {product.customerPhotos?.length > 4 && (
            <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center text-xs">
              +{product.customerPhotos.length - 4}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

## 3. Performance Optimizations (Bolt-inspired)

### 3.1 Lazy Loading & Code Splitting
```typescript
// Implement Bolt-style performance optimizations
const LazyProductGrid = lazy(() => import('./ProductGrid'));
const LazyCheckout = lazy(() => import('./Checkout'));

// Route-based code splitting
const routes = [
  {
    path: '/',
    component: lazy(() => import('../pages/Home'))
  },
  {
    path: '/products',
    component: lazy(() => import('../pages/Products'))
  },
  {
    path: '/checkout',
    component: lazy(() => import('../pages/Checkout'))
  }
];
```

### 3.2 Optimistic UI Updates
```typescript
// Bolt-style optimistic updates for better UX
const useOptimisticCart = () => {
  const [cart, setCart] = useState([]);
  
  const addToCart = async (product: Product) => {
    // Optimistically update UI
    setCart(prev => [...prev, product]);
    
    try {
      await api.cart.add(product);
    } catch (error) {
      // Revert on error
      setCart(prev => prev.filter(item => item.id !== product.id));
      showErrorNotification('Failed to add to cart');
    }
  };
  
  return { cart, addToCart };
};
```

This adaptation guide ensures that the e-commerce platform maintains Bolt's excellent UX principles while providing comprehensive e-commerce functionality.
