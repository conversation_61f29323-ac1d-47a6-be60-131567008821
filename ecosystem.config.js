module.exports = {
  apps: [
    {
      name: 'ecommerce-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/ecommerce-frontend',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        FRAPPE_URL: 'http://localhost:8000'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        FRAPPE_URL: 'https://api.yourdomain.com'
      },
      error_file: '/var/log/pm2/ecommerce-frontend-error.log',
      out_file: '/var/log/pm2/ecommerce-frontend-out.log',
      log_file: '/var/log/pm2/ecommerce-frontend.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max_old_space_size=1024',
      watch: false,
      ignore_watch: ['node_modules', '.next'],
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
